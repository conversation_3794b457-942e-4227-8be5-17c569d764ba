/***************************************************
 * Created by nany<PERSON>ing<PERSON> on 2020/4/14 17:53. *
 ***************************************************/
const fs = require('fs')
const path = require('path')
const jszippackage = require('jszip/package.json')
const k = jszippackage.browser['.'] || jszippackage.browser['./index.js']
delete jszippackage.browser['.']
jszippackage.browser['./index.js'] = k
const text = JSON.stringify(jszippackage, null, 4)
const ps = require.resolve('jszip/package.json')
fs.writeFileSync(ps, text)
