/***************************************************
 * Created by nany<PERSON>ingfeng on 2019-07-04 16:52. *
 ***************************************************/
const config = require("whispered-build/webpack.plugin.dev.config");
const fs = require('fs');

require("@ekuaibao/vendor-lodash/patch")(config);
require("@ekuaibao/vendor-common/patch")(config);
require("@ekuaibao/vendor-whispered/patch")(config);
require("@ekuaibao/vendor-antd/patch")(config);
if (fs.existsSync(__dirname + '/webpack.local.js')) {
  // webpack.local.js可以复制一份webpack.local.example.js
  require('./webpack.local')(config)
}
module.exports = config.toConfig();
