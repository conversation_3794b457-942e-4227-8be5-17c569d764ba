{"name": "@ekuaibao/plugin-web-settlement-checking-domain", "version": "1.11.1-release.4", "author": "", "scripts": {"plugin": "node ./scripts/auto-pull-plugin.js", "fix-jszip-issue": "node ./scripts/fix-jszip-issue.js", "lint": "eslint --ext .tsx,.ts --fix ./src", "build:pre": "node ./scripts/build-domain-entry.js", "start": "npm run build:pre && npm run dev", "dev": "NODE_ENV=development webpack-dev-server --progress --color --config webpack.config.dev.js", "dev:window": "cross-env NODE_ENV=development webpack-dev-server --progress --color --config webpack.config.dev.js", "clean": "<PERSON><PERSON><PERSON> build", "build": "run-s clean build:pre build:src", "upload_plugin_to_cdn": "upload_plugin_to_cdn build", "upload_plugin_to_minio": "upload_plugin_to_minio build", "upload_plugin_to_mfe": "upload_plugin_to_mfe build", "build:src": "ANALYZE_PROD=true NODE_ENV=production webpack --progress --color -p --config webpack.config.pro.js", "test": "jest", "push_plugin": "node ./scripts/push-back-plugin.js"}, "devDependencies": {"@hose/eui": "3.0.52", "@types/jest": "^24.0.11", "@types/loadable__component": "^5.10.0", "@types/node": "^12.0.2", "@types/react": "^16.8.18", "cross-env": "^7.0.2", "jest": "^24.7.1", "lint-staged": "^8.1.5", "npm-run-all": "^4.1.5", "npmlog": "^7.0.1", "prettier": "^2.5.1", "ts-jest": "^24.0.2", "upload_to_cdn": "^1.2.8", "whispered-build": "3.3.13-beta.0"}, "dependencies": {"@babel/runtime": "7.18.3", "@ekuaibao/collection-definition": "1.0.6-release.4", "@ekuaibao/datagrid": "1.9.0", "@ekuaibao/ekuaibao_types": "^1.0.56", "@ekuaibao/enhance-layer-manager": "^5.5.1-WN.5", "@ekuaibao/enhance-stacker-manager": "^3.1.3", "@ekuaibao/eui": "*", "@ekuaibao/eui-styles": "^2.1.0", "@ekuaibao/eui-web": "^1.0.0-release", "@ekuaibao/helpers": "^1.1.9", "@ekuaibao/keel": "^1.1.1", "@ekuaibao/lib": "2.8.23", "@ekuaibao/loading": "^4.0.1", "@ekuaibao/rpc": "2.2.0-beta.0", "@ekuaibao/template": "^5.0.7", "@ekuaibao/uploader": "3.1.49", "@ekuaibao/vendor-antd": "3.9.9", "@ekuaibao/vendor-common": "^1.1.0", "@ekuaibao/vendor-lodash": "^4.17.2", "@ekuaibao/vendor-whispered": "^4.5.2-release.2", "@hose/eui-icons": "3.0.15", "antd": "^3.8.4", "core-js": "^3.45.1", "ekbc-query-builder": "^2.0.2", "mobx-react-lite": "^2.2.2", "moment": "2.22.2", "number-format.js": "1.1.11", "react-copy-to-clipboard": "^5.0.4", "tslib": "^1", "victory": "36.6.8"}, "publishConfig": {"registry": "https://npm.ekuaibao.com/"}, "license": "UNLICENSED", "xhusky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"linters": {"*.{js,jsx,ts,tsx,json,css,less,scss,md}": ["prettier --write", "git add"]}, "ignore": ["**/assets/**/*"]}, "jest": {"moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node", "mjs"], "collectCoverage": true, "collectCoverageFrom": ["<rootDir>/src/**/*.{ts,tsx}", "!**/*.d.ts"], "coverageDirectory": "temp/coverage", "testMatch": ["<rootDir>/src/**/*.spec.{ts,tsx}"], "transform": {"^.+\\.tsx?$": "ts-jest"}}, "description": "对账结算"}