# 说明：
该项目为聚合多个 plugin 项目为一个业务域的主项目，为了规避一些后续的维护问题，
基于约定大于配置，特做以下约定：

- src 为所有源代码目录；
- 该项目中的package.json中的 devDependencies 与 dependencies 请手动更新引入的所有子包的package.json中依赖的集合， 保留需要的版本，后续改为自动合并。
- 所有合并到该项目的子plugin位置在 src 目录，目录名字为原 plugin 的名字，例如： plugin-web-loan-manage, 则目录为 src/loan-manage;
- 所有聚合进来的子项目禁止破坏删除原有应用中的配置及环境文件；如package.json，webpack等文件；
- 新增自定义模块，目录结构与其他子项目保持一致；如新增模块，new-module，入口文件为 src/new-module/src/index.ts,
- 禁止 src 下各个子项目使用相对路径互相访问资源，（假设后续子项目仍有独立使用场景）。

## 初始化项目：
- 配置需要拉取的plugins.js;
- 运行 npm run plugin; 

## 该项目与子项目的带你同步操作：

由于操作命令过长，git地址每次输入比较麻烦，可以放到 git remote 里来管理：
```shell
git remote add <目录名> <git地址>

举例：git remote add loan-manage ****************.com:torpedo/plugin-web-loan-manage.git

则后续操作命令中就可以只写它的名字了：

git subtree pull --prefix=src/loan-manage  loan-manage  master

```

### 新增已存在子项目（plugin）

```shell

git subtree add --prefix=<目录名> <git地址，请使用****************... 格式> <分支名>

举例：git subtree add --prefix=src/loan-manage  ****************.com:torpedo/plugin-web-loan-manage.git  master
```

### pull 已存在plugin 子项目代码变更到该项目

```shell
git subtree pull --prefix=<目录名> <git地址，请使用****************... 格式> <分支名>
```


### push该项目代码变更到已存在plugin 子项目

```shell
git subtree push --prefix=<目录名> <git地址，请使用****************... 格式> <分支名>
```

### --squash 可以将子项目的多个 commit，合并为一个commit 同步到该项目
```shell
git subtree pull --prefix=src/loan-manage loan-manage master --squash
```

疑问？：

#### <strong>以上操作，subtree 是怎么知道哪些 commit 是新的，是属于这个子项目的呢？</strong>

原因是 subtree add 的时候单独生成了一个 commit，
git 会遍历 git log，直到找到这个 commit，然后把之间的 commit 里涉及到那个目录的改动摘出来，单独 push 到子项目。

因为有个遍历 commit 的过程，所以这一步可能会比较慢。
当然也有优化的方式，当 commit 多的时候，你可以执行以下命令，更新一个新的commit 标记，

```shell
git subtree split --prefix=<目录名> --rejoin
```

因为 subtree push 的时候会从上往下找 commit，直到找到这样的 commit 结束。

所以 split 命令就可以指定找到哪个 commit，之前的就不找了，从而优化性能。




## 当前项目结构：

- scripts       // 脚本目录，包括自动生成项目入口
- src           // 源代码入口，所有子项目代码聚合位置
- package.json  // 当前所有项目的依赖版本配置，如依赖缺失，请手动添加，如果新增依赖，建议在包含package.json的子项目中也新增对应依赖。
- webpack.config.dev.js    // 开发配置
- webpack.config.pro.js    // 生产配置

