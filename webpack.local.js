const zlib = require('node:zlib')
// const PROXY_URL= 'https://wx2.ekuaibao.com/',
// const PROXY_URL= 'https://app.ekuaibao.com/',
//const PROXY_URL = 'https://release.ekuaibao.net/'
// const PROXY_URL= 'https://ekuaibao2297.eapps.dingtalkcloud.com/',
 //const PROXY_URL= 'https://hotfix.ekuaibao.net/'
// const PROXY_URL= 'https://bcqa.qa-dev.ekuaibao.net/'
 const PROXY_URL= 'https://release.ekuaibao.net/'
// const PROXY_URL= 'http://127.0.0.1:9990',
// 使用 Nginx 秒级切换前端代理服务器 https://hose2019.feishu.cn/wiki/QASCwP8NnienGbkkhOWc81qvnyg

function createProxyConfig(target, { contentType, sourceText, distText }) {
  return {
    target: target,
    changeOrigin: true,
    cookieDomainRewrite: true,
    selfHandleResponse: true,
    onProxyRes: function onProxyRes(proxyRes, req, res) {
      if (proxyRes.headers['content-type']?.indexOf(contentType) === -1) {
        return
      }

      const body = []
      proxyRes.on('data', function (chunk) {
        body.push(chunk)
      })
      proxyRes.on('end', function () {
        const buffer = Buffer.concat(body)
        let text
        if (proxyRes.headers['content-encoding'] === 'gzip') {
          text = zlib.gunzipSync(buffer).toString('utf8')
        } else {
          text = buffer.toString('utf8')
        }
        if (sourceText && distText) {
          text = text.replace(sourceText, distText)
        }
        res.end(text)
      })
    },
  }
}
module.exports = function (config) {
  const moduleName = config.output.get('publicPath').replace('/assets/', '').replace('/', '')
  const host = '127.0.0.1'
  const port = 7009

  config.devServer.merge({
    port,
    host,
    open: true,
    openPage: 'web/app.html',
    proxy: {
      '/web/app.html': createProxyConfig(PROXY_URL, {
        contentType: 'text/html',
        sourceText: 'https://mfe-server-dev.serverless.ekuaibao.com',
        distText: `http://${host}:${port}`,
      }),
      '/web/debugger.html': createProxyConfig(PROXY_URL, {
        contentType: 'text/html',
        sourceText: 'https://mfe-server-dev.serverless.ekuaibao.com',
        distText: `http://${host}:${port}`,
      }),
      '/web/thirdparty.html': createProxyConfig(PROXY_URL, {
        contentType: 'text/html',
        sourceText: 'https://mfe-server-dev.serverless.ekuaibao.com',
        distText: `http://${host}:${port}`,
      }),
      '/web/': {
        target: PROXY_URL,
        changeOrigin: true,
        cookieDomainRewrite: true,
      },

      '/api/registry/': createProxyConfig('https://mfe-server-dev.serverless.ekuaibao.com/', {
        contentType: 'text/javascript',
        sourceText: new RegExp(
          `https://statics?.ekuaibao.com/ekb/assets/${moduleName}/${moduleName}.[\\d\\S]+.js`,
          'gi',
        ),
        distText: `http://${host}:${port}/assets/${moduleName}/${moduleName}.js`,
      }),

      '/api/': {
        target: PROXY_URL,
        changeOrigin: true,
        cookieDomainRewrite: '',
      },
    },
  })
}