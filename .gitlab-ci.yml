variables:
  GIT_SUBMODULE_STRATEGY: recursive

before_script:
  - git config --global user.name "${GITLAB_USER_NAME}"
  - git config --global user.email "${GITLAB_USER_EMAIL}"

stages:
  - report
  - build

build:
  stage: build
  image: registry-prod.ekuaibao.com/ci/ci-nodejs:14.5.0
  script:
    - npm install
    - npm run build
    - npm publish build
    - npm run upload_plugin_to_cdn
    - npm run upload_plugin_to_minio
    - npm run upload_plugin_to_mfe
  only:
    - /^v?\d+(\.\d+)+[\.\-_\w]*/
  tags:
    - k8s 