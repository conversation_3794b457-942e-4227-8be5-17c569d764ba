{"presets": [["@babel/preset-env", {"modules": false}], "@babel/preset-react"], "env": {"test": {"presets": ["@babel/preset-env", "@babel/preset-react"]}, "lib": {"plugins": [["babel-plugin-import", {"libraryName": "antd", "style": false, "libraryDirectory": "lib"}], "@babel/plugin-transform-modules-commonjs"]}, "esm": {"plugins": [["babel-plugin-import", {"libraryName": "antd", "style": false, "libraryDirectory": "es"}]]}}, "plugins": ["@babel/plugin-external-helpers", "@babel/plugin-transform-runtime", "@babel/plugin-transform-object-assign", "@babel/plugin-syntax-dynamic-import", "@babel/plugin-proposal-async-generator-functions", "@babel/plugin-transform-regenerator", "@babel/plugin-proposal-function-bind", "@babel/plugin-proposal-object-rest-spread", ["@babel/plugin-proposal-decorators", {"legacy": true}], ["@babel/plugin-proposal-class-properties", {"loose": true}], ["@babel/plugin-proposal-private-methods", {"loose": true}], "babel-plugin-lodash"]}