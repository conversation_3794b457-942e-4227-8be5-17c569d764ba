import { QuestionnaireProps } from '@hose/eui/es/components/questionnaire'

type questionnaireType = {
  confirmBill: QuestionnaireProps
  confirmInvoice: QuestionnaireProps
}
let questionnaireConfig: questionnaireType = {
  confirmBill: {
    sid: '5102263416765440',
    channelId: '6200999267649536',
    width: '450px'
  },
  confirmInvoice: {
    sid: '5098533530745856',
    channelId: '6201025633896448',
    width: '400px'
  }
}

// 开发环境
if(process.env.NODE_ENV === 'development' || window.origin.includes('ekb-qa.k8s03.ekuaibao')) {
  questionnaireConfig = {
    confirmBill: {
      sid: '5102263416765440',
      channelId: '6200999267649536',
      width: '450px'
    },
    confirmInvoice: {
      sid: '5098533530745856',
      channelId: '6201025633896448',
      width: '450px'
    }
  }
}

export { questionnaireConfig }
