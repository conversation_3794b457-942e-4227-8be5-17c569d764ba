/*
 * @Description:
 * @Creator: ch<PERSON>can<PERSON><PERSON>
 * @Date: 2021-06-09 11:23:17
 */
import React from 'react';
import { app as api } from '@ekuaibao/whispered';
import { parseFields } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil';
const { parseColumns } = api.require('@elements/DataLinkTable/tableUtil');
import { Fetch } from '@ekuaibao/fetch';
import { stateMap } from '../settlement-checking/utils/data-map';
import { Tooltip } from 'antd';

export function getColumns({ template, path }) {
  const params: any = { res: template };
  const fields: any[] = parseFields(params);
  const paramsFields = { fields: fields, path: path };
  const { columns, fieldMap } = parseColumns(paramsFields);
  return { columns, fieldMap };
}

export async function fnASyncExportAction(params: any, isCheckImage = false) {
  const { taskName } = await api.open('@layout:AsyncExportModal');
  params = {
    ...params,
    taskName,
  };
  const url = `${Fetch.fixOrigin(
    location.origin,
  )}/api/checking/v3/statementDetailExtend/export/async?taskName=${taskName}`;
  await Fetch.POST(url, undefined, { body: params });
}

const accountPeriodState = () => {
  return [
    { label: i18n.get('本期对账'), value: 'currentPeriod' },
    { label: i18n.get('本期不对账'), value: 'nextPeriod' },
  ]
}

const adjustStateState = () => {
  return [
    { value: 'UNADJUSTED', label: i18n.get('未调整') },
    { value: 'ADJUSTING', label: i18n.get('调整中') },
    { value: 'ADJUST_SUCCESS', label: i18n.get('调整成功') },
    { value: 'ADJUST_FAIL', label: i18n.get('调整失败') },
    { value: 'ADJUST_CONFIRMED', label: i18n.get('已确认') },
  ];
}

export function columnsFilter(columns = []) {
  return columns?.map((it: { dataIndex: string }) => {
    let obj = {};
    if (it?.dataIndex === 'dataLink.E_system_checking_checkingState') {
      obj = {
        render: (text, record, index) => {
          return <div>{stateMap('checking')[text] || '-'}</div>;
        },
      };
    }
    if (it?.dataIndex === 'dataLink.E_system_checking_invoiceState') {
      obj = {
        render: (text, record, index) => {
          return <div>{stateMap('invoice')[text] || '-'}</div>;
        },
      };
    }
    if (it?.dataIndex === 'dataLink.E_system_checking_settlementState') {
      obj = {
        render: (text, record, index) => {
          return <div>{stateMap('settlement')[text] || '-'}</div>;
        },
      };
    }
    if (it?.dataIndex === 'dataLink.E_system_checking_adjustContent') {
      obj = {
        sorter: false,
        filterType: false,
        width: 350,
        render: (text, record, index) => {
          const infosLeft = text?.replace?.(/\n|\r\n/g, "<br />")
          if (!infosLeft) {
            return '-'
          }
          return <Tooltip title={<div style={{ textAlign: 'left' }} dangerouslySetInnerHTML={{ __html: infosLeft }}></div>}>
            <span className='data-link-table-text-style-new' dangerouslySetInnerHTML={{ __html: infosLeft }}></span>
          </Tooltip>;
        },
      };
    }
    if (it?.dataIndex === 'dataLink.E_system_checking_notes') {
      obj = {
        sorter: false,
        filterType: false
      };
    }
    if (it?.dataIndex === 'dataLink.E_system_checking_accountPeriod') {
      obj = {
        filterType: false,
        sorter: false,
        lookup: {
          dataSource: accountPeriodState(),
          displayExpr: 'label',
          valueExpr: 'value',
        },
        render: (value) => accountPeriodState().find((el) => el.value === value)?.label || '-'
      };
    }
    if (it?.dataIndex === 'dataLink.E_system_checking_adjustState') {
      obj = {
        filterType: false,
        sorter: false,
        lookup: {
          dataSource: adjustStateState(),
          displayExpr: 'label',
          valueExpr: 'value',
        },
        render: (value) => adjustStateState().find((el) => el.value === value)?.label || '-'
      };
    }
    if (it?.dataIndex === 'dataLink.E_system_checking_costAscriptionDepartment') {
      obj = {
        render: (text, record, index) => {
          return <div>{text?.name ? `${text?.active ? text?.name : `${text?.name}(已停用)`}` : '-'}</div>;
        },
      };
    }
    return { ...it, ...obj };
  });
}

import can from '../images/icons/can.svg';
import didi from '../images/icons/didi.svg';
import elm from '../images/icons/elm.svg';
import jd from '../images/icons/jd.svg';

export function name2icon(name: string) {
  let obj = {};
  switch (name) {
    case '滴滴公司':
      obj = { icon: didi, style: 'didi' };
      break;
    case '佳能采购':
      obj = { icon: can, style: 'can' };
      break;
    case '银企联':
      obj = { icon: null, style: 'yql' };
      break;
    case '聚通宝':
      obj = { icon: null, style: 'jtb' };
      break;
    case '京东企业购':
      obj = { icon: jd, style: 'jd' };
      break;
    case '东莞市友泰智能科技有限公司':
      obj = { icon: null, style: '' };
      break;
    case '南京合思':
      obj = { icon: null, style: '' };
      break;
    case '饿了么':
      obj = { icon: elm, style: 'elm' };
      break;
    default:
      obj = { icon: null, style: '' };
  }
  return obj;
}

import CREDIT from '../images/CREDIT.svg';
import PRIECHARGE from '../images/PRIECHARGE.svg';
import OTHER from '../images/OTHER.svg';

export function settlementType2Obj(settlementType: any) {
  switch (settlementType) {
    case 'CREDIT':
      return { icon: CREDIT, name: i18n.get('授信'), label: i18n.get('授信账户') };
    case 'PRIECHARGE':
      return { icon: PRIECHARGE, name: i18n.get('预存'), label: i18n.get('预存账户') };
    default:
      return { icon: OTHER, name: i18n.get('其他'), label: i18n.get('其他账户') };
  }
}
