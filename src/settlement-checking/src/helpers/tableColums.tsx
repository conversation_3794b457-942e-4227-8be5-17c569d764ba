/**
 *  Created by pw on 2021/6/2 下午5:32.
 */
import React from 'react';
import { Column } from '@ekuaibao/datagrid/lib/types/column';
import { supplierArchiveTypeLabel } from '../supplier-dimension/utils/form';
import { Space } from '@hose/eui';
export interface TableColumnActionIF {
  onEdit: (line: any) => void;
  onCopy?: (line: any) => void;
  statusChange: (line: any) => void;
}

const noop = (value?: any) => {};

export const renderStatus = ({ status, label }) => {
  if (status) {
    return (
      <Space>
        <div style={{ height: 6, width: 6, borderRadius: 6, background: 'var(--eui-function-success-500)' }}></div>
        <div style={{ color: 'var(--eui-text-title)' }}>{label}</div>
      </Space>
    );
  }
  return (
    <Space>
      <div style={{ height: 6, width: 6, borderRadius: 6, background: 'var(--eui-decorative-neu-500)' }}></div>
      <div style={{ color: 'var(--eui-text-title)' }}>{label}</div>
    </Space>
  );
};

export const StatementsConfigColumns = (props: TableColumnActionIF): Column[] => {
  const { onEdit = noop, statusChange = noop } = props;
  return [
    {
      title: '供应商账户',
      label: '供应商账户',
      dataIndex: ['supplierAccountId', 'name'],
      dataType: 'text',
    },
    {
      title: '适用的分拆规则',
      label: '适用的分拆规则',
      dataIndex: ['splitConfigId', 'name'],
      dataType: 'text',
    },
    {
      title: '子对账单命名规则',
      label: '子对账单命名规则',
      dataIndex: 'titleGenBy',
      dataType: 'text',
      render: (value, line) => {
        return (
          <span>
            {value ? (value == 'DEFAULT' ? '按单据模板标题公式命名' : '按账期命名') : '按单据模板标题公式命名'}
          </span>
        );
      },
    },
    {
      title: '子对账单标题',
      label: '子对账单标题',
      dataIndex: 'titleTpl',
      dataType: 'text',
      render: (value, line) => {
        return <span>{value ? `某年某月${value}` : '参考「单据模板-对账单(系统)」标题字段的规则'}</span>;
      },
    },
    {
      title: '操作',
      label: '操作',
      dataIndex: 'action',
      render: (_, line) => {
        return (
          <div className="settlement-setting-action">
            <div className="action-text edit-action statement-rule-btn" onClick={() => onEdit(line)}>
              编辑
            </div>
            <div className="action-text disabled-action" onClick={() => statusChange(line)}>
              删除
            </div>
          </div>
        );
      },
    },
  ] as unknown as Column[];
};

export const StatementsSplitConfigColumns = (props: TableColumnActionIF): Column[] => {
  const { onEdit = noop, statusChange = noop } = props;
  return [
    {
      title: '名称',
      label: '名称',
      dataIndex: 'name',
      dataType: 'text',
    },
    {
      title: '描述',
      label: '描述',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '操作',
      label: '操作',
      dataIndex: 'action',
      render: (_, line) => {
        return (
          <div className="settlement-setting-action">
            <div className="action-text edit-action" onClick={() => onEdit(line)}>
              编辑
            </div>
            <div className="action-text disabled-action" onClick={() => statusChange(line)}>
              删除
            </div>
          </div>
        );
      },
    },
  ] as Column[];
};

export const FeeGenerateConfigColumns = (props: TableColumnActionIF): Column[] => {
  const { onEdit = noop, onCopy, statusChange = noop } = props;
  return [
    {
      title: '配置名称',
      label: '配置名称',
      dataIndex: 'name',
      dataType: 'text',
      width: '35%'
    },
    {
      title: '配置描述',
      label: '配置描述',
      dataIndex: 'description',
      dataType: 'text',
      width: '35%'
    },
    {
      title: '状态',
      label: '状态',
      dataIndex: 'active',
      dataType: 'text',
      width: '15%',
      render: (value, line) => {
        return renderStatus({ status: value, label: value ? '已启用' : '已停用' });
      },
    },
    {
      title: '操作',
      label: '操作',
      dataIndex: 'action',
      render: (_, line) => {
        return (
          <div className="settlement-setting-action" style={{ minWidth: 140 }}>
            <div onClick={() => onEdit(line)}>编辑</div>
            <div onClick={() => statusChange(line)}>{line?.active ? '停用' : '启用'}</div>
            {onCopy && <div onClick={() => onCopy(line)}>复制</div>}
          </div>
        );
      },
    },
  ] as Column[];
};

export const SettleBillConfigColumns = (props: TableColumnActionIF): Column[] => {
  const { onEdit = noop, onDisabled = noop } = props;
  return [
    {
      title: '配置名称',
      label: '配置名称',
      dataIndex: 'name',
      dataType: 'text',
    },
    {
      title: '配置描述',
      label: '配置描述',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '操作',
      label: '操作',
      dataIndex: 'action',
      render: (_, line) => {
        return (
          <div className="settlement-setting-action">
            <div className="action-text edit-action" onClick={() => onEdit(line)}>
              编辑
            </div>
            <div className="action-text disabled-action" onClick={() => onDisabled(line)}>
              删除
            </div>
          </div>
        );
      },
    },
  ] as Column[];
};

export const SupplierBillConfigColumns = (props: TableColumnActionIF): Column[] => {
  const { onEdit = noop, onDisabled = noop } = props;
  return [
    {
      title: '订单号',
      label: '订单号',
      dataIndex: 'name',
      dataType: 'text',
    },
    {
      title: '票证状态',
      label: '票证状态',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '业务状态',
      label: '业务状态',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '预订人',
      label: '预订人',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '票号',
      label: '票号',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '乘机人',
      label: '乘机人',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '国内/国际',
      label: '国内/国际',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '出发地',
      label: '出发地',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '目的地',
      label: '目的地',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '航班号',
      label: '航班号',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '仓位',
      label: '仓位',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '预订日期',
      label: '预订日期',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '出发日期',
      label: '出发日期',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '到达日期',
      label: '到达日期',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '结算金额/元',
      label: '结算金额/元',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '结算方式',
      label: '结算方式',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '操作',
      label: '操作',
      dataIndex: 'action',
      render: (_, line) => {
        return (
          <div className="settlement-setting-action">
            <div className="action-text edit-action" onClick={() => onEdit(line)}>
              编辑
            </div>
            <div className="action-text disabled-action" onClick={() => onDisabled(line)}>
              删除
            </div>
          </div>
        );
      },
    },
  ] as Column[];
};

export const ApiSynchroConfigColumns = (props: TableColumnActionIF): Column[] => {
  const { onEdit = noop, statusChange = noop } = props;
  return [
    {
      title: '名称',
      label: '名称',
      dataIndex: 'name',
      dataType: 'text',
    },
    {
      title: '描述',
      label: '描述',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: '操作',
      label: '操作',
      dataIndex: 'action',
      render: (_, line) => {
        return (
          <div className="settlement-setting-action">
            <div className="action-text edit-action" onClick={() => onEdit(line)}>
              编辑
            </div>
            <div className="action-text disabled-action" onClick={() => statusChange(line)}>
              删除
            </div>
          </div>
        );
      },
    },
  ] as Column[];
};

export const OrderConfigColumns = (props: TableColumnActionIF): Column[] => {
  const { onEdit = noop, statusChange = noop } = props;
  return [
    {
      title: i18n.get('配置名称'),
      label: i18n.get('配置名称'),
      groupIndex: 'name',
      dataIndex: 'name',
      dataType: 'text',
    },
    {
      title: i18n.get('配置描述'),
      label: i18n.get('配置描述'),
      groupIndex: 'description',
      dataIndex: 'description',
      dataType: 'text',
    },
    {
      title: i18n.get('供应商类型'),
      label: i18n.get('供应商类型'),
      groupIndex: 'supplierArchiveType',
      dataIndex: 'supplierArchiveType',
      dataType: 'text',
      render: (value: string, line) => {
        const type = supplierArchiveTypeLabel();
        return <span>{type[value]}</span>;
      },
    },
    {
      title: i18n.get('操作'),
      label: i18n.get('操作'),
      groupIndex: 'action',
      dataIndex: 'action',
      render: (_, line: any) => {
        return (
          <div className="settlement-setting-action">
            <div className="action-text edit-action" onClick={() => onEdit(line)}>
              编辑
            </div>
            <div className="action-text disabled-action" onClick={() => statusChange(line)}>
              {line?.active ? '停用' : '启用'}
            </div>
          </div>
        );
      },
    },
  ];
};
