/**
 *  Created by pw on 2021/7/2 下午5:12.
 */
import { DatalinkFieldIF, DatalinkIF, GlobalFieldIF } from '@ekuaibao/ekuaibao_types';
import { app } from '@ekuaibao/whispered';
const { formatFieldType } = app.require('@custom-specification/DataLinkConditional/Utils');
let ENTITYINFOLIST: any[] = [];

const blackList = [
  'E_system_checking_requisitionId',
  'E_system_checking_checkingId',
  'E_system_checking_checkingDetailId',
  'E_system_checking_checkingFeeTypeId',
  'E_system_checking_settlementId',
  'E_system_checking_checkingCode',
  'E_system_checking_checkingTitle',
  'E_system_checking_checkingState',
  'E_system_checking_settlementCode',
  'E_system_checking_settlementTitle',
  'E_system_checking_settlementState',
];

export async function getDataLinkData(systemId: string = 'system_账单'): Promise<any[]> {
  if (!ENTITYINFOLIST.length) {
    await app.dataLoader('@common.globalFields').load();
    const items = await app.invokeService('@third-party-manage:get:entity:fields', { id: systemId });
    const availableFileds = items.filter((field: GlobalFieldIF) => !~blackList.indexOf(field.name));
    ENTITYINFOLIST = availableFileds.map((item: GlobalFieldIF) => formatFieldType({item, isRefChild: true, isStaffChild: true, showDimension: true}));
  }
  return ENTITYINFOLIST;
}

export function getDatalinkFields(datalink: DatalinkIF) {
  if (!datalink) {
    return [];
  }
  const availableFileds = datalink?.fields.filter((field: DatalinkFieldIF) => !~blackList.indexOf(field.name));
  return availableFileds.map((item: DatalinkFieldIF) => formatFieldType({item, isRefChild: true, isStaffChild: true, showDimension: true}));
}
