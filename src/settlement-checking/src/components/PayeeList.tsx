import React from 'react';
import { Row, Col } from 'antd';
import { app } from '@ekuaibao/whispered';
import PayeeItem from './PayeeItem';
import AddPayee from '../supplier-dimension/elements/AddPayee';
import { useObserver } from 'mobx-react-lite';
import { useInstance } from '@ekuaibao/react-ioc';
import { SupplierEditVm } from '../supplier-dimension/vms';
import { PayeeInfoIF } from '@ekuaibao/ekuaibao_types';

interface PayeeListProps {}

export const PayeeList: React.FC<PayeeListProps> = () => {
  const vm = useInstance<SupplierEditVm>(SupplierEditVm.NAME);
  const handleAdd = async () => {
    const payees: PayeeInfoIF[] = await app.open('@dimension-map:choosePayeesModal', {
      needCreate: true,
      payeeType: 'PUBLIC',
      selectPayee: vm.paymentAccountObj,
    });
    vm.setPaymentAccountObj(payees);
  };
  const handleDefaultChange = (line: PayeeInfoIF) => {
    const payees = vm.paymentAccountObj!.filter((item) => item.id !== line.id);
    vm.setPaymentAccountObj([line, ...payees]);
    vm.setDefaultPaymentAccountId(line.id);
  };
  const handleDel = (line: PayeeInfoIF) => {
    const payees = vm.paymentAccountObj?.filter((item) => item.id !== line.id);
    vm.setPaymentAccountObj(payees);
  };
  return useObserver(() => {
    return (
      <Row gutter={{ xs: 8, sm: 16, md: 24 }}>
        {vm.paymentAccountObj?.map((item) => {
          return (
            <Col key={item?.id} className="col-bottom-16" span={12}>
              <PayeeItem
                isDefault={vm.defaultPaymentAccountId === item.id}
                payee={item}
                setDefault={handleDefaultChange}
                onDel={handleDel}
              />
            </Col>
          );
        })}
        <Col span={12} key={'newAddPayee'}>
          <AddPayee onClick={handleAdd} />
        </Col>
      </Row>
    );
  });
};

export default PayeeList;
