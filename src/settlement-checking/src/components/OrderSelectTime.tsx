import React, { useState, useEffect } from 'react';
import { <PERSON>hanceField } from '@ekuaibao/template';
import { Radio, DatePicker } from 'antd';
import moment from 'moment';
import { IComponentDef } from '@ekuaibao/template/src/Cellar';
const RadioGroup = Radio.Group;
const { RangePicker } = DatePicker;
interface IProps {
  [key: string]: any;
}
const radioStyle = {
  display: 'block',
  height: '30px',
  lineHeight: '30px',
};
const startEndDate = (day: any) => {
  const date = moment(day);
  const start = date.startOf('month').format('YYYY-MM-DD');
  const startDate = moment(start).valueOf();
  const end = date.endOf('month').format('YYYY-MM-DD');
  const endDate = moment(end).valueOf();
  return { startDate, endDate, date, start, end };
};
const OrderSelectTime: React.FC<IProps> = (props: IProps) => {
  const { orderData } = props;
  const { startDate, endDate, date, start, end } = startEndDate(orderData?.name);
  const [radioVal, setRadioVal] = useState('1');
  useEffect(() => {
    return () => {};
  }, []);
  const handlerChange = (_: any, val: any) => {
    props.onChange && props.onChange({ startDate: moment(val[0]).format('x'), endDate: moment(val[1]).format('x') });
  };
  const radioChange = (e: any) => {
    setRadioVal(e?.target?.value);
    if (e?.target?.value === '1') {
      props.onChange && props.onChange({ startDate, endDate });
    }
  };
  const disabledDate = (current: any) => {
    return current && current.month() !== date.month();
  };
  return (
    <div className="mt-10">
      <RadioGroup value={radioVal} onChange={radioChange}>
        <Radio style={radioStyle} value="1">
          <span>{i18n.get('当前对账单账期')}</span>
          {radioVal === '1' && <span className="ml-8">{`${start}~${end}`}</span>}
        </Radio>
        <Radio style={radioStyle} value="2">
          <span>{i18n.get('自定义范围')}</span>
          {radioVal === '2' && (
            <RangePicker
              allowClear={false}
              className="ml-8"
              defaultValue={[moment(startDate), moment(endDate)]}
              // disabledDate={disabledDate}
              format={'YYYY-MM-DD'}
              size="large"
              onChange={handlerChange}
            />
          )}
        </Radio>
      </RadioGroup>
    </div>
  );
};
export default EnhanceField({
  descriptor: {
    type: 'order-select-time',
  },
  validator: () => (rule, value, callback) => {
    const { startDate, endDate } = value;
    if (!startDate || !endDate) {
      return callback(i18n.get('请选择时间范围'));
    }
    if (rule.level > 0) {
      return callback();
    }
    callback();
  },
  initialValue(props: IProps) {
    const { orderData } = props;
    const { startDate, endDate } = startEndDate(orderData?.name);
    return { startDate, endDate };
  },
} as unknown as IComponentDef)(OrderSelectTime);
