import React, { useEffect } from 'react';
import { Input, Button, Select, Radio, Alert } from '@hose/eui';
import { useInstance } from '@ekuaibao/react-ioc';
import { Category, SupplierManagerVm } from '../supplier-dimension/vms';
import { useObserver } from 'mobx-react-lite';
import { app } from '@ekuaibao/whispered';
import { showMessage } from '@ekuaibao/show-util';
import { ECategory } from '../types/enums';
import { SelectValue } from 'antd/lib/select';
import { Fetch } from '@ekuaibao/fetch';

interface TopProps {}

export const Top: React.FC<TopProps> = () => {
  const vm = useInstance<SupplierManagerVm>(SupplierManagerVm.NAME);
  useEffect(() => {
    vm.checkImportError();
  }, []);

  const handleStateChange = (value: SelectValue) => {
    vm.setCategory(value as ECategory);
  };
  const searchNameChange = (e: any) => {
    vm.setSearchName(e.target.value);
  };

  const searchCodeChange = (e: any) => {
    vm.setSearchCode(e.target.value);
  };

  const handleSearch = async () => {
    await vm.getSupplier();
  };

  const handleRefresh = () => {
    vm.getSupplier();
    vm.checkImportError();
  };

  const handleReset = async () => {
    vm.resetData();
    await vm.getSupplier();
  };

  const handleCreate = async () => {
    await app.open('@settlement-checkin:SupplierEditNew');
    await vm.getSupplier();
  };

  const batchEnable = async () => {
    try {
      await vm.batchEnable();
      vm.resetData();
      await vm.getSupplier();
    } catch (e) {
      showMessage.error(e.message);
    }
  };

  const batchDisable = async () => {
    try {
      await vm.batchDisable();
      vm.resetData();
      await vm.getSupplier();
    } catch (e) {
      showMessage.error(e.message);
    }
  };

  const importExcel = () => {
    app.open('@bills:ImportExcelModal', {
      showDownTemplateEntry: true,
      action: `${Fetch.fixOrigin(location.origin)}/api/supplier/v3/archive/excel/import?corpId=${encodeURIComponent(
        Fetch.ekbCorpId,
      )}&accessToken=${encodeURIComponent(Fetch.accessToken)}`,
      errorMsgTip: '上传内容有误，请下载标错版查看具体错误原因',
      onDownloadTemplate: handleDownloadTemplate,
      onImport: handleImportFinish,
      onCheckIsImportSuccess: handleCheckImportSuccess,
    });
  };

  const handleCheckImportSuccess = (result: Record<string, any>) => {
    if (!!result.value) {
      showMessage.success('导入完成，请稍后刷新查看导入结果');
    } else {
      showMessage.error('导入失败');
    }
    return { success: !!result.value, errorFile: !result.value, importFinish: true };
  };

  const handleDownloadTemplate = () => {
    const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId);
    const url = `${Fetch.fixOrigin(location.origin)}/api/supplier/v3/archive/excel/export/template?corpId=${ekbCorpId}`;
    app.emit('@vendor:download', url);
  };

  const handleImportFinish = async () => {
    await vm.checkImportError();
    if (!vm.errorImportExcel) {
      handleSearch();
    }
  };

  const handleClose = async () => {
    await vm.deleteSupplierErrorExcel();
    vm.checkImportError();
  };

  const exportExcel = () => {
    vm.exportExcel();
  };

  const batchExportExcel = () => {
    vm.exportAll();
  };

  const handleDownloadError = () => {
    app.emit('@vendor:download', vm.errorImportExcel, '供应商导入标错版');
  };

  return useObserver(() => (
    <div className="header">
      <div className="horizontal header-search">
        <label>{i18n.get('状态') + ':'}</label>
        <Select value={vm.category} style={{ width: 100 }} onChange={handleStateChange}>
          {Category.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
        <label>{i18n.get('供应商名称') + ':'}</label>
        <Input
          className="search"
          value={vm.searchName}
          allowClear
          placeholder={i18n.get('请输入供应商名称')}
          onChange={searchNameChange}
        />
        <label>{i18n.get('供应商编码') + ':'}</label>
        <Input
          className="search"
          value={vm.searchCode}
          allowClear
          placeholder={i18n.get('请输入供应商编码')}
          onChange={searchCodeChange}
        />
        <Button className="ml-16 mr-4"onClick={handleSearch}>
          {i18n.get('查询')}
        </Button>
        <Button className="ml-4 mr-8" category='secondary' onClick={handleReset}>
          {i18n.get('重置')}
        </Button>
      </div>

      <div className="header-operation">
        <Button className="mr-8" style={{ height: 30 }} onClick={handleCreate}>
          {i18n.get('新建')}
        </Button>
        <Radio.Group className="mr-8">
          <Radio.Button value={false} onClick={importExcel}>{i18n.get('导入')}</Radio.Button>
          <Radio.Button disabled={vm.enabledExport} value={vm.enabledExport} onClick={exportExcel}>
            {i18n.get('导出')}
          </Radio.Button>
          <Radio.Button disabled={vm.enabledBatchExport} value={vm.enabledBatchExport} onClick={batchExportExcel}>
            {i18n.get('全部导出')}
          </Radio.Button>
          <Radio.Button disabled={vm.batchEnableDisabled} value={vm.batchEnableDisabled} onClick={batchEnable}>
            {i18n.get('批量启用')}
          </Radio.Button>
          <Radio.Button disabled={vm.batchDisableDisabled} value={vm.batchDisableDisabled} onClick={batchDisable}>
            {i18n.get('批量停用')}
          </Radio.Button>
          <Radio.Button value={false} onClick={handleRefresh}>{i18n.get('刷新')}</Radio.Button>
        </Radio.Group>
      </div>

      {!!vm.errorImportExcel?.length && (
        <div className="error-content">
          <Alert
            className="supplier-error"
            message={
              <span>
                {i18n.get('导入失败，请重新上传Excel')}
                <span className="download-error-excel" onClick={handleDownloadError}>
                  {i18n.get('下载标错版')}
                </span>
              </span>
            }
            type="error"
            showIcon
            closable
            onClose={handleClose}
          />
        </div>
      )}
    </div>
  ));
};

export default Top;
