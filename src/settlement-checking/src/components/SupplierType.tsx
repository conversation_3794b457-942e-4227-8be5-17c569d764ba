import React from 'react';
import { app } from '@ekuaibao/whispered';
import { <PERSON>han<PERSON><PERSON>ield } from '@ekuaibao/template';
import { Select } from 'antd';
import { SelectValue } from 'antd/lib/select';
const { wrapper } = app.require('@components/layout/FormWrapper');
const { Option } = Select;

@((<PERSON>han<PERSON><PERSON>ield as any)({
  descriptor: {
    name: 'billPlatform',
  },
  validator: (field: any) => (rule: any, value: any, callback: (err?: string) => void) => {
    if (!value) {
      callback(i18n.get('请选择供应商类型'));
      return;
    }
    callback();
  },
  wrapper: wrapper(),
}))
export default class SupplierType extends React.PureComponent<any> {
  state = {
    dataSource: [
      {
        name: i18n.get('携程商旅'),
        id: 'XC_TRIP'
      },
      {
        name: i18n.get('吉利商旅'),
        id: 'GEELY_TRIP'
      },
      {
        name: i18n.get('同程商旅'),
        id: 'TC_TRIP'
      }, 
      {
        name: i18n.get('差旅壹号'),
        id: 'TRAVEL_ONE'
      }
    ],
  };

  handleSelectChange = (value: SelectValue) => {
    const { bus } = this.props;
    const v = value ? value : '';
    this.props.onChange?.(v);
    bus.emit('generate:api:fields:data', { billPlatform: value, billOrderType: '' });
  };
  render() {
    return (
      <Select
        defaultValue={this.props.value}
        disabled={this.props.editData}
        filterOption={true}
        className="w-100b"
        allowClear={true}
        size={'large'}
        showSearch
        optionFilterProp={'title'}
        placeholder={this.props.field?.placeholder}
        onChange={this.handleSelectChange}
      >
        {this.state.dataSource.map((item: any) => {
          return (
            <Option key={item.id} value={item.id} title={item.name}>
              {item.name}
            </Option>
          );
        })}
      </Select>
    );
  }
}
