/**
 *  Created by pw on 2021/6/4 下午6:23.
 */
import React, { Component } from 'react';
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template';
import { app } from '@ekuaibao/whispered';
const { wrapper } = app.require('@components/layout/FormWrapper');
const { required } = app.require('@components/validator/validator');
import { EnhanceConnect } from '@ekuaibao/store';
const FeeTypeSelect = app.require<any>('@elements/feeType-tree-select');
import { FeeTypeMapIF } from '@ekuaibao/ekuaibao_types';
import MessageCenter from '@ekuaibao/messagecenter';
import { IField } from '@ekuaibao/template/types/Cellar';
// @ts-ignore
import styles from './CostMatchFeeType.module.less';

interface Props {
  layer: any;
  bus: MessageCenter;
  value: string;
  feeTypes: FeeTypeMapIF[];
  feeTypeMap: FeeTypeMapIF;
  onChange: (key: string) => void;
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'cost-match-feeType',
  },
  validator: (field: IField) => (rule: any, value: string, callback: () => void) => {
    // @ts-ignore
    callback(required({ ...field, label: i18n.get('费用类型') }, value));
  },
  wrapper: wrapper(),
})
@EnhanceConnect((state: any) => ({
  feeTypes: state['@common'].feetypes.data,
  feeTypeMap: state['@common'].feetypes.map,
}))
export default class CostMatchFeeType extends Component<Props> {
  private handleFeeTypeChange = async (id: string) => {
    const { onChange, bus, feeTypeMap } = this.props;
    onChange && onChange(id);
    const feeType = feeTypeMap[id];
    const { items } = await app.invokeService('@custom-feetype:getFeetypeTemplateById', feeType?.id);
    const [feeTypeSP] = items;
    bus.emit('cost:match:feetype:change', feeTypeSP);
  };

  render() {
    const { feeTypes, value } = this.props;
    return (
      <div className={styles['cost-match-feetype-wrapper']}>
        <FeeTypeSelect
          useTreeSelectRC
          showFeeTypeCode
          filterOnSearch
          size="large"
          style={{ width: '100%' }}
          disabledCheckedFather
          multiple={false}
          treeCheckable={false}
          feeTypes={feeTypes}
          checkedKeys={value ? [value] : undefined}
          className="cg_feeType_select"
          onChange={this.handleFeeTypeChange}
        />
      </div>
    );
  }
}
