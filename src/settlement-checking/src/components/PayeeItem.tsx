import React from 'react';
import { PayeeInfoIF } from '@ekuaibao/ekuaibao_types';
import styles from '../supplier-dimension/elements/PayeeItem.module.less';

interface PayeeItemProps {
  payee: PayeeInfoIF;
  isDefault?: boolean;
  setDefault: (item: PayeeInfoIF) => void;
  onDel: (item: PayeeInfoIF) => void;
}

export const PayeeItem: React.FC<PayeeItemProps> = ({ payee, isDefault, setDefault, onDel }) => {
  const handleDefault = () => {
    setDefault(payee);
  };
  const handleDel = () => {
    onDel(payee);
  };

  return (
    <div className={styles['item']}>
      <div className="icon">
        <img src={payee.icon} />
      </div>
      <div className="info_view">
        <div className="bank">{payee.bank}</div>
        <div className="bank_number">{payee.accountNo}</div>
        <div className="company">{payee.accountName}</div>
      </div>
      {isDefault ? (
        <span className="default">{i18n.get('默认')}</span>
      ) : (
        <div className="action">
          <span onClick={handleDefault}>{i18n.get('设为默认')}</span>
          <span className="delete" onClick={handleDel}>
            {i18n.get('删除')}
          </span>
        </div>
      )}
    </div>
  );
};

export default PayeeItem;
