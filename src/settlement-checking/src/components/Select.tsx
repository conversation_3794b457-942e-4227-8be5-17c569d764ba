import React from 'react';
import { Select } from 'antd';
import { cloneDeep } from 'lodash';
const { Option } = Select;
export type opt = {
  name: string;
  label: string;
  [key: string]: any;
};
export interface IOrderSelect {
  options: opt[];
  value?: any;
  disabled?: boolean;
  valueKey?: string;
  labelKey?: string;
  optionFilterProp?: string;
  style?: any;
  mode?: 'multiple' | 'tags';
  onChange?: (value?: any) => void;
}
export const Selects: React.FC<IOrderSelect> = (props) => {
  const {
    options,
    value = [],
    optionFilterProp = 'children',
    onChange,
    valueKey = 'name',
    disabled = false,
    labelKey = 'label',
    style,
    mode,
  } = props;
  return (
    <Select
      showSearch
      value={value}
      disabled={disabled}
      onChange={onChange}
      optionFilterProp={optionFilterProp}
      style={style}
      mode={mode}
    >
      {cloneDeep(options)?.map((i) => {
        return (
          <Option key={i.label} value={i[valueKey]} disabled={i.disabled}>
            {i[labelKey]}
          </Option>
        );
      })}
    </Select>
  );
};
