import React from 'react';
import { app } from '@ekuaibao/whispered';
import { <PERSON>hanceField } from '@ekuaibao/template';
import { Select } from 'antd';
import { SelectValue } from 'antd/lib/select';
const { wrapper } = app.require('@components/layout/FormWrapper');
const { Option } = Select;

@((EnhanceField as any)({
  descriptor: {
    name: 'apportionIds',
  },
  wrapper: wrapper(),
}))
export default class ApportionWay extends React.PureComponent<any> {
  state = {
    dataSource: [],
  };
  handleFeeTypeChange = async (feeType?: any) => {
    let apportions = await app.dataLoader('@common.apportionSpecifications').load();
    if (feeType?.expenseComponents) {
      const apportionSpeIds = feeType.expenseComponents.find((item: any) => item.type === 'apportions')
        ?.specificationIds;
      if (apportionSpeIds) {
        apportions = apportions.filter((item: any) => apportionSpeIds.includes(item.originalId));
      }
    }
    this.setState({ dataSource: apportions });
  };
  componentDidMount() {
    const { bus } = this.props;
    bus.on('cost:match:feetype:change', this.handleFeeTypeChange);
    this.handleFeeTypeChange();
  }

  componentWillUnmount() {
    const { bus } = this.props;
    bus.un('cost:match:feetype:change');
  }

  handleSelectChange = (value: SelectValue) => {
    const v = value ? [value] : [];
    this.props.onChange?.(v);
  };
  render() {
    return (
      <Select
        defaultValue={this.props.value}
        filterOption={true}
        className="w-100b"
        allowClear={true}
        size={'large'}
        showSearch
        optionFilterProp={'title'}
        placeholder={this.props.field?.placeholder}
        onChange={this.handleSelectChange}
      >
        {this.state.dataSource.map((item: any) => {
          return (
            <Option key={item.id} value={item.originalId} title={item.name}>
              {item.name}
            </Option>
          );
        })}
      </Select>
    );
  }
}
