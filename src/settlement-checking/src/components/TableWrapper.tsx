import React, { useState } from 'react';
// @ts-ignore
import styles from './TableWrapper.module.less';
import * as DataGrid from '@ekuaibao/datagrid';
import { app } from '@ekuaibao/whispered';
import { TableWrapperProps } from '@ekuaibao/datagrid/lib/table/TableWrapper';
import { PaginationProps } from '@ekuaibao/datagrid';
import { ColumnChooserProps } from '@ekuaibao/datagrid/esm/widgets/ColumnChooser';
import { SearchProps } from '@ekuaibao/datagrid/esm/Search';
import { ScenesProps } from '@ekuaibao/datagrid/esm/Scenes';
import { Button as EuiBtn, Tooltip } from '@hose/eui';
import { Button } from 'antd'
import { OutlinedDirectionWindowMax, OutlinedDirectionWindowMini } from "@hose/eui-icons";
const withLoader = app.require<any>('@elements/data-grid-v2/withLoader');
const CustomSearch = app.require('@elements/data-grid-v2/CustomSearch')

interface WaitImportListProps {
  tableProps: TableWrapperProps;
  paginationProps?: PaginationProps;
  columnChooserProps?: ColumnChooserProps;
  searchProps?: SearchProps;
  scenesProps?: ScenesProps;
  footerAction?: React.ReactNode;
  exportProps?: any;
  searchClass?: string;
  columnChooserClassName?: string;
  tableLeftAction?: React.ReactNode;
  tableRightAction?: React.ReactNode;
  bodyStyle?: Record<string, string>;
  sceneClassName?: string;
  containerClassName?: string;
  isHose: boolean;
  isFull: boolean;
  handleFullScreen: () => void;
}

export const TableWrapper: React.FC<WaitImportListProps> = ({
  tableProps,
  paginationProps,
  columnChooserProps,
  searchProps,
  scenesProps,
  footerAction,
  children,
  exportProps,
  searchClass = '',
  columnChooserClassName = '',
  tableLeftAction,
  tableRightAction,
  bodyStyle = {},
  sceneClassName = '',
  containerClassName = '',
  isHose,
  handleFullScreen,
  isFull
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const onFullScreen = () => {
    app.store.dispatch('@layout5/changeImmersive')(false)
    setIsOpen(false)
    handleFullScreen?.()
  }
  const onOpenChange = (open: boolean) => {
    setIsOpen(open)
  }
  return (
    <div className={`${styles.container} ${containerClassName}`}>
      {!!scenesProps && (
        <div className={`sceneRow ${sceneClassName}`}>
          <div className={'sceneWrapper'}>
            <DataGrid.Scenes {...scenesProps} />
          </div>
        </div>
      )}
      {!!tableProps && (
        <div style={{ ...bodyStyle }} className={styles.body}>
          <DataGrid.TableWrapper className={styles.grid_wrapper} {...tableProps} />
          {!!tableLeftAction && tableLeftAction}
          {exportProps && (
            <Button className={styles.btn} {...exportProps}>
              {i18n.get('导出')}
            </Button>
          )}
          {!!searchProps && (
            searchProps.newSearch ? (
              <div style={{
                minWidth: 240,
                position: 'absolute',
                top: 2,
                right: 216,
              }}>
                <CustomSearch options={searchProps.searchOptions} onSearch={searchProps.onNewSearch} />
              </div>
            ) : (
              <DataGrid.Search {...searchProps} className={searchClass ? searchClass : styles['search-custom']} />
            )
          )}
          {!!columnChooserProps && (
            <div className={columnChooserClassName?.length ? columnChooserClassName : styles.columnChooser}>
              <DataGrid.ColumnChooser {...columnChooserProps} />
            </div>
          )}
          {isHose && (
            <Tooltip placement={isFull ? 'left' : 'top'} title={i18n.get(isFull ? '还原' : '最大化')} open={isOpen} onOpenChange={onOpenChange} >
              <EuiBtn category="secondary" className="fee-detail-table-operate" onClick={onFullScreen}>
                {
                  isFull ? <OutlinedDirectionWindowMini fontSize={14} /> : <OutlinedDirectionWindowMax fontSize={14} />
                }
              </EuiBtn>
            </Tooltip>
          )}
          {!!tableRightAction && tableRightAction}
        </div>
      )}
      {(!!paginationProps || !!footerAction) && (
        <div className={styles.footer}>
          {!!footerAction && footerAction}
          {!!paginationProps && <DataGrid.Pagination {...paginationProps} />}
        </div>
      )}
      {!!children && children}
    </div>
  );
};
export default withLoader(() => Promise.resolve({ default: TableWrapper }));
