/*
 * @Description:
 * @Creator: chencan<PERSON>han
 * @Date: 2021-06-24 16:22:40
 */
import React from 'react';
import styles from './AccountItem.module.less';
import { app } from '@ekuaibao/whispered';
import SVGIcon from './SVGIcon';
import { get } from 'lodash';
import { Tooltip } from 'antd';
import disable from '../images/disable.svg';
import enable from '../images/enable.svg';
import { name2icon, settlementType2Obj } from '../helpers/utils';

interface AccountItemProps {
  it: any;
  keel: any;
  switchStatus: (it: any) => void;
  onDetail: (it: any) => void;
  putSupplierAccount: (res, it: any) => void;
}

export const AccountItem: React.FC<AccountItemProps> = ({ it, keel, switchStatus, putSupplierAccount, onDetail }) => {
         return (
           <div
             className={styles['item_v']}
             onClick={() => {
               onDetail(it);
             }}
           >
             <div className={`item ${get(name2icon(get(it, 'supplierArchiveId.name', '')), 'style', '')}`}>
               <Tooltip placement="topLeft" title={get(it, 'name', '')}>
                 <div className="title">{get(it, 'name', '')}</div>
               </Tooltip>
               <div className="settlement_type_view">
                 <SVGIcon src={get(settlementType2Obj(get(it, 'settlementType')), 'icon')} />
                 {get(settlementType2Obj(get(it, 'settlementType')), 'label')}
               </div>
               <div className="btv">
                 <div
                   className="status_view"
                   onClick={(e) => {
                     e?.stopPropagation();
                     switchStatus(it);
                   }}
                 >
                   <SVGIcon src={get(it, 'active') ? enable : disable} />
                   <div className="status">{get(it, 'active') ? i18n.get('启用中') : i18n.get('已停用')}</div>
                 </div>
                 <div
                   className="edit"
                   onClick={(e) => {
                     e?.stopPropagation();
                     app
                       .open('@settlement-checkin:SupplierAccountModal', {
                         title: i18n.get('编辑供应商账户'),
                         data: it,
                       })
                       .then((res) => {
                         putSupplierAccount(res, it);
                       });
                   }}
                 >
                   {i18n.get(`编辑`)}
                 </div>
               </div>
             </div>
           </div>
         );
       };

export default AccountItem;
