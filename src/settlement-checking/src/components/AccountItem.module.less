@import '~@ekuaibao/web-theme-variables/styles/default.less';
@import '~@ekuaibao/web-theme-variables/styles/colors.less';
@import '~@ekuaibao/eui-styles/less/token.less';
.item_v {
  width: 314px;
  height: 110px;
  border-radius: 6px;
  :global {
    .item {
      cursor: pointer;
      width: 314px;
      height: 110px;
      border-radius: 6px;
      display: flex;
      flex-direction: column;
      background: linear-gradient(146deg, #5c8dff 13%, #7b6eff 84%, #4143f3 100%);
      padding: 10px 16px;
      position: relative;
      .title {
        opacity: 0.92;
        font-size: 16px;
        color: #ffffff;
        white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
      }
      .settlement_type_view {
        display: flex;
        color: #ffffff;
        margin-top: 5px;

        img {
          margin-right: 4px;
        }
      }

      .btv {
          display: flex;
          justify-content: space-between;
          align-items: center;
          position: absolute;
          bottom: 8px;
          left: 10px;
          right: 10px;
        .status_view {
          cursor: pointer;
          display: flex;
          align-items: center;
          padding: 8px;
          background: transparent;
          border-radius: 4px;
          opacity: 0.92;
          font-size: 14px;
          color: #ffffff;
          margin-left: -3px;

          &:hover {
            background: rgba(0, 0, 0, 0.1);
          }

          img {
            margin-top: 1px;
          }

          .status {
            font-size: 14px;
            margin-left: 7px;
          }
        }
        .edit {
            cursor: pointer;
            opacity: 0.92;
            color: #ffffff;
            padding: 8px 12px;
            font-size: 14px;
            background: transparent;
            border-radius: 4px;
            &:hover {
              background: rgba(0, 0, 0, 0.1);
            }
          }
      }
    }

    .ekb {
      background: linear-gradient(146deg, #31b7cf 13%, #31b7cf 44%, #33bcd4 84%, #2da5bb 100%);
    }

    .alsl {
      background: linear-gradient(146deg, #02a3ff 13%, #009df7 44%, #1aacff 84%, #126eff 100%);
    }

    .didi {
      background: linear-gradient(146deg, #ff905c 13%, #fd864e 44%, #ffa073 84%, #ff7e41 100%);
    }

    .can {
      background: linear-gradient(146deg, #bf4141 13%, #c12d2d 44%, #e95555 84%, #b03131 100%);
    }

    .jtb {
      background: linear-gradient(146deg, #6e5cff 13%, #5e4bfb 44%, #7e6eff 84%, #5441f3 100%);
    }

    .yql {
      background: linear-gradient(146deg, #d02d2d 13%, #e34040 44%, #f26b6b 84%, #c42626 100%);
    }

    .jd {
      background: linear-gradient(146deg, #de2e28 13%, #ff554f 89%, #c92620 100%);
    }

    .elm {
      background: linear-gradient(146deg, #04b6fd 13%, #04b6fd 44%, #55cfff 84%, #04b6fd 100%);
    }
    .disable_bgc {
      background: #b3b3b3;
    }
  }
}
