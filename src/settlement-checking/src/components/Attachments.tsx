/*
 * @Date: 2022-04-20 17:53:37
 * @LastEditors: wangminze
 * @LastEditTime: 2022-04-28 19:50:57
 * @FilePath: \plugin-web-settlement-checking\src\components\Attachments.tsx
 */
import React, { useState } from 'react';
import { app } from '@ekuaibao/whispered';
import { parseAttachment } from '@ekuaibao/lib/lib/attachment/parseAttachment';
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util';
import { IMG_REG } from '@ekuaibao/lib/lib/enums';
import { useInstance } from '@ekuaibao/react-ioc';
import { SupplierEditVm } from '../supplier-dimension/vms';
import { useObserver } from 'mobx-react-lite';
const AttachmentWrapper = app.require('@elements/puppet/attachment');

interface AttachmentsProps {}

export const Attachments: React.FC<AttachmentsProps> = () => {
  const vm = useInstance<SupplierEditVm>(SupplierEditVm.NAME);
  const [uploaderFileList, setUploaderFileList] = useState([]);

  const handleChange = (list) => {
    setUploaderFileList(list);
  };

  const handleFinish = (list) => {
    const fileList = parseAttachment(list);
    setUploaderFileList([]);
    vm.setAttachmentObj(fileList);
  };

  const handleRemoveAttachment = (line) => {
    const fileList = fnFormatAttachment(vm.attachmentObj);
    const cloneList = fileList.slice(0);
    const imgIndex = cloneList.findIndex((v) => v.fileId === line.fileId);
    cloneList.splice(imgIndex, 1);
    vm.setAttachmentObj(cloneList);
  };

  const handleOnFilePreview = (line) => {
    const fileList = fnFormatAttachment(vm.attachmentObj);
    const KA_PREVIEW_SHOW_MODAL = app.getState('@common.powers').KA_PREVIEW_PDF_SHOW_MODAL
    if (KA_PREVIEW_SHOW_MODAL) {
      let currentIdx = 0
      const items = fileList
        .filter((item: any) => item)
        .map((item: any, index: number) => {
          if ((line.id || line.fileId) === (item.id || item.fileId)) {
            currentIdx = index
          }
          return {
            key: item.key,
            fileId: item.id || item.fileId,
            title: (item.fileName || '').split('.')[0],
            source: 'UPLOAD',
            hasPDF: true,
            image: {
              url: item.url,
              fileName: item.key
            },
            imageSrc: item.url,
          }
        })
      app.emit('@vendor:iframepreview:pdf', {
        modalTitle: '附件预览',
        items,
        currentIdx
      })
      return
    }
    const imageList = fileList.filter((v) => IMG_REG.test(v.fileName));
    if (!IMG_REG.test(line.fileName)) {
      app.emit('@vendor:preview:byFetch', line.url, line.fileName, line.key);
    } else {
      app.emit('@vendor:preview:images', imageList, line);
    }
  };

  const handleFileDownload = (line) => {
    const url = line?.url;
    const fileName = line?.fileName;
    app.emit('@vendor:download', url, fileName);
  };

  return useObserver(() => {
    return (
      <AttachmentWrapper
        value={vm.attachmentObj}
        uploaderFileList={uploaderFileList}
        handleChange={handleChange}
        handleFinish={handleFinish}
        handleRemoveAttachment={handleRemoveAttachment}
        onFilePreview={handleOnFilePreview}
        onFileDownload={handleFileDownload}
        canSelectDP={true}
      />
    );
  });
};

export default Attachments;
