import React, { Component } from 'react';
import { app } from '@ekuaibao/whispered';
import { EnhanceField, IExtendBus } from '@ekuaibao/template';
import { IField } from '@ekuaibao/template/types/Cellar';
const { wrapper } = app.require('@components/layout/FormWrapper');
const { required } = app.require('@components/validator/validator');
import { Select } from 'antd';
const { Option } = Select;
import './CostFilterCodition.less';
import { getSupplierAccountList, getSupplierAccount } from '../setttlement-checkin-action';
import { QuerySelect } from 'ekbc-query-builder';
import { ICategoryId, ISupplierAccountDetail, SupplierAccountIF } from '@ekuaibao/ekuaibao_types';
import { SelectValue } from 'antd/es/select';

interface Props {
  value?: any;
  bus: IExtendBus;
  editData: any
  onChange: (value: any) => void;
}

interface State {
  supplierAccounts: SupplierAccountIF[];
  categories: ICategoryId[];
  categoryIdS: string | undefined;
  billOrderType: string | undefined;
  supplierAccountId: string | undefined;
  billPlatform: string | undefined;
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'api-filter-condition',
  },
  validator: (field: IField) => (rule: any, value: any, callback: (err?: string) => void) => {
    if (!value?.categoryId) {
      callback(i18n.get('请选择品类'));
      return;
    }
    if (!value?.supplierAccountId) {
      callback(i18n.get('请选择品类'));
      return;
    }
    if (!value?.billOrderType) {
      callback(i18n.get('请选择品类'));
      return;
    }
    // @ts-ignore
    callback(required({ ...field, label: i18n.get('筛选数据') }, value));
  },
  wrapper: wrapper(),
})
export default class ApiFilterCodition extends Component<Props, State> {
  state = {
    supplierAccounts: [] as SupplierAccountIF[],
    categories: [] as ICategoryId[],
    categoryIdS: undefined,
    orderTypes: [
      {
        id: 'FLIGHT',
        name: '飞机'
      },
      {
        id: 'HOTEL',
        name: '酒店'
      },
      {
        id: 'TRAIN',
        name: '火车'
      },
      {
        id: 'TAXI',
        name: '用车'
      },
    ]
  };

  accountMap: Record<string, ISupplierAccountDetail> = {};

  async componentDidMount() {
    const { value, bus } = this.props;
    const query = new QuerySelect()
      .select('supplierArchiveId(id,name),categoryIds(...),...')
      .filterBy(`active==true`)
      .asc('createTime');
    const { items = [] } = await getSupplierAccountList({ ...query.value() });
    if (!!value) {
      const { supplierAccountId, categoryId } = value;
      if (supplierAccountId?.id) {
        const supplierAccount = items.find(i => i?.id === supplierAccountId?.id)
        this.setState({ categories: supplierAccount?.categoryIds});
        const data: any = { supplierAccountId: supplierAccountId?.id, categoryId: categoryId?.id  };
        this.onChange(data);
      }
    }
    this.setState({ 
      supplierAccounts: items, 
      categoryIdS: value?.categoryId?.id || value?.categoryId, 
      supplierAccountId: value?.supplierAccountId?.id,
      billOrderType:  value?.billOrderType,
    });
    bus.on('generate:api:fields:data', this.handleBillPlatformChange)  
  }

  componentWillUnmount() {
    const { bus } = this.props;
    bus.un('generate:api:fields:data', this.handleBillPlatformChange)  
  }

  handleBillPlatformChange = (data: { billPlatform: string; billOrderType: string; }) => {
    if (data?.billOrderType) return
    const { onChange } = this.props;
    const data1: any = { supplierAccountId: '', categoryId: '', billOrderType: ''  };
    if (onChange) {
      onChange(data1);
    }
    this.setState({
      billPlatform: data?.billPlatform,
      categoryIdS: '',
      supplierAccountId: '',
      billOrderType: '',
      categories: []
    })
  }

  //获取供应商账户
  handleSupplierAccountChange = async (id: any, categoryId?: string) => {
    const { bus } = this.props;
    const { categoryIdS } = this.state;
    if (categoryIdS) {
      this.setState({ categories: [], categoryIdS: '' });
      bus.emit('generate:supplier:account:change');
    }

    let accountDetail = this.accountMap[id as string];
    if (!accountDetail) {
      const { value } = await getSupplierAccount(id as string);
      accountDetail = value;
      this.accountMap[id as string] = value;
    }
    
    let categories = accountDetail.categoryIds.filter((line) => !!line);
    const data: any = { supplierAccountId: id };
    this.setState({ 
      categories, 
      supplierAccountId: id
    });
    this.onChange(data);
    
  };

  //获取供应商账户品类
  handleCategoryChange = async (id: SelectValue) => {
    const { bus } = this.props;
    const { categories } = this.state;
    bus.emit('generate:supplier:account:change');
    this.setState({ categoryIdS: id as string });
    this.onChange({ categoryId: id });
    this.handleSettleBillData(categories, id)
  };

  //获取对账单字段
  handleSettleBillData = (categories: any, categoryId: any) => {
    const { bus } = this.props;
    if (!categoryId) return
    const category = categories.find((category: any) => category.id === categoryId)
    let settleBillFields = category?.fields || []
    if (settleBillFields?.length) {
      bus.emit('generate:settle:bill:data', settleBillFields);
    }
  }

  //获取供应商订单类型
  handleTravelTypeChange = async (id: SelectValue) => {
    const { bus } = this.props;
    this.setState({ billOrderType: id as string });
    this.onChange({ billOrderType: id });
    bus.emit('generate:api:fields:data', {billPlatform: '', billOrderType: id})
  };

  onChange = (data = {}) => {
    const { onChange, value = {} } = this.props;
    if (onChange) {
      onChange({ ...value, ...data });
    }
  };

  render() {
    const { editData } = this.props;
    const { supplierAccounts = [], categories = [], categoryIdS, orderTypes = [], billOrderType, supplierAccountId, billPlatform } = this.state;
    const supplierAccountsAvailable = supplierAccounts.filter(val => val?.supplierArchiveType === billPlatform || editData?.billPlatform) ?? []
    return (
      <>
        <div className="cost-filter-condition-wrapper">
          <div className="cost-filter-condition-wrapper-label w-30 mr-8">选择</div>
          <Select
            value={supplierAccountId}
            disabled={editData}
            filterOption={true}
            className="cost-filter-condition-select"
            size={'large'}
            showSearch
            optionFilterProp={'title'}
            placeholder={'请选择账单模板'}
            onChange={(selectValue) => this.handleSupplierAccountChange(selectValue)}
          >
            {supplierAccountsAvailable.map((account) => {
              return (
                <Option key={account.id} value={account.id} title={account.name}>
                  {account.name}
                </Option>
              );
            })}
          </Select>
          <div className="cost-filter-condition-wrapper-label w-70 mr-8">账户账单的</div>
          <Select
            className={'cost-filter-condition-select'}
            value={categoryIdS}
            disabled={editData}
            size={'large'}
            showSearch
            optionFilterProp={'title'}
            placeholder={'请选择品类'}
            onChange={(selectValue) => this.handleCategoryChange(selectValue)}
          >
            {categories.map((category) => {
              return (
                <Option key={category.id} value={category.id} title={category.name}>
                  {category.name}
                </Option>
              );
            })}
          </Select>
          <div className="cost-filter-condition-wrapper-label w-60">商品品类</div>
        </div>
        <div className="cost-filter-condition-wrapper" style={{marginTop:'10px'}}>
          <div className="cost-filter-condition-wrapper-label mr-8">选择供应商</div>
          <Select
            value={billOrderType}
            disabled={editData}
            filterOption={true}
            className="cost-filter-condition-select"
            style={{width:'40%'}}
            size={'large'}
            showSearch
            optionFilterProp={'title'}
            placeholder={'请选择供应商'}
            onChange={(selectValue) => this.handleTravelTypeChange(selectValue)}
          >
            {orderTypes.map((v) => {
              return (
                <Option key={v.id} value={v.id} title={v.name}>
                  {v.name}
                </Option>
              );
            })}
          </Select>
          <div className="cost-filter-condition-wrapper-label w-70 mr-8">行程类型</div>
        </div>
      </>
    );
  }
}
