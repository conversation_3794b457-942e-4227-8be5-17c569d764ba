/**
 *  Created by pw on 2021/8/11 下午3:15.
 */
import React, { useEffect, useState, memo } from 'react';
import { app } from '@ekuaibao/whispered';
import { DatalinkIF, GlobalFieldIF, GlobalFieldMapIF } from '@ekuaibao/ekuaibao_types';
import { array2object } from '@ekuaibao/helpers';
import { CostMatchMappingSource } from '../../helpers/enums';
import { T } from '@ekuaibao/i18n';
import { Select, Tooltip } from 'antd';
const { Option } = Select;
const EKBIcon = app.require<any>('@elements/ekbIcon');
import { fieldType, RuleIF } from './helper';
import { FixedCP } from './FixedValueComponent';
import classNames from 'classnames';
import { get } from 'lodash';

interface CostMatchMappingRuleItemIF {
  rule: RuleIF;
  rules: RuleIF[];
  allFeilds: GlobalFieldIF[];
  globalFieldsMap: GlobalFieldMapIF;
  groupFieldTypeMap: Record<string, GlobalFieldIF[]>;
  datalinkTypeMap: Record<string, DatalinkIF[]>;
  onAdd: (sourceField: string) => void;
  onDel: (rule: RuleIF) => void;
  onChange: (rule: RuleIF) => void;
}

export const CostMatchMappingRuleItem: React.FC<CostMatchMappingRuleItemIF> = memo(
  (props) => {
    const { rule, rules, allFeilds, globalFieldsMap, groupFieldTypeMap, datalinkTypeMap, onAdd, onDel, onChange } =
      props;
    const [sourceFields, setSourceFields] = useState<GlobalFieldIF[]>([]);
    const [mappingSource, setMappingSource] = useState<string | undefined>(rule.targetEntity);
    useEffect(() => {
      const map = array2object(rules, 'sourceField');
      const sourceFields = allFeilds.filter((f) => !map[f.name] && f.name !== 'flowLinks'); //过滤单据查询组件
      const currentRule = allFeilds.find((f) => f.name === rule.sourceField)!;
      const currentRules = [];
      if (!!currentRule) {
        currentRules.push(currentRule);
      }
      setSourceFields([...currentRules, ...sourceFields]);
    }, [rules.length]);

    const getTargetSourceData = (): any[] => {
      if (!mappingSource) {
        return [];
      }
      if (rule.sourceField?.length) {
        const field = globalFieldsMap[rule.sourceField];
        const type = fieldType(field);
        const fields = groupFieldTypeMap[type] || [];
        const data: any[] = mappingSource === CostMatchMappingSource.REQUISITION ? fields : datalinkTypeMap[type] || [];
        const hasTargetField = data.find((item: GlobalFieldIF | DatalinkIF) => item.name === rule.targetField);
        if (!hasTargetField) {
          rule.targetField = undefined;
          onChange(rule);
        }
        const list_type = get(field, 'dataType.elemType.type', '');
        const list_Entity = get(field, 'dataType.elemType.entity', '');
        if (type === 'list' && list_type === 'ref' && list_Entity) {
          return data.filter((el: any) => el?.elemType?.entity === list_Entity);
        }
        return data;
      }
      return [];
    };

    const getEvaluationSourceData = () => {
      const field = globalFieldsMap[rule.sourceField];
      const baseSources = [
        { key: CostMatchMappingSource.BILL, value: CostMatchMappingSource.BILL, label: '账单' },
        { key: CostMatchMappingSource.REQUISITION, value: CostMatchMappingSource.REQUISITION, label: '申请' },
      ];
      const fixed = { key: CostMatchMappingSource.FIXED, value: CostMatchMappingSource.FIXED, label: '固定值' };
      if (field?.dataType?.type === 'invoice') {
        return [{ key: CostMatchMappingSource.INVOICE, value: CostMatchMappingSource.INVOICE, label: '固定值' }];
      }
      if ((field?.dataType?.type === 'list' && field?.dataType?.elemType?.type === 'ref') || field?.dataType?.entity === 'basedata.city') {
        return baseSources;
      }
      return baseSources.concat([fixed]);
    };

    const handleAddAnd = () => {
      const sourceFieldMap = rules.reduce((result, rule) => {
        result[rule.sourceField] = rule;
        return result;
      }, {} as Record<string, RuleIF>);
      const waitingFeilds = allFeilds.filter((f) => !sourceFieldMap[f.name]);
      if (waitingFeilds.length) {
        const nextRule = waitingFeilds[0];
        onAdd(nextRule.name);
      }
    };

    const handleDel = () => {
      onDel(rule);
    };

    const handleSourceFieldChange = (value: string) => {
      const lastField = globalFieldsMap[rule.sourceField];
      const lastType = fieldType(lastField);
      const currentField = globalFieldsMap[value];
      const currentType = fieldType(currentField);
      // list类型切换list类型时，比较字段entity是否一致，不一致清空选项右值
      let isMultipleChange = false;
      if (currentType === 'list' && lastType === 'list') {
        const currentEntity = get(currentField, 'dataType.elemType.entity', '');
        const lastEntity = get(lastField, 'dataType.elemType.entity', '');
        if (currentEntity && lastEntity && currentEntity !== lastEntity) {
          isMultipleChange = true;
        }
      }
      if (lastType !== currentType || isMultipleChange) {
        rule.targetEntity = '';
        rule.targetField = '';
        setMappingSource(undefined);
      }
      rule.sourceField = value;

      onChange(rule);
    };

    const handleMappingSourceChange = (value: string) => {
      rule.targetEntity = value;
      rule.targetField = undefined;
      setMappingSource(value);
      onChange(rule);
    };

    const handleTargetChange = (value: string) => {
      rule.targetField = value;
      onChange(rule);
    };

    const handleFixedChange = (value: any) => {
      rule.targetField = value;
      onChange(rule);
    };
    const field = globalFieldsMap[rule?.sourceField];

    const vertical = mappingSource === CostMatchMappingSource.FIXED && field?.dataType?.type === 'ref';

    // 神奇的问题，通过组件之间引用的时候会导致下面渲染死循环
    const EvaluationSourceSelect: React.FC = () => {
      return (
        <Select
          placeholder={'请选择来源'}
          className="right-source"
          value={mappingSource}
          onChange={(value) => handleMappingSourceChange(value as string)}
        >
          {getEvaluationSourceData().map((line) => {
            return (
              <Option key={line.key} value={line.value}>
                {line.label}
              </Option>
            );
          })}
        </Select>
      );
    };

    const renderOperation = () => {
      const requiredView = () => {
        if (rules.length === 1) {
          return (
            <Tooltip title={i18n.get('请至少保留一条规则')} trigger="click">
              <EKBIcon name="#EDico-scan-b" className="oper" />
            </Tooltip>
          );
        }
        if (rule.required) {
          return (
            <Tooltip title={i18n.get('必填字段不能删除')} trigger="click">
              <EKBIcon name="#EDico-scan-b" className="oper-disabled" />
            </Tooltip>
          );
        }
        return <EKBIcon name="#EDico-scan-b" className="oper" onClick={handleDel} />;
      };

      return (
        <div className="operation">
          {rules.length === allFeilds.length ? (
            <Tooltip title={i18n.get('没有可添加的费用字段')} trigger="click">
              <EKBIcon name="#EDico-plus-default" className="oper mr-8" />
            </Tooltip>
          ) : (
            <EKBIcon name="#EDico-plus-default" className="oper mr-8" onClick={handleAddAnd} />
          )}
          {requiredView()}
        </div>
      );
    };

    return (
      <div className="cost-match-mapping-item">
        <div className="left">
          <div className="left-title">
            <T name={'费用明细字段：'} />
          </div>
          <Select
            className="left-select"
            defaultValue={rule.sourceField}
            onChange={(value) => handleSourceFieldChange(value as string)}
          >
            {sourceFields.map((field) => {
              return (
                <Option key={field.name} value={field.name}>
                  {field.label}
                </Option>
              );
            })}
          </Select>
        </div>
        <div className="right">
          <div className="right-title">
            <T name={'取'} />
          </div>
          <div className={classNames({ horizontal: !vertical, vertical: vertical })}>
            {rule?.targetEntity === CostMatchMappingSource.INVOICE ? null : (
              <Select
                placeholder={'请选择来源'}
                className="right-source"
                value={mappingSource}
                onChange={(value) => handleMappingSourceChange(value as string)}
              >
                {getEvaluationSourceData().map((line) => {
                  return (
                    <Option key={line.key} value={line.value}>
                      {line.label}
                    </Option>
                  );
                })}
              </Select>
            )}
            {mappingSource === CostMatchMappingSource.FIXED || mappingSource === CostMatchMappingSource.INVOICE ? (
              <FixedCP
                rule={rule}
                globalFieldsMap={globalFieldsMap}
                onFixedChange={handleFixedChange}
                EvaluationSourceSelect={EvaluationSourceSelect}
              />
            ) : (
              <Select
                placeholder={'请选择字段值'}
                className="right-source-input"
                showSearch
                value={(rule?.targetField as string)?.length ? (rule.targetField as string) : undefined}
                optionFilterProp={'title'}
                onChange={(value) => handleTargetChange(value as string)}
              >
                {getTargetSourceData().map((field) => {
                  return (
                    <Option key={field.name} value={field.name} title={field.label}>
                      {field.label}
                    </Option>
                  );
                })}
              </Select>
            )}
          </div>
          <div className="right-label">
            <T name={'字段值'} />
          </div>
          {renderOperation()}
        </div>
      </div>
    );
  },
  (prevProps, nextProps) => {
    const { rule: preRule, rules: preRules } = prevProps;
    const { rule: nextRule, rules: nextRules } = nextProps;
    return (
      preRule?.targetField === nextRule?.targetField &&
      preRule.sourceField === nextRule.sourceField &&
      preRule.sourceEntity === nextRule.sourceEntity &&
      preRule.targetEntity === nextRule.targetEntity &&
      preRules?.length === nextRules?.length
    );
  },
);
