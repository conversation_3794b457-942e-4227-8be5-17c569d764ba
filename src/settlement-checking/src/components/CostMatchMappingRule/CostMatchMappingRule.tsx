/**
 *  Created by pw on 2021/6/4 下午6:49.
 */
import React, { Component } from 'react';
import { EnhanceField, IExtendBus } from '@ekuaibao/template';
import { app } from '@ekuaibao/whispered';
const { wrapper } = app.require('@components/layout/FormWrapper');
import { EnhanceConnect } from '@ekuaibao/store';
import { IField } from '@ekuaibao/template/types/Cellar';
import { FeeTypeIF, GlobalFieldIF, ComponentIF, GlobalFieldMapIF, DatalinkIF } from '@ekuaibao/ekuaibao_types';
import './CostMatchMappingRule.less';
import { uuid, isString } from '@ekuaibao/helpers';
const EmptyBody = app.require<any>('@bills/elements/EmptyBody');
const EMPTY_ICON = app.require('@images/empty.svg');
import { global_blackList } from '@ekuaibao/lib/lib/lib-util';
import { getDatalinkFields } from '../../helpers/FeeGenerateConfigUtil';
import { CostMatchMappingRuleItem } from './CostMatchMappingRuleItem';
import { defaultLegalEntityField, fieldType, RuleIF, RuleInvoiceTargetValue } from './helper';
import { CostMatchMappingSource } from '../../helpers/enums';
import { findLastIndex } from 'lodash';

interface Props {
  bus: IExtendBus;
  baseDataProperties: GlobalFieldIF[];
  globalFieldsMap: GlobalFieldMapIF;
  form: any;
  value?: RuleIF[];
  tag: any;
  onChange: (values: any) => void;
}

interface State {
  rules: RuleIF[];
  fields: GlobalFieldIF[];
  expenseComponents: ComponentIF[];
  groupFieldTypeMap: Record<string, GlobalFieldIF[]>;
  datalinkTypeMap: Record<string, DatalinkIF[]>;
}

const DataLinkFieldMapType: Record<string, string> = {
  E_system_checking_city: 'basedata.city',
};

@((EnhanceField as any)({
  descriptor: {
    type: 'cost-match-mapping-rule',
  },
  validator: (field: IField) => (rule: any, values: RuleIF[], callback: (err?: string) => void) => {
    if (rule.level === 1) {
      return callback();
    }
    if (values) {
      const hasError = values.reduce((result: Record<number, boolean>, cur, index) => {
        if (
          cur.targetEntity === CostMatchMappingSource.INVOICE &&
          (cur?.targetField as RuleInvoiceTargetValue)?.type === 'unify' &&
          !(cur?.targetField as RuleInvoiceTargetValue)?.invoiceCorporation?.length
        ) {
          result[index] = true;
          return result;
        }
        let targetField = cur?.targetField;
        if (cur?.targetField && !isString(cur?.targetField)) {
          targetField = `${cur?.targetField}`;
        }
        if (!(targetField as string)?.length || !cur?.targetEntity?.length) {
          result[index] = true;
        }
        return result;
      }, {});
      const keys = Object.keys(hasError);
      if (keys.length) {
        const rows = keys.map((key) => Number(key) + 1);
        return callback(`第${rows.join('，')}行赋值规则填写不完整`);
      }
    } else {
      return callback(`请填写赋值规则`);
    }
    callback();
  },
  wrapper: wrapper(),
}))
@EnhanceConnect((state: any) => ({
  baseDataProperties: state['@common'].globalFields.data,
  globalFieldsMap: state['@common'].globalFields.baseDataPropertiesMap,
}))
export default class CostMatchMappingRule extends Component<Props, State> {
  state = {
    rules: [] as RuleIF[],
    fields: [],
    expenseComponents: [] as ComponentIF[],
    groupFieldTypeMap: {},
    datalinkTypeMap: {},
  };

  async componentDidMount() {
    const { bus, baseDataProperties, tag } = this.props;
    bus.on('cost:match:feetype:change', this.handleFeeTypeChange);
    bus.on('generate:fee:datalink:change', this.fnHandleDataLinkChange);
    await this.fnHandleDataLinkChange(tag?.dataLinkEntity, true);
    const groupFieldTypeMap = baseDataProperties
      .filter((field) => !~global_blackList.indexOf(field.name))
      .reduce((result: Record<string, GlobalFieldIF[]>, field) => {
        const type = fieldType(field);
        let fields = result[type];
        if (!fields) {
          fields = [];
          result[type] = fields;
        }
        fields.push(field);
        return result;
      }, {});
    this.fnUpdateVauleByFeeType();
    this.setState({ groupFieldTypeMap });
  }

  componentWillUnmount() {
    const { bus } = this.props;
    bus.un('cost:match:feetype:change', this.handleFeeTypeChange);
    bus.un('generate:fee:datalink:change', this.fnHandleDataLinkChange);
  }

  fnUpdateVauleByFeeType = async () => {
    const { form, value } = this.props;
    const feeTypeId = form.getFieldValue('feeTypeId');
    if (feeTypeId && value) {
      const rules = value.map((rule) => {
        return { ...rule, id: uuid(8) };
      });
      const { items } = await app.invokeService('@custom-feetype:getFeetypeTemplateById', feeTypeId);
      const [feeTypeSP] = items;
      await this.handleFeeTypeChange(feeTypeSP, rules);
    }
  };

  handleFeeTypeChange = async (feeType: FeeTypeIF, ruleValues: RuleIF[] = []) => {
    const { globalFieldsMap } = this.props;
    const blackFieldMap: Record<string, string> = {
      apportions: 'apportions',
      system_statement: 'system_statement',
    };
    const blackFieldTypeMap: Record<string, string> = { attachments: 'attachments' };
    const { expenseComponents = [] } = feeType as any;

    const allFields = expenseComponents
      .filter((f: ComponentIF) => f.label.length && f.optional !== undefined)
      .filter((f: ComponentIF) => {
        return !blackFieldMap[f.field] && !blackFieldTypeMap[f.type];
      });
    const allFieldMap: Record<string, ComponentIF> = allFields.reduce(
      (result: Record<string, ComponentIF>, field: ComponentIF) => {
        result[field.field] = field;
        return result;
      },
      {},
    );

    const legalEntityFieldName = 'u_对账法人实体';
    const requireFields = allFields
      .filter((field: ComponentIF) => !field.optional)
      .map((f: ComponentIF) => globalFieldsMap[f.field])
      .filter((field: GlobalFieldMapIF) => field?.active);
    if (!ruleValues?.length) {
      const legalEntityField = allFields.find((field: ComponentIF) => field.field === legalEntityFieldName);
      if (!legalEntityField) {
        const legalEntityField = globalFieldsMap[legalEntityFieldName] || defaultLegalEntityField;
        requireFields.push(legalEntityField);
      }
    }

    const optionalFields = allFields
      .filter((field: ComponentIF) => field.optional)
      .map((f: ComponentIF) => globalFieldsMap[f.field])
      .filter((field: GlobalFieldMapIF) => field?.active);
    const rules = ruleValues?.length
      ? ruleValues
          .filter((rule) => !!allFieldMap[rule.sourceField])
          .map((rule) => {
            const required = !!requireFields.find((field: GlobalFieldIF) => field.name === rule.sourceField);
            return { ...rule, required, sourceFieldComponent: allFieldMap[rule.sourceField] };
          })
      : requireFields.map((f: GlobalFieldIF) => ({
          id: uuid(8),
          sourceField: f.name,
          sourceEntity: 'flow.form',
          required: true,
          sourceFieldComponent: allFieldMap[f.name],
        }));
    this.fnAddLegalEntityField(ruleValues, rules, legalEntityFieldName, requireFields, allFieldMap);
    this.handleChange(rules);
    this.setState({ rules, expenseComponents, fields: [...requireFields, ...optionalFields] });
  };

  // 在已经添加规则的列表里面增加对账法人实体字段
  fnAddLegalEntityField = (
    ruleValues: RuleIF[],
    rules: any[],
    fieldName: string,
    requireFields: GlobalFieldIF[],
    allFieldMap: Record<string, ComponentIF>,
  ) => {
    if (ruleValues?.length) {
      const { globalFieldsMap } = this.props;
      const legalEntityField = ruleValues.find((rule) => rule.sourceField === fieldName);
      if (!legalEntityField) {
        const lastRequiredIndex = findLastIndex(rules, (rule: any) => rule.required);
        if (lastRequiredIndex >= 0) {
          const legalField = globalFieldsMap[fieldName] || defaultLegalEntityField;
          rules.splice(lastRequiredIndex + 1, 0, {
            id: uuid(8),
            sourceField: legalField.name,
            sourceEntity: 'flow.form',
            required: true,
            sourceFieldComponent: allFieldMap[legalField.name],
          });
          requireFields.push(legalField);
        }
      }
    }
  };

  fnHandleDataLinkChange = async (datalink: DatalinkIF, isFirstLoad = false) => {
    const fields: DatalinkIF[] = getDatalinkFields(datalink);
    const datalinkTypeMap = fields.reduce<any>((result: Record<string, DatalinkIF[]>, field) => {
      const type = DataLinkFieldMapType[field.name] || fieldType(field);
      let fields = result[type];
      if (!fields) {
        fields = [];
        result[type] = fields;
      }
      fields.push(field);
      return result;
    }, {});
    let { rules } = this.state;
    if (!isFirstLoad) {
      if (rules?.length) {
        rules = rules?.map((rule) => {
          if (rule.targetEntity === CostMatchMappingSource.BILL) {
            rule.id = uuid(8);
            rule.targetField = undefined;
          }
          return rule;
        });
        this.handleChange(rules);
      }
    }
    this.setState({ datalinkTypeMap, rules });
  };

  handleChange = (rules: RuleIF[]) => {
    const { onChange } = this.props;
    const values = rules.map((rule) => {
      return {
        targetEntity: rule.targetEntity,
        targetField: rule.targetField,
        sourceEntity: rule.sourceEntity,
        sourceField: rule.sourceField,
      };
    });
    onChange(values);
  };

  handleAddRule = (sourceField: string) => {
    const { rules, expenseComponents } = this.state;
    const sourceFieldComponent = expenseComponents.find((cp) => cp.field === sourceField);
    const addRule = { id: uuid(8), sourceField, sourceEntity: 'flow.form', sourceFieldComponent } as RuleIF;
    const changeRules = rules.concat(addRule);
    this.handleChange(changeRules);
    this.setState({ rules: rules.concat(addRule) });
  };

  handleDelRule = (rule: RuleIF) => {
    const { rules } = this.state;
    const filterRules = rules.filter((r) => r.id !== rule.id);
    this.handleChange(filterRules);
    this.setState({ rules: filterRules });
  };

  handleRuleChange = (rule: RuleIF) => {
    const { rules, expenseComponents } = this.state;
    const ruleIndex = rules.findIndex((r) => r.id === rule.id);
    const lastRule = rules[ruleIndex];
    if (lastRule.sourceField !== rule.sourceField) {
      rule.sourceFieldComponent = expenseComponents.find((cp) => cp.field === rule.sourceField);
    }
    rules[ruleIndex] = rule;
    this.setState({ rules: rules.slice() });
    this.handleChange(rules);
  };

  render() {
    const { rules = [], fields, groupFieldTypeMap, datalinkTypeMap } = this.state;
    const { globalFieldsMap } = this.props;
    return (
      <div className="cost-match-mapping-rule-wrapper">
        {rules.length === 0 && (
          <div className="cost-no-match-mapping">
            <EmptyBody className={'no-match-mapping'} icon={EMPTY_ICON} label={i18n.get('暂无赋值规则')} />
          </div>
        )}
        {rules.map((rule) => {
          return (
            <CostMatchMappingRuleItem
              key={rule.id}
              rule={rule}
              rules={rules}
              allFeilds={fields}
              globalFieldsMap={globalFieldsMap}
              groupFieldTypeMap={groupFieldTypeMap}
              datalinkTypeMap={datalinkTypeMap}
              onAdd={this.handleAddRule}
              onDel={this.handleDelRule}
              onChange={this.handleRuleChange}
            />
          );
        })}
      </div>
    );
  }
}
