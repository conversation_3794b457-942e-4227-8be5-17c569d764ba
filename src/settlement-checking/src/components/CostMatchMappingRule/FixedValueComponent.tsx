/**
 *  Created by pw on 2021/8/11 下午3:23.
 */
import React, { useEffect, useState, memo } from 'react';
import { DatePicker, Select, Input } from 'antd';
import { Input as EUIInput, DatePicker as EUIDatePicker } from '@hose/eui';
import { app } from '@ekuaibao/whispered';
import { isObject, isString } from '@ekuaibao/helpers';
import { fixedValueFieldType, RuleIF, invoiceOptions, RuleInvoiceTargetValue } from './helper';
import { DimensionIF, GlobalFieldIF, GlobalFieldMapIF } from '@ekuaibao/ekuaibao_types';
import moment from 'moment';
import { fetchDatalinkReferenceData } from '../../setttlement-checkin-action';
import { SelectValue } from 'antd/es/select';
const RefView = app.require<any>('@elements/puppet/Ref');
const FakeInput = app.require<any>('@elements/puppet/FakeInput');
const { Option } = Select;

interface FixedCPIF {
  rule: RuleIF;
  sourceType?: string;
  sourceField?: any;
  style?: React.CSSProperties;
  globalFieldsMap?: GlobalFieldMapIF;
  needFullData?: boolean;
  useEUI?: boolean;
  datalinkId?: string;
  multiple?: boolean;
  useTreeSelectRC?: boolean;
  onFixedChange: (value: any) => void;
  EvaluationSourceSelect?: React.FC;
}

export const FixedCP: React.FC<FixedCPIF> = (props) => {
  const {
    rule,
    globalFieldsMap,
    onFixedChange,
    EvaluationSourceSelect,
    sourceType,
    sourceField,
    style,
    needFullData,
    useEUI,
    datalinkId,
    multiple,
    useTreeSelectRC,
  } = props;
  let type = sourceType;
  const field = sourceField || globalFieldsMap?.[rule.sourceField];
  if (!sourceType) {
    type = fixedValueFieldType(field);
  }

  useEffect(() => {
    if (!rule.targetField && (type === 'date' || type === 'dateRange')) {
      onFixedChange(moment().valueOf());
    }
  }, [type]);

  const typeComponent: Record<string, React.FC<BaseProps>> = {
    date: DateCP,
    dateRange: DateCP,
    invoice: InvoiceCP,
    money: NumberCP,
    number: NumberCP,
    'basedata.Dimension': DimensionCP,
    'organization.Department': DimensionCP,
    'organization.Staff': DimensionCP,
    'basedata.Enum': DimensionCP,
    'datalink.DataLinkEntity': DatalinkCP,
    'basedata.city': DimensionCP,
  };

  const CP = typeComponent[type!] || DefaultCP;
  return (
    <CP
      rule={rule}
      onFixedChange={onFixedChange}
      field={field}
      EvaluationSourceSelect={EvaluationSourceSelect}
      style={style}
      needFullData={needFullData}
      useEUI={useEUI}
      datalinkId={datalinkId}
      multiple={multiple}
      useTreeSelectRC={useTreeSelectRC}
    />
  );
};

interface BaseProps {
  rule: RuleIF;
  field: GlobalFieldIF;
  style?: React.CSSProperties;
  needFullData?: boolean;
  onFixedChange: (value: any) => void;
  EvaluationSourceSelect?: any;
  useEUI?: boolean;
  datalinkId?: string;
  multiple?: boolean;
  useTreeSelectRC?: boolean;
}

const DateCP: React.FC<BaseProps> = memo((props) => {
  const { style, rule, onFixedChange, useEUI } = props;
  const handleChange = (e: any) => {
    onFixedChange(moment(e).valueOf());
  };

  if (useEUI) {
    return (
      <EUIDatePicker
        style={style}
        className="right-source-input"
        defaultValue={rule?.targetField ? moment(parseInt(rule.targetField as string)) : moment()}
        onChange={handleChange}
      />
    );
  }

  return (
    <DatePicker
      style={style}
      className="right-source-input"
      defaultValue={rule?.targetField ? moment(parseInt(rule.targetField as string)) : moment()}
      onChange={handleChange}
    />
  );
}, areEqual);

const DimensionCP: React.FC<BaseProps> = memo((props) => {
  const { rule, field, style, needFullData, onFixedChange, multiple = false, useTreeSelectRC } = props;

  const handleDimensionList = (dimension?: DimensionIF) => {
    if (needFullData) {
      onFixedChange(dimension);
    } else {
      onFixedChange(dimension?.id);
      rule.targetField = dimension?.id;
    }
  };
  const data = {
    onChange: handleDimensionList,
    id: isObject(rule.targetField) ? (rule.targetField as any)?.id : rule.targetField,
    multiple,
  };
  const param = { name: field?.dataType?.entity };
  return (
    <div style={style} className="right-source-select-ref">
      <RefView param={param} data={data} onlyLeafCanBeSelected={false} useTreeSelectRC={useTreeSelectRC} />
    </div>
  );
}, areEqual);

const DatalinkCP: React.FC<BaseProps> = memo((props) => {
  const { rule, field, style, datalinkId, onFixedChange } = props;
  const [data, setData] = useState({} as any);

  useEffect(() => {
    const useDatalinkId = datalinkId || rule?.targetField;
    if (useDatalinkId) {
      app
        .invokeService('@third-party-manage:get:entity:detail:data', { dataLinkId: useDatalinkId, type: 'DETAIL' })
        .then((res: any) => {
          setData({ ...res.value.data });
        });
    }
  }, []);

  const handleOnClick = async () => {
    const referenceDataId = rule.sourceFieldComponent?.referenceData;
    if (!referenceDataId) {
      return;
    }
    let referenceData: any = referenceDataId;
    if (referenceDataId && isString(referenceDataId)) {
      const { value } = await fetchDatalinkReferenceData({ id: referenceDataId });
      referenceData = value;
      rule.sourceFieldComponent!.referenceData = referenceData;
    }
    const entityId = field?.dataType?.entity?.split('.')?.pop();
    const type = referenceData?.platformId?.type;
    const result: any = await app.open('@bills:SelectDataLinkModal', {
      entityInfo: {
        selectedEntity: data,
        dataLink: { id: entityId, name: field?.label },
        name: rule?.sourceFieldComponent?.label,
        type,
        referenceData,
      },
      field,
      showClose: true,
    });
    if (result) {
      setData({ ...result.data });
      rule.targetField = result?.data?.dataLink?.id;
      onFixedChange(rule.targetField);
    }
  };

  return (
    <div style={style} className="right-source-select-ref">
      <FakeInput onClick={handleOnClick} placeholder={i18n.get('请选择')}>
        {data?.dataLink?.[`E_${data?.dataLink?.entityId}_name`]}
      </FakeInput>
    </div>
  );
}, areEqual);

const InvoiceCP: React.FC<BaseProps> = memo((props) => {
  const { rule, onFixedChange, EvaluationSourceSelect } = props;
  const [type, setType] = useState((rule?.targetField as RuleInvoiceTargetValue)?.type);
  const [corporationList, setCorporationList] = useState<any[]>([]);
  const [invoiceCorporation, setInvoiceCorporation] = useState(
    (rule?.targetField as RuleInvoiceTargetValue)?.invoiceCorporation,
  );
  useEffect(() => {
    setType((rule?.targetField as RuleInvoiceTargetValue)?.type);
  }, [(rule?.targetField as RuleInvoiceTargetValue)?.type]);

  useEffect(() => {
    if ((rule?.targetField as RuleInvoiceTargetValue)?.invoiceCorporation?.length) {
      fetchCorporation();
    }
  }, [(rule?.targetField as RuleInvoiceTargetValue)?.invoiceCorporation]);

  const fetchCorporation = async () => {
    if (!corporationList?.length) {
      const { items } = await app.invokeService('@bills:get:invoice:corporation');
      setCorporationList(items);
    }
  };

  const handleChange = async (val: SelectValue) => {
    rule.targetField = { type: val as string };
    if (val === 'unify') {
      await fetchCorporation();
      rule.targetField.invoiceCorporation = corporationList?.length ? corporationList[0].id : undefined;
    }
    setType(val as string);
    onFixedChange(rule.targetField);
  };

  const handleChangeCorporation = (val: SelectValue) => {
    setInvoiceCorporation(val as string);
    (rule.targetField! as RuleInvoiceTargetValue).invoiceCorporation = val as string;
    onFixedChange(rule.targetField);
  };

  return (
    <div className={'vertical'}>
      <div className={'dis-f'}>
        <EvaluationSourceSelect />
        <Select value={type} className="right-source-input" onChange={handleChange}>
          {invoiceOptions().map((item) => {
            return (
              <Option key={item.value} value={item.value} title={item.label}>
                {item.label}
              </Option>
            );
          })}
        </Select>
      </div>
      {type === 'unify' && (
        <Select value={invoiceCorporation} style={{ width: '100%', marginTop: 6 }} onChange={handleChangeCorporation}>
          {corporationList.map((item, index) => {
            return (
              <Option key={item.id} value={item.id}>
                {item.name}
              </Option>
            );
          })}
        </Select>
      )}
    </div>
  );
}, areEqual);

const NumberCP: React.FC<BaseProps> = memo((props) => {
  const { rule, onFixedChange, useEUI } = props;
  const [stateValue, setStateValue] = useState<string | ReadonlyArray<string> | number>('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value: inputValue } = e.target;
    const reg = /^-?\d*(\.\d*)?$/;
    if (reg.test(inputValue) || inputValue === '' || inputValue === '-') {
      setStateValue(inputValue);
      rule.targetField = inputValue;
      onFixedChange(inputValue);
    } else {
      const originalValue = stateValue
      setStateValue(originalValue);
    }
  };

  if (useEUI) {
    return (
      <EUIInput
        defaultValue={rule.targetField as string}
        value={stateValue}
        className="right-source-input"
        placeholder={i18n.get('请输入')}
        onChange={handleChange}
      />
    );
  }
  return (
    <Input
      defaultValue={rule.targetField as string}
      className="right-source-input"
      placeholder={i18n.get('请输入')}
      onChange={handleChange}
    />
  );
}, areEqual);

const DefaultCP: React.FC<BaseProps> = memo((props) => {
  const { rule, onFixedChange, useEUI } = props;
  const handleChange = (e: any) => {
    const value = e.target.value.trim();
    rule.targetField = value;
    onFixedChange(value);
  };
  if (useEUI) {
    return (
      <EUIInput
        defaultValue={rule.targetField as string}
        className="right-source-input"
        placeholder={i18n.get('请输入')}
        onChange={handleChange}
      />
    );
  }
  return (
    <Input
      defaultValue={rule.targetField as string}
      className="right-source-input"
      placeholder={i18n.get('请输入')}
      onChange={handleChange}
    />
  );
}, areEqual);

function areEqual(prevProps: BaseProps, nextProps: BaseProps) {
  return prevProps.rule?.targetField === nextProps.rule?.targetField;
}
