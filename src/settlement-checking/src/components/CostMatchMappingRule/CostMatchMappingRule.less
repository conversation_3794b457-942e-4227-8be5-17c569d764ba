@import '~@ekuaibao/eui-styles/less/token.less';

.cost-match-mapping-rule-wrapper {
  display: flex;
  flex-direction: column;
  .cost-match-mapping-item {
    padding: @space-5 @space-6;
    margin-top: @space-6;
    display: flex;
    background-color: #f7f7f7;
    border-radius: @space-3;
    justify-content: space-between;
    align-items: center;
    &:first-child {
      margin-top: 0;
    }
    .left {
      display: flex;
      align-items: center;
      .left-title {
        .font-size-2;
        .font-weight-2;
        color: rgba(0, 0, 0, 0.6);
      }
      .left-select {
        width: 120px;
        .ant-select-selection--single {
          width: 100%;
        }
      }
    }
    .right {
      display: flex;
      align-items: center;
      .right-title {
        .font-size-2;
        .font-weight-2;
        color: rgba(0, 0, 0, 0.6);
        margin-right: @space-4;
      }
      .right-source {
        width: 100%;
        min-width: 100px;
        display: flex;
        .ant-select-selection--single {
          width: 100%;
        }
      }
      .right-source-value {
        width: 140px;
        margin-left: @space-4;
      }
      .right-source-input {
        width: 160px;
        margin-left: @space-4;
        .ant-select-selection--single {
          width: 100%;
        }
      }
      .right-source-select-ref {
        width: 268px;
        margin-top: @space-3;
        .ant-select-selection--single {
          width: 100%;
        }
      }
      .right-label {
        .font-size-2;
        .font-weight-2;
        margin-left: @space-4;
        color: rgba(0, 0, 0, 0.6);
      }
      .operation {
        display: flex;
        cursor: pointer;
        margin-left: @space-5;
        flex-shrink: 0;
        .oper {
          cursor: pointer;
          color: var(--brand-base);
        }
        .oper-disabled {
          color: var(--brand-base);
          opacity: 0.5;
        }
      }
    }
  }
  .cost-no-match-mapping {
    margin-top: @space-7;
    display: flex;
    .no-match-mapping {
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }
  }
}
