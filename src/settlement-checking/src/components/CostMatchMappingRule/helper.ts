/**
 *  Created by pw on 2021/8/11 下午3:21.
 */

import { ComponentIF, GlobalFieldIF } from '@ekuaibao/ekuaibao_types';
import { app } from '@ekuaibao/whispered';

export const fieldType = (field: any): string => {
  const type = field?.dataType?.entity || field?.entity || field?.dataType?.type || field?.type;
  if (type?.startsWith('basedata.Dimension')) {
    return 'basedata.Dimension';
  }
  return type;
};

export const fixedValueFieldType = (field: any): string => {
  const type = fieldType(field);
  if (type?.startsWith('basedata.Enum')) {
    return 'basedata.Enum';
  }
  if (type?.startsWith('datalink.DataLinkEntity')) {
    return 'datalink.DataLinkEntity';
  }
  return type;
};

export const invoiceOptions = () => {
  return [
    { label: i18n.get('待开发票'), value: 'wait' },
    { label: i18n.get('无需填写'), value: 'noWrite' },
    { label: i18n.get('统一开票'), value: 'unify' },
  ];
};

export interface RuleIF extends GlobalFieldIF {
  id: string;
  required: boolean;
  sourceEntity: string;
  sourceFieldComponent?: IFieldComponent; // 费用类型模板组件
  sourceField: string;
  targetEntity: string;
  targetField: string | RuleInvoiceTargetValue | undefined;
}

interface IFieldComponent extends ComponentIF {
  referenceData?: any;
}

export interface RuleInvoiceTargetValue {
  type: string;
  invoiceCorporation?: string;
}

export const defaultLegalEntityField = {
  name: 'u_对账法人实体',
  label: '对账法人实体',
  active: true,
  canAsDimension: true,
  dataType: { type: 'ref', entity: 'basedata.Dimension.法人实体' },
  cnLabel: '对账法人实体',
  enLabel: '',
};

export function standardValueMoney(value: string) {
  const standardCurrency = app.getState()['@common'].standardCurrency;
  const { strCode, numCode, symbol, unit, scale } = standardCurrency;
  return {
    standard: value,
    standardStrCode: strCode,
    standardNumCode: numCode,
    standardSymbol: symbol,
    standardUnit: unit,
    standardScale: scale,
  };
}
