/*
 * @Description: 配置赋值规则
 * @Creator: chencan<PERSON>han
 * @Date: 2021-06-18 19:30:07
 */
import React, { useEffect, useState } from 'react';
import {  Select } from 'antd';
import style from './AssignRule.module.less';
const { Option } = Select;

interface Props {
  value: Array<Array<string[]>>;
  onChange?: (values: any) => void;
  bus: any;
  [key: string]: any;
}

const AssignRule: React.FC<Props> = (props) => {
  const func = (value) => {
    let obj = {};
    value?.forEach((it) => (obj[it?.sourceField] = it?.targetField));
    return obj;
  };
  const { value, onChange, bus, textArr = [], PayeeInfoArr = [], moneyArr = [], basedataArr = [], show } = props;
  const obj = func(value);
  const [payMoney, setPayMoney] = useState(obj?.payMoney || '');
  const [supplierArchive, setSupplierArchive] = useState(obj?.supplierArchive || '');
  const [payeeInfo, setPayeeInfo] = useState(obj?.payeeInfo || '');
  const [entityId, setEntityId] = useState(obj?.entityId || '');

  const handleValueChange = (payMoney, supplierArchive, payeeInfo, entityId) => {
    if (onChange) {
      show
        ? onChange([
            { targetField: payMoney, sourceField: 'payMoney' },
            { targetField: supplierArchive, sourceField: 'supplierArchive' },
            { targetField: payeeInfo, sourceField: 'payeeInfo' },
            { targetField: entityId, sourceField: 'entityId' },
          ])
        : onChange([
            { targetField: payMoney, sourceField: 'payMoney' },
            { targetField: supplierArchive, sourceField: 'supplierArchive' },
            { targetField: entityId, sourceField: 'entityId' },
          ]);
    }
  };
  const resetValue = () => {
    setPayMoney('');
    setSupplierArchive('');
    setPayeeInfo('');
    setEntityId('');
    handleValueChange('', '', '', '');
  };

  useEffect(() => {
    bus && bus.on('assignRule:resetValue', resetValue);
    return () => {
      bus && bus.un('assignRule:resetValue', resetValue);
    };
  }, []);

  return (
    <div className={style['assign-rule-wrapper']}>
      <div className="assign_item">
        <div className="label">法人实体统计金额</div>
        <div className="right_view">
          <div className="">赋值到单据</div>
          <Select
            style={{ width: 238, marginLeft: 16, marginRight: 16 }}
            size={'large'}
            value={payMoney}
            allowClear
            placeholder={'请选择字段'}
            optionFilterProp={'title'}
            onChange={(selValue) => {
              setPayMoney(selValue);
              handleValueChange(selValue, supplierArchive, payeeInfo, entityId);
            }}
          >
            {moneyArr?.map((line, index) => {
              return (
                <Option key={index} value={line.name}>
                  {line.label}
                </Option>
              );
            })}
          </Select>
          <div className="">字段</div>
        </div>
      </div>
      <div className="assign_item">
        <div className="label">供应商名称</div>
        <div className="right_view">
          <div className="">赋值到单据</div>
          <Select
            allowClear
            style={{ width: 238, marginLeft: 16, marginRight: 16 }}
            size={'large'}
            value={supplierArchive}
            placeholder={'请选择字段'}
            optionFilterProp={'title'}
            onChange={(selValue) => {
              setSupplierArchive(selValue);
              handleValueChange(payMoney, selValue, payeeInfo, entityId);
            }}
          >
            {textArr?.map((line, index) => {
              return (
                <Option key={index} value={line.name}>
                  {line.label}
                </Option>
              );
            })}
          </Select>
          <div className="">字段</div>
        </div>
      </div>
      {show && (
        <div className="assign_item">
          <div className="label">收款信息</div>
          <div className="right_view">
            <div className="">赋值到单据</div>
            <Select
              style={{ width: 238, marginLeft: 16, marginRight: 16 }}
              size={'large'}
              value={payeeInfo}
              allowClear
              placeholder={'请选择字段'}
              optionFilterProp={'title'}
              onChange={(selValue) => {
                setPayeeInfo(selValue);
                handleValueChange(payMoney, supplierArchive, selValue, entityId);
              }}
            >
              {PayeeInfoArr?.map((line, index) => {
                return (
                  <Option key={index} value={line.name}>
                    {line.label}
                  </Option>
                );
              })}
            </Select>
            <div className="">字段</div>
          </div>
        </div>
      )}

      <div className="assign_item">
        <div className="label">法人实体</div>
        <div className="right_view">
          <div className="">赋值到单据</div>
          <Select
            style={{ width: 238, marginLeft: 16, marginRight: 16 }}
            size={'large'}
            value={entityId}
            allowClear
            placeholder={'请选择字段'}
            optionFilterProp={'title'}
            onChange={(selValue) => {
              setEntityId(selValue);
              handleValueChange(payMoney, supplierArchive, payeeInfo, selValue);
            }}
          >
            {basedataArr?.map((line, index) => {
              return (
                <Option key={index} value={line.name}>
                  {line.label}
                </Option>
              );
            })}
          </Select>
          <div className="">字段</div>
        </div>
      </div>
    </div>
  );
};

export default AssignRule;
