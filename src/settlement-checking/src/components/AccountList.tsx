/*
 * @Description:
 * @Creator: chencan<PERSON>han
 * @Date: 2021-06-24 16:19:21
 */
import React from 'react';
import { Row, Col } from 'antd';
import { app } from '@ekuaibao/whispered';
import AccountItem from './AccountItem';
import AddPayee from '../supplier-dimension/elements/AddPayee';
import { useObserver } from 'mobx-react-lite';
import { useInstance } from '@ekuaibao/react-ioc';
import { SupplierEditVm } from '../supplier-dimension/vms';
import { showMessage } from '@ekuaibao/show-util';
interface AccountListProps {
  keel: any;
}

export const AccountList: React.FC<AccountListProps> = (props) => {
  const vm = useInstance<SupplierEditVm>(SupplierEditVm.NAME);
  const switchStatus = (it) => {
    vm.getSupplierAccountAction(it);
  };
  return useObserver(() => {
    return (
      <Row gutter={{ xs: 8, sm: 16, md: 24 }}>
        {vm.accountList?.map((item) => {
          return (
            <Col key={item?.id} className="col-bottom-16" span={12}>
              <AccountItem
                it={item}
                keel={props?.keel}
                switchStatus={() => {
                  switchStatus(item);
                }}
                onDetail={(it) => {
                  props?.onDetail(it);
                }}
                putSupplierAccount={(res, it) => {
                  vm.putSupplierAccountAction(res, it);
                }}
              />
            </Col>
          );
        })}
        <Col span={12} key={'AddPayee'}>
          <AddPayee
            title={'新增账户信息'}
            onClick={() => {
              if (vm?.originValue?.supplierArchiveType === 'HOSE_TRIP') {
                showMessage.info('不能创建账户');
              } else if (!vm?.originValue?.active) {
                showMessage.info('当前供应商已停用,不能创建账户');
              } else {

                app
                  .open('@settlement-checkin:SupplierAccountModal', {
                    title: i18n.get('新建供应商账户'),
                    supplierArchiveId: vm?.originValue?.id,
                  })
                  .then((res) => {
                    vm.postSupplierAccountAction({ ...res, supplierArchiveType: vm?.originValue?.supplierArchiveType });
                  });
              }
            }}
          />
        </Col>
      </Row>
    );
  });
};

export default AccountList;
