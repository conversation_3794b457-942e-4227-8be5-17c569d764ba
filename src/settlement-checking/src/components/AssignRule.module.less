@import '~@ekuaibao/eui-styles/less/token.less';

.assign-rule-wrapper {
  display: flex;
  width: 100%;
  flex-direction: column;
  font-size: 14px;

  :global {
    .assign_item {
      margin-top: 8px;
      &:first-child{
        margin-top: 0;
      }
      height: 46px;
      opacity: 1;
      background: #f5f5f5;
      border-radius: 6px;
      display: flex;
      align-items: center;
      padding-left: 16px;
      padding-right: 16px;
      .label {
        color: #22b2cc;
        display: flex;
        flex: 1;
      }
      .right_view {
        color: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
      }
    }
  }
}
.assign-rule-others{
  display: flex;
  margin-bottom: 10px;
  .operation {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-left: @space-5;
    flex-shrink: 0;
    .oper {
      cursor: pointer;
      color: var(--brand-base);
    }
    .oper-disabled {
      color: var(--brand-base);
      opacity: 0.5;
    }
  }
}