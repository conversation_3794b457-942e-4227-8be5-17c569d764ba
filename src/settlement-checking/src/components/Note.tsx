import React, { useState } from 'react';
import { Input } from 'antd';
import { useObserver } from 'mobx-react-lite';
import { useInstance } from '@ekuaibao/react-ioc';
import { SupplierEditVm } from '../supplier-dimension/vms';
import { noteValidator } from '../supplier-dimension/utils/form';

interface NoteProps {}

export const Note: React.FC<NoteProps> = () => {
  const vm = useInstance<SupplierEditVm>(SupplierEditVm.NAME);
  const [error, setError] = useState('');
  const handleChange = (e) => {
    const value = e.target.value;
    const err = noteValidator(value);
    setError(err);
    vm.setDescription(e.target.value);
  };
  return useObserver(() => {
    return (
      <div className={}>
        <Input.TextArea
          className={!!error ? 'error-border' : ''}
          value={vm.description}
          autosize={{ minRows: 5, maxRows: 6 }}
          placeholder={i18n.get('请输入备注信息')}
          onChange={handleChange}
        />
        {!!error && <span className="error">{error}</span>}
      </div>
    );
  });
};

export default Note;
