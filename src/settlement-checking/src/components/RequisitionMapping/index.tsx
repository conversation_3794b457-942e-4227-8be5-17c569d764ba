import React from 'react';
import { app } from '@ekuaibao/whispered';
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template';
import { IField } from '@ekuaibao/template/types/Cellar';
import { EnhanceConnect } from '@ekuaibao/store';
import { uuid } from '@ekuaibao/helpers';
import { showMessage } from '@ekuaibao/show-util';
import { BaseMapping, Mapping } from './helper';
import { FeeTypeIF, SpecificationIF } from '@ekuaibao/ekuaibao_types';
import MappingItem from './MappingItem';
import styles from './index.module.less';
const { wrapper } = app.require('@components/layout/FormWrapper');

interface Props extends IField {
  value?: BaseMapping[];
  feeTypes: FeeTypeIF[];
}
interface State {
  mappings: Mapping[];
  requisitionSpecification: SpecificationIF[];
}

const initData = () => ({ id: uuid(8), specificationId: '', feeTypeId: '' });

@((<PERSON>hanceField as any)({
  descriptor: {
    name: 'requisitionMappings',
  },
  validator: (field: IField) => (rule: any, values: any, callback: (err?: string) => void) => {
    callback();
  },
  wrapper: wrapper(),
}))
@EnhanceConnect((state: any) => ({
  feeTypes: state['@common'].feetypes.data,
}))
export default class RequisitionMapping extends React.PureComponent<Props, State> {
  state = {
    requisitionSpecification: [],
    mappings: [initData()],
  };
  async componentDidMount() {
    try {
      const requisitions = await app.invokeService('@settlement:get:support:requisition');
      const reqs: SpecificationIF[] = requisitions.items;
      const { value } = this.props;
      let mappings = [initData()];
      if (!!value?.length) {
        mappings = value
          ?.filter((item) => reqs.some((ele) => ele.id === item.specificationId))
          ?.map((item) => {
            return {
              id: uuid(8),
              ...item,
            };
          });
      }
      !mappings.length && this.props.onChange?.([]);
      this.setState({ requisitionSpecification: reqs, mappings: mappings.length ? mappings : [initData()] });
    } catch (e) {
      e.message && showMessage.error(e.message);
    }
  }

  handleChange = (index: number, item: Mapping) => {
    const result = [...this.state.mappings];
    result.splice(index, 1, item);
    this.props.onChange?.(result);
    this.setState({ mappings: result });
  };

  handleAdd = (index: number) => {
    const result = [...this.state.mappings];
    result.splice(index + 1, 0, initData());
    this.props.onChange?.(result);
    this.setState({ mappings: result });
  };
  handleDel = (index: number) => {
    const result = [...this.state.mappings];
    result.splice(index, 1);
    this.props.onChange?.(result);
    this.setState({ mappings: result });
  };

  render() {
    const { feeTypes } = this.props;
    const { requisitionSpecification, mappings } = this.state;
    return (
      <div className={styles['requisition-mapping-wrapper']}>
        {mappings.map((item, index) => {
          return (
            <MappingItem
              requisitions={requisitionSpecification}
              feeTypes={feeTypes}
              item={item}
              mappings={mappings}
              onChange={(ele) => this.handleChange(index, ele)}
              add={() => this.handleAdd(index)}
              del={() => this.handleDel(index)}
            />
          );
        })}
      </div>
    );
  }
}
