import React, { memo } from 'react';
import { T } from '@ekuaibao/i18n';
import { Select } from 'antd';
import { app } from '@ekuaibao/whispered';
import { SelectValue } from 'antd/es/select';
import { FeeTypeIF, SpecificationIF } from '@ekuaibao/ekuaibao_types';
import { Mapping } from './helper';
const EKBIcon = app.require<any>('@elements/ekbIcon');
const FeeTypeSelect = app.require<any>('@elements/feeType-tree-select');

const { Option } = Select;

interface MappingItemProps {
  requisitions: SpecificationIF[];
  feeTypes: FeeTypeIF[];
  item: Mapping;
  mappings: Mapping[];
  onChange: (item: Mapping) => void;
  add: () => void;
  del: () => void;
}

export const MappingItem: React.FC<MappingItemProps> = memo(
  ({ requisitions, feeTypes, item, mappings, onChange, add, del }) => {
    const handleRequisitionChange = (value: SelectValue) => {
      console.log(value);
      item.specificationId = value as string;
      onChange(item);
    };
    const handleFeeTypeChange = (id: string) => {
      item.feeTypeId = id;
      onChange(item);
    };
    const disableDel = mappings.length <= 1;
    const delClass = disableDel ? 'oper-disabled' : 'oper';
    return (
      <div className="requisition-mapping-item" key={item.id}>
        <div className="left">
          <div className="item-title">
            <T name={'关联到：'} />
          </div>
          <Select
            className="left-select"
            allowClear={true}
            defaultValue={item.specificationId ? item.specificationId : undefined}
            placeholder={i18n.get('请选择申请单模版')}
            onChange={handleRequisitionChange}
          >
            {requisitions.map((item) => {
              return (
                <Option key={item.id} value={item.id}>
                  {item.name}
                </Option>
              );
            })}
          </Select>
        </div>
        <div className="right">
          <div className="item-title right-title">
            <T name={'的'} />
          </div>
          <div className="select-feetype">
            <FeeTypeSelect
              useTreeSelectRC
              showFeeTypeCode
              filterOnSearch
              size="large"
              style={{ width: '100%' }}
              disabledCheckedFather
              multiple={false}
              treeCheckable={false}
              feeTypes={feeTypes}
              checkedKeys={item.feeTypeId ? [item.feeTypeId] : undefined}
              onChange={handleFeeTypeChange}
            />
          </div>
        </div>
        <div className="operation">
          <EKBIcon name="#EDico-plus-default" className="oper mr-8" onClick={add} />
          <EKBIcon name="#EDico-scan-b" className={delClass} onClick={() => (disableDel ? () => {} : del())} />
        </div>
      </div>
    );
  },
  (pre, nex) => {
    return (
      pre.requisitions === nex.requisitions &&
      pre.feeTypes === nex.feeTypes &&
      pre.mappings.length === nex.mappings.length
    );
  },
);

export default MappingItem;
