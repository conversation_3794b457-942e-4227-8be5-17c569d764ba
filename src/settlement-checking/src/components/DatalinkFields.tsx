/**
 *  Created by pw on 2021/7/26 下午5:14.
 */
import React, { Component } from 'react';
import { app } from '@ekuaibao/whispered';
import { EnhanceField, IExtendBus } from '@ekuaibao/template';
import { IField } from '@ekuaibao/template/types/Cellar';
const { wrapper } = app.require('@components/layout/FormWrapper');
const FieldConfig = app.require<any>(
  '@third-party-manage/data-interconnection/data-entity-modal/FieldConfig/FieldConfig',
);

interface Props {
  value?: any;
  field?: IField;
  bus: IExtendBus;
  isEdit?: boolean;
  tag: any;
}

const limitFieldType = [
  'text',
  'date',
  'dateRange',
  'number',
  'switcher',
  'money',
  'ref:datalink.DataLinkEntity',
  'ref:basedata.Dimension',
  'ref:basedata.city',
  'ref:organization.Department',
  'ref:organization.Staff',
  'list:organization.Staff',
  '',
  'ref',
  'list',
  'list:ref:basedata.Dimension'
];

// @ts-ignore
@EnhanceField({
  descriptor: {
    name: 'datalinkFields',
  },
  validator: (field: IField) => (rule: any, value: any, callback: (err?: string) => void) => {
    callback();
  },
  wrapper: wrapper(),
})
export default class DatalinkFields extends Component<Props> {
  render() {
    const { tag, bus, isEdit } = this.props;
    return (
      <FieldConfig
        value={tag?.fields}
        bus={bus}
        isEdit={isEdit}
        nameLabel={i18n.get('产品类型')}
        canEditName={isEdit}
        limitFieldType={limitFieldType}
        allEntityList={tag?.allEntityList}
        showOptional={false}
        editModeValidate={true}
      />
    );
  }
}
