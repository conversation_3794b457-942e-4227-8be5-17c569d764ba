/**
 *  Created by pw on 2021/6/2 下午9:59.
 */
import React, { useEffect, useState } from 'react';
import { Popconfirm, Select, Tooltip } from 'antd';
import './SplitRule.less';
import { EnhanceField } from '@ekuaibao/template';
import { IField } from '@ekuaibao/template/types/Cellar';
import { app } from '@ekuaibao/whispered';
import { GlobalFieldIF } from '@ekuaibao/ekuaibao_types';
import { T } from '@ekuaibao/i18n';
import { uuid } from '@ekuaibao/helpers';

const { Option } = Select;
const SVG_ADD = require('../images/add.svg');
const SVG_DELETE = require('../images/delete.svg');
const SVG_GROUP = require('../images/delete-icon.svg');
const { wrapper } = app.require<any>('@components/layout/FormWrapper');
import { global_blackList } from '@ekuaibao/lib/lib/lib-util';
const blackList = global_blackList.concat(['system_statement', 'supplierReconciliation', 'supplierSettlement']);
const departLevel = [
  { label: i18n.get('末级部门'), value: '-1' },
  { label: i18n.get('第一级部门'), value: '1' },
  { label: i18n.get('第二级部门'), value: '2' },
  { label: i18n.get('第三级部门'), value: '3' },
  { label: i18n.get('第四级部门'), value: '4' },
  { label: i18n.get('第五级部门'), value: '5' },
  { label: i18n.get('第六级部门'), value: '6' },
  { label: i18n.get('第七级部门'), value: '7' },
  { label: i18n.get('第八级部门'), value: '8' },
  { label: i18n.get('第九级部门'), value: '9' },
  { label: i18n.get('第十级部门'), value: '10' },
]
const defaultDepartLevel = departLevel[0]!.value
const SplitSymbol = '##'

interface Props {
  value: Array<Array<string[]>>;
  onChange?: (values: any) => void;
}

interface SplitRuleGroupIF {
  id: string;
  items: SplitRuleIF[];
}

interface SplitRuleIF {
  id: string;
  value: string;
  level?: string;
}

const SplitRule: React.FC<Props> = (props) => {
  const { value: rules = [], onChange } = props;
  const [globalFields, setGlobalFields] = useState<GlobalFieldIF[]>([]);
  const [groups, setGroups] = useState<Array<SplitRuleGroupIF>>([]);
  useEffect(() => {
    fetchData();
  }, []);

  const buildData = (globalFields: GlobalFieldIF[]) => {
    if (rules.length) {
      const data: any = rules.map((rule) => {
        return {
          id: uuidForId(),
          items: rule.map((r: any) => {
            const hasLevel = r.includes(SplitSymbol)
            if (hasLevel) {
              const values = r.split(SplitSymbol);
              return { id: uuidForId(), value: values[0], level: values[1] };
            } else {
              const field = globalFields.find(el => el.name === r)
              if (field?.dataType?.type === 'ref' && field?.dataType?.entity === 'organization.Department') {
                return { id: uuidForId(), value: r, level: defaultDepartLevel };
              }
              return { id: uuidForId(), value: r };
            }
          }),
        };
      });
      setGroups(data);
    } else {
      setGroups([defaultGroup()]);
    }
  };

  const handleValueChange = (changeGroups: Array<SplitRuleGroupIF>) => {
    if (onChange) {
      const values = changeGroups.map((rule: any) => {
        return rule.items.map((rule: SplitRuleIF) => {
          return rule?.level === undefined || rule?.level === defaultDepartLevel ? rule?.value : `${rule?.value}${SplitSymbol}${rule?.level}`;
        }, []);
      });
      onChange(values);
    }
  };

  const fetchData = async () => {
    const { data } = await app.dataLoader('@common.globalFields').load();
    const globleFields = data
      .filter((field: GlobalFieldIF) => !field?.ability || !field?.ability.length)
      .filter((field: GlobalFieldIF) => !~blackList.indexOf(field.name));
    setGlobalFields(globleFields);
    buildData(globleFields);
  };

  const defaultGroup = () => {
    return { id: uuidForId(), items: [{ id: uuidForId(), value: '' }] };
  };

  const handleAddGroup = () => {
    const changeGloups: Array<SplitRuleGroupIF> = groups.concat([defaultGroup()]);
    setGroups(changeGloups);
    handleValueChange(changeGloups);
  };

  const handleDelGroup = (groupId: string) => {
    const changeGroups = groups.filter((group) => group.id !== groupId);
    setGroups(changeGroups);
    handleValueChange(changeGroups);
  };

  const handleGroupPriority = (changeIndex: number, groupId: string) => {
    const currentIndex = groups.findIndex((group) => group.id === groupId);
    if (changeIndex !== currentIndex) {
      const last = groups[changeIndex];
      groups[changeIndex] = groups[currentIndex];
      groups[currentIndex] = last;
      const changeGroups = groups.slice();
      setGroups(changeGroups);
      handleValueChange(changeGroups);
    }
  };

  const handleChangeGroup = (group: SplitRuleGroupIF) => {
    const index = groups.findIndex((g) => g.id === group.id);
    groups[index] = group;
    const changeGroups = groups.slice();
    setGroups(changeGroups);
    handleValueChange(changeGroups);
  };

  return (
    <div className="split-rule-wrapper">
      {groups.map((group, index) => {
        return (
          <SplitRuleGroup
            key={group.id}
            groupIndex={index}
            group={group}
            groups={groups}
            globalFields={globalFields}
            onDelGroup={handleDelGroup}
            onGroupPriority={handleGroupPriority}
            onChangeGroup={handleChangeGroup}
          />
        );
      })}
      <div className="add-or" onClick={handleAddGroup}>
        <T name={'添加条件组'} />
      </div>
    </div>
  );
};

const SplitRuleGroup: React.FC<SplitRuleGroupProps> = (props) => {
  const { groups, group, globalFields, onDelGroup, onGroupPriority, groupIndex, onChangeGroup } = props;

  const handlePriority = (value: number, id: string) => {
    onGroupPriority(value, id);
  };

  const handleAddRow = () => {
    group.items.push({ id: uuidForId(), value: '' });
    onChangeGroup(group);
  };

  const handleDelRow = (rowId: string) => {
    group.items = group.items.filter((item) => item.id !== rowId);
    onChangeGroup(group);
  };

  const handleChangeRule = (changeRule: SplitRuleIF) => {
    const ruleIndex = group.items.findIndex((rule) => rule.id === changeRule.id);
    group.items[ruleIndex] = changeRule;
    onChangeGroup(group);
  };

  return (
    <div className="split-rule-group-wrapper">
      <div className="split-rule-group-title-wrapper">
        <div className="left">
          <Select
            className="priority-select"
            size={'large'}
            defaultValue={groupIndex}
            onChange={(value) => handlePriority(value as number, group.id)}
          >
            {groups.map((group, index) => {
              return <Option key={index} value={index}>{`优先级${index + 1}`}</Option>;
            })}
          </Select>
        </div>
        {groups.length > 1 && (
          <Popconfirm title={<T name={'确定删除此条件组？'} />} onConfirm={() => onDelGroup(group.id)}>
            <img src={SVG_GROUP} className="del-icon" />
          </Popconfirm>
        )}
      </div>
      <div className="split-rule-group">
        {group.items.map((rule, index) => {
          return (
            <SplitRuleRow
              key={rule.id}
              rule={rule}
              rowIndex={index}
              rules={group.items}
              globalFields={globalFields}
              onChangeRule={handleChangeRule}
              onAddRow={handleAddRow}
              onDelRow={handleDelRow}
            />
          );
        })}
      </div>
    </div>
  );
};

const SplitRuleRow: React.FC<SplitRuleRowProps> = (props) => {
  const { rowIndex, rule: defaultRule, rules, globalFields, onChangeRule, onAddRow, onDelRow } = props;
  const [dataSource, setDataSource] = useState<Record<string, GlobalFieldIF[]>>({});
  const [nullFeildData, setNullFieldData] = useState<GlobalFieldIF[]>(globalFields);
  const [rule, setRule] = useState<SplitRuleIF>(defaultRule);
  const [showDepartLevel, setShowDepartLevel] = useState<boolean>(false);
  useEffect(() => {
    fnDataSource(globalFields);
  }, [globalFields?.length]);

  const fnDataSource = (data = globalFields) => {
    const valueMap = rules.reduce((result: Record<string, string>, rule) => {
      result[rule.value] = rule.value;
      return result;
    }, {});
    const mapData: Record<string, GlobalFieldIF[]> = {};
    rules.forEach((rule) => {
      const tempMap = Object.assign({}, valueMap);
      delete tempMap[rule.value];
      mapData[rule.value] = data.filter((item) => !tempMap[item.name]);
    });
    const nullFeildData = data.filter((item) => !valueMap[item.name]);
    setNullFieldData(nullFeildData);
    setDataSource(mapData);
  };

  const handleAddAnd = () => {
    onAddRow();
  };

  const handleDel = (value: string) => {
    if (rules.length <= 1) {
      return;
    }
    onDelRow(rule.id);
    if (rule.value.length) {
      fnDataSource(globalFields);
    }
  };

  const handleRuleChange = (value: string) => {
    const line = data.find(el => el.name === value);
    let newRule: SplitRuleIF;
    if (line?.dataType?.type === 'ref' && line?.dataType?.entity === 'organization.Department') {
      setShowDepartLevel(true);
      newRule = { ...rule, value, level: defaultDepartLevel };
    } else {
      setShowDepartLevel(false);
      rule.hasOwnProperty('level') && delete rule.level;
      newRule = { ...rule, value };
    }
    setRule(newRule);
    onChangeRule(newRule);
    fnDataSource(globalFields);
  };
  const handleDepartLevelChange = (value: string) => {
    setRule({ ...rule, level: value });
    onChangeRule({ ...rule, level: value });
  }
  const data = dataSource[rule.value] || nullFeildData;

  return (
    <div className="split-rule-row-wrapper">
      <div className="split-rule-row">
        {rowIndex > 0 && (
          <div className="and">
            <T>且</T>
          </div>
        )}
        <Select
          style={{ width: '100%' }}
          size={'large'}
          showSearch
          value={rule?.value ? rule?.value : undefined}
          placeholder={'请选择字段'}
          optionFilterProp={'title'}
          onChange={(selValue) => handleRuleChange(selValue as string)}
        >
          {data.map((line, index) => {
            return (
              <Option key={index} title={line.label} value={line.name}>
                {line.label}
              </Option>
            );
          })}
        </Select>
        <div className="operator">
          <img src={SVG_ADD} onClick={handleAddAnd} />
          <Tooltip title={i18n.get('请至少保留一个条件')} trigger="click">
            <img src={SVG_DELETE} onClick={() => handleDel(rule.value)} />
          </Tooltip>
        </div>
      </div>
      {
        (showDepartLevel || rule?.level) && (
          <div className="split-rule-row-level">
            <Select
              style={{ width: '100%' }}
              size={'large'}
              showSearch
              value={rule?.level ? rule?.level : defaultDepartLevel}
              placeholder={i18n.get('请选择等级')}
              optionFilterProp={'title'}
              onChange={(line: any) => handleDepartLevelChange(line)}
            >
              {departLevel.map((line, index) => {
                return (
                  <Option key={line.value} title={line.label} value={line.value}>
                    {line.label}
                  </Option>
                );
              })}
            </Select>
          </div>
        )
      }
    </div>
  );
};

const uuidForId = (): string => uuid(8);

interface SplitRuleGroupProps {
  groupIndex: number;
  groups: SplitRuleGroupIF[];
  group: SplitRuleGroupIF;
  globalFields: GlobalFieldIF[];
  onDelGroup: (groupId: string) => void;
  onGroupPriority: (changeIndex: number, groupId: string) => void;
  onChangeGroup: (group: SplitRuleGroupIF) => void;
}

interface SplitRuleRowProps {
  rowIndex: number;
  rule: SplitRuleIF;
  rules: SplitRuleIF[];
  globalFields: GlobalFieldIF[];
  onAddRow: () => void;
  onDelRow: (rowId: string) => void;
  onChangeRule: (rule: SplitRuleIF) => void;
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    name: 'rules',
  },
  validator: (field: IField) => (rule: any, values: string[][], callback: any) => {
    if (!values?.length) {
      return callback('请填写规则');
    }
    if (!!rule?.level) {
      const emptyList = values.filter((cur: any) => {
        return !!cur.filter((item: string) => item?.length === 0).length;
      }, []);
      if (emptyList?.length) {
        return callback('请填写规则');
      }
    }
    return callback();
  },
  wrapper: wrapper(),
})
export default class SplitRuleCP extends React.Component<Props> {
  render() {
    return <SplitRule {...this.props} />;
  }
}
