/*
 * @Author: <PERSON>
 * @Date: 2022-04-20 14:08:02
 * @LastEditTime: 2022-04-26 11:52:23
 * @LastEditors: Hunter
 * @Description:
 * @FilePath: \plugin-web-settlement-checking\src\components\AssignRuleOthers.tsx
 * 可以输入预定的版权声明、个性签名、空行等
 */
import React from 'react';
import { Select, Tooltip } from 'antd';
import style from './AssignRule.module.less';
import { cloneDeep } from 'lodash';
import { app } from '@ekuaibao/whispered';
const EKBIcon = app.require<any>('@elements/ekbIcon');
const { Option } = Select;
interface IProps {
  [key: string]: any;
}
const checkMsg = (value: any, ruleList: any) => {
  if (value.length >= ruleList.length) {
    return i18n.get('没有可添加的费用字段');
  }
  if (value.length >= 3) {
    return i18n.get('最多添加三个条件');
  }
  return false;
};
export default function AssignRuleOthers(props: IProps) {
  const { ruleList, value, onChange } = props;
  const _val = value?.length > 0 ? cloneDeep(value) : [''];
  const handleAdd = (item: any, i: number) => {
    const _r = ruleList.filter((i: any) => !_val.includes(i.name));
    _val.push(_r[0]?.name ?? '');
    onChange(_val);
  };
  const handleDel = (item: any, i: number) => {
    _val.splice(i, 1);
    onChange(_val);
  };
  const onSelectChange = (v: any, i: number) => {
    _val[i] = v;
    onChange(_val);
  };
  const msg = checkMsg(_val, ruleList);
  return (
    <>
      {_val?.map((item: any, i: number) => {
        return (
          <div className={style['assign-rule-others']} key={i}>
            <Select placeholder={'请选择字段值'} value={item} onChange={(v: any) => onSelectChange(v, i)}>
              {ruleList.map((line: any, index: number) => {
                return (
                  <Option key={index} disabled={_val.includes(line.name)} value={line.name}>
                    {line.label}
                  </Option>
                );
              })}
            </Select>
            <div className={style['operation']}>
              {msg ? (
                <Tooltip title={msg} trigger="click">
                  <EKBIcon name="#EDico-plus-default" className="oper mr-8" />
                </Tooltip>
              ) : (
                <EKBIcon name="#EDico-plus-default" className="oper mr-8" onClick={() => handleAdd(item, i)} />
              )}
              {_val.length === 1 ? (
                <Tooltip title={i18n.get('请至少保留一个条件')} trigger="click">
                  <EKBIcon name="#EDico-scan-b" className="oper" />
                </Tooltip>
              ) : (
                <EKBIcon name="#EDico-scan-b" className="oper" onClick={() => handleDel(item, i)} />
              )}
            </div>
          </div>
        );
      })}
    </>
  );
}
