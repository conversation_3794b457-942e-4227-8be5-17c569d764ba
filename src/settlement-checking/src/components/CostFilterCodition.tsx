/**
 *  Created by pw on 2021/7/21 上午11:03.
 */
import React, { Component } from 'react';
import { app } from '@ekuaibao/whispered';
import { EnhanceField, IExtendBus } from '@ekuaibao/template';
import { IField } from '@ekuaibao/template/types/Cellar';
const { wrapper } = app.require('@components/layout/FormWrapper');
const { required } = app.require('@components/validator/validator');
import { Select } from 'antd';
const { Option } = Select;
import './CostFilterCodition.less';
import { getSupplierAccountList, getSupplierAccount } from '../setttlement-checkin-action';
import { QuerySelect } from 'ekbc-query-builder';
import { ICategoryId, ISupplierAccountDetail, SupplierAccountIF } from '@ekuaibao/ekuaibao_types';
import { SelectValue } from 'antd/es/select';
import { showModal } from '@ekuaibao/show-util';

interface Props {
  value?: any;
  bus: IExtendBus;
  onChange: (value: any) => void;
}

interface State {
  supplierAccounts: SupplierAccountIF[];
  categories: ICategoryId[];
  categoryId: string | undefined;
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'cost-filter-condition',
  },
  validator: (field: IField) => (rule: any, value: any, callback: (err?: string) => void) => {
    if (!value?.dataLinkEntityId) {
      callback(i18n.get('请输入筛选数据'));
      return;
    }
    if (!value?.supplierAccountId) {
      callback(i18n.get('请输入筛选数据'));
      return;
    }
    // @ts-ignore
    callback(required({ ...field, label: i18n.get('筛选数据') }, value));
  },
  wrapper: wrapper(),
})
export default class CostFilterCodition extends Component<Props, State> {
  state = {
    supplierAccounts: [] as SupplierAccountIF[],
    categories: [] as ICategoryId[],
    categoryId: undefined,
  };

  accountMap: Record<string, ISupplierAccountDetail> = {};

  async componentDidMount() {
    const { value } = this.props;
    const query = new QuerySelect()
      .select('supplierArchiveId(id,name),categoryIds(...),...')
      .filterBy(`active==true`)
      .asc('createTime');
    const { items = [] } = await getSupplierAccountList({ ...query.value() });
    if (!!value) {
      const { supplierAccountId, dataLinkEntityId } = value;
      if (supplierAccountId?.id) {
        await this.handleSupplierAccountChange(supplierAccountId?.id, dataLinkEntityId?.id);
        await this.handleCategoryChange(dataLinkEntityId?.id, false);
        this.setState({ categoryId: dataLinkEntityId?.id });
        this.onChange({ dataLinkEntityId: dataLinkEntityId?.id });
      }
    }
    this.setState({ supplierAccounts: items, categoryId: value?.dataLinkEntityId?.id || value?.dataLinkEntityId });
  }

  handleSupplierAccountChange = async (id: SelectValue, dataLinkEntityId?: string) => {
    let accountDetail = this.accountMap[id as string];
    if (!accountDetail) {
      const { value } = await getSupplierAccount(id as string);
      accountDetail = value;
      this.accountMap[id as string] = value;
    }
    const { categoryId } = this.state;
    const categories = accountDetail.categoryIds.filter((line) => !!line);
    const category = categories.find((category) => category.id === categoryId);
    const data: any = { supplierAccountId: id };
    data.dataLinkEntityId = category ? category.id : undefined;
    this.setState({ categories, categoryId: data.dataLinkEntityId });
    this.onChange(data);
    const { bus } = this.props;
    if (!dataLinkEntityId) {
      bus.emit('generate:fee:datalink:change');
    }
  };

  handleCategoryChange = async (id: SelectValue, needSendMsg = true) => {
    const { bus } = this.props;
    const { categories } = this.state;
    const fnUpdateCategory = () => {
      const datalink = categories.find((item) => item.id === id);
      bus.emit('generate:fee:datalink:change', datalink);
      this.setState({ categoryId: id as string });
      this.onChange({ dataLinkEntityId: id });
    };
    if (needSendMsg) {
      const values = await bus.getFieldsValue();
      if (values?.conditions?.length || values?.mappings?.length) {
        showModal.confirm({
          title: i18n.get('确定切换账单?'),
          content: i18n.get('切换账单后，已经填写的「消费与费用映射配置」和「配置赋值规则」中有关账单的数据将会清空'),
          onOk: () => {
            fnUpdateCategory();
          },
        });
      } else {
        fnUpdateCategory();
      }
    }
  };

  onChange = (data = {}) => {
    const { onChange, value = {} } = this.props;
    if (onChange) {
      onChange({ ...value, ...data });
    }
  };

  render() {
    const { value } = this.props;
    const { supplierAccounts = [], categories = [], categoryId } = this.state;
    return (
      <div className="cost-filter-condition-wrapper">
        <div className="cost-filter-condition-wrapper-label w-30 mr-8">选择</div>
        <Select
          defaultValue={value?.supplierAccountId?.id}
          filterOption={true}
          className="cost-filter-condition-select"
          size={'large'}
          showSearch
          optionFilterProp={'title'}
          placeholder={'请选择账单模板'}
          onChange={(selectValue) => this.handleSupplierAccountChange(selectValue)}
        >
          {supplierAccounts.map((account) => {
            return (
              <Option key={account.id} value={account.id} title={account.name}>
                {account.name}
              </Option>
            );
          })}
        </Select>
        <div className="cost-filter-condition-wrapper-label w-70 mr-8">账户账单的</div>
        <Select
          className={'cost-filter-condition-select'}
          value={categoryId}
          size={'large'}
          showSearch
          optionFilterProp={'title'}
          placeholder={'请选择品类'}
          onChange={(selectValue) => this.handleCategoryChange(selectValue)}
        >
          {categories.map((category) => {
            return (
              <Option key={category.id} value={category.id} title={category.name}>
                {category.name}
              </Option>
            );
          })}
        </Select>
        <div className="cost-filter-condition-wrapper-label w-60">商品品类</div>
      </div>
    );
  }
}
