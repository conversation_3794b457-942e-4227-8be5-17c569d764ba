import React, { memo, useEffect, useState } from 'react';
import { DimensionIF, GlobalFieldIF, GlobalFieldMapIF, StaffIF } from '@ekuaibao/ekuaibao_types';
import { fixedValueFieldType, standardValueMoney } from './CostMatchMappingRule/helper';
import moment from 'moment';
import { DatePicker, Input, Switch, Select } from '@hose/eui';
import { isArray, isObject } from '@ekuaibao/helpers';
import { app } from '@ekuaibao/whispered';
const { RangePicker } = DatePicker;
const RefView = app.require<any>('@elements/puppet/Ref');
const FakeInput = app.require<any>('@elements/puppet/FakeInput');

interface IFeild extends GlobalFieldIF {
  withTime?: boolean;
  type?: string;
  customData?: any[];
}

interface Props {
  field: any;
  value?: any;
  type?: string;
  style?: React.CSSProperties;
  globalFieldsMap?: GlobalFieldMapIF;
  needFullData?: boolean;
  datalinkId?: string;
  multiple?: boolean;
  useTreeSelectRC?: boolean;
  onFixedChange: (value: any) => void;
  leftChange?: boolean
}

const FixedValueSelect: React.FC<Props> = (props) => {
  const {
    field: sourceField,
    value,
    globalFieldsMap,
    onFixedChange,
    style,
    needFullData,
    datalinkId,
    multiple,
    useTreeSelectRC,
    type: sourceType,
    leftChange,
    idx
  } = props;
  const field = isObject(sourceField) ? sourceField : globalFieldsMap?.[sourceField];
  const type = sourceType || fixedValueFieldType(field);

  useEffect(() => {
    if (!field && (type === 'date' || type === 'dateRange')) {
      onFixedChange(moment().valueOf());
    }
  }, [type]);

  const typeComponent: Record<string, React.FC<BaseProps>> = {
    date: DateCP,
    dateRange: DateRangeCP,
    money: NumberCP,
    number: NumberCP,
    switcher: SwitchCP,
    'basedata.Dimension': DimensionCP,
    'organization.Department': DimensionCP,
    'organization.Staff': StaffCP,
    'basedata.Enum': DimensionCP,
    'datalink.DataLinkEntity': DatalinkCP,
    'basedata.city': DimensionCP,
  };

  const CP = typeComponent[type!] || DefaultCP;
  return (
    <CP
      onFixedChange={onFixedChange}
      field={field!}
      style={style}
      needFullData={needFullData}
      datalinkId={datalinkId}
      multiple={multiple}
      useTreeSelectRC={useTreeSelectRC}
      value={value}
      leftChange={leftChange}
      idx={idx}
    />
  );
};

interface BaseProps {
  field: IFeild;
  value?: any;
  style?: React.CSSProperties;
  needFullData?: boolean;
  onFixedChange: (value: any) => void;
  datalinkId?: string;
  multiple?: boolean;
  useTreeSelectRC?: boolean;
}

const DimensionCP: React.FC<BaseProps> = (props) => {
  const { value, field, style, needFullData, onFixedChange, multiple = false, leftChange, idx } = props;

  const handleDimensionList = (dimension?: DimensionIF) => {

    if (needFullData) {
      onFixedChange(dimension);
    } else {
      onFixedChange(dimension?.id);
    }
  };
  const customData = field.customData;
  if (customData) {
    const handleCustomChange = (val: string) => {
      const item = customData.find((item) => item.value === val);
      handleDimensionList(item);
    };
    return (
      <Select
        style={style}
        className="right-source-select-ref"
        showSearch={true}
        placeholder={i18n.get('请选择')}
        optionFilterProp="label"
        onChange={(val) => handleCustomChange(val)}
      >
        {customData.map((item: any) => {
          return (
            <Select.Option key={item.value} value={item.value} label={item.label}>
              {item.label}
            </Select.Option>
          );
        })}
      </Select>
    );
  }

  if (leftChange && leftChange?.[idx]) {
    return null
  }
  const data = {
    onChange: handleDimensionList,
    id: isObject(value) ? (value as any)?.id : isArray(value) ? value.map((item: any) => item.id) : value,
    multiple,
    needFullData,
  };
  const param = { name: field?.dataType?.entity };

  return (
    <div style={style} className="right-source-select-ref">
      <RefView param={param} data={data} onlyLeafCanBeSelected={false} useTreeSelectRC={false} useEUI={true} />
    </div>
  );
}

const DateCP: React.FC<BaseProps> = memo((props) => {
  const { style, value, field, onFixedChange } = props;

  useEffect(() => {
    const defaultValue = value ? moment(parseInt(value as string)) : moment();
    onFixedChange(defaultValue.valueOf());
  }, []);

  const handleChange = (e: any) => {
    onFixedChange(moment(e).valueOf());
  };

  const others: any = { format: 'YYYY-MM-DD' };
  if (field.withTime) {
    others.showTime = { format: 'HH:mm' };
    others.format = 'YYYY-MM-DD HH:mm';
  }

  return (
    <DatePicker
      style={style}
      className="right-source-input"
      {...others}
      defaultValue={value ? moment(parseInt(value as string)) : moment()}
      onChange={handleChange}
    />
  );
}, areEqual);

const DateRangeCP: React.FC<BaseProps> = memo((props) => {
  const { style, value, field, onFixedChange } = props;

  const defaultValue = value
    ? [moment(parseInt(value?.start as string)), moment(parseInt(value?.end as string))]
    : [moment(), moment()];

  useEffect(() => {
    onFixedChange({ start: defaultValue[0].valueOf(), end: defaultValue[1].valueOf() });
  }, []);

  const handleChange = (e: any) => {
    const [start, end] = e;
    onFixedChange({ start: moment(start).valueOf(), end: moment(end).valueOf() });
  };
  const others: any = { format: 'YYYY-MM-DD' };
  if (field.withTime) {
    others.showTime = { format: 'HH:mm' };
    others.format = 'YYYY-MM-DD HH:mm';
    // @ts-ignore
    style?.minWidth = 320;
  }

  return (
    <RangePicker
      style={style}
      className="right-source-input"
      defaultValue={defaultValue}
      onChange={handleChange}
      {...others}
    />
  );
}, areEqual);

const NumberCP: React.FC<BaseProps> = memo((props) => {
  const { value, field, onFixedChange } = props;
  const [stateValue, setStateValue] = useState<string | ReadonlyArray<string> | number>('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value: inputValue } = e.target;
    const reg = /^-?\d*(\.\d*)?$/;
    if (reg.test(inputValue) || inputValue === '' || inputValue === '-') {
      setStateValue(inputValue);
      if (field?.type === 'money') {
        onFixedChange(standardValueMoney(inputValue));
      } else {
        onFixedChange(inputValue);
      }
    } else {
      const originalValue = stateValue
      setStateValue(originalValue);
    }
  };

  return (
    <Input
      defaultValue={value}
      value={stateValue}
      className="right-source-input"
      placeholder={i18n.get('请输入')}
      onChange={handleChange}
    />
  );
}, areEqual);

const StaffCP: React.FC<BaseProps> = memo((props) => {
  const { value, field, style, onFixedChange, multiple } = props;
  const handleOnClick = async () => {
    let checkedList = [
      {
        type: 'department-member',
        checkIds: Array.isArray(value) ? value.map(v => v.id) : value?.id ? [value.id] : []
      }
    ]
    app.open('@organizationManagement:SelectStaff', {
      data: checkedList,
      multiple
    }).then((resp: any) => {
      const checkList = resp?.[0].checkList
      const data = !multiple ? checkList?.[0] : checkList
      onFixedChange(data);
    });
  };

  return (
    <div style={style} className="right-source-select-ref">
      <FakeInput onClick={handleOnClick} placeholder={`${i18n.get('请选择')}${field?.label || ''}`}>
        {isArray(value) ? value.map((item: StaffIF) => item.name)?.join(',') : value?.name}
      </FakeInput>
    </div>
  );
}, areEqual);

const DatalinkCP: React.FC<BaseProps> = memo((props) => {
  const { value, field, style, datalinkId, onFixedChange } = props;
  const [data, setData] = useState({} as any);

  // useEffect(() => {
  //   const useDatalinkId = field.referenceData;
  //   if (useDatalinkId) {
  //     app
  //       .invokeService('@third-party-manage:get:entity:detail:data', { dataLinkId: useDatalinkId, type: 'DETAIL' })
  //       .then((res: any) => {
  //         setData({ ...res.value.data });
  //       });
  //   }
  // }, []);

  const handleOnClick = async () => {
    return app
      .open('@bills:SelectDataLinkModal', {
        entityInfo: { dataLink: { id: datalinkId }, referenceData: { id: datalinkId } },
        isDetail: false,
        formValue: value,
      })
      .then(({ data }: any) => {
        setData(data);
      });
  };

  return (
    <div style={style} className="right-source-select-ref">
      <FakeInput onClick={handleOnClick} placeholder={i18n.get('请选择')}>
        {data?.dataLink?.[`E_${data?.dataLink?.entityId}_name`]}
      </FakeInput>
    </div>
  );
}, areEqual);

const SwitchCP: React.FC<BaseProps> = memo((props) => {
  const { onFixedChange } = props;
  const handleChange = (checked: boolean) => {
    onFixedChange(checked);
  };
  return <Switch className="right-source-input" onChange={handleChange} />;
}, areEqual);

const DefaultCP: React.FC<BaseProps> = memo((props) => {
  const { value, onFixedChange } = props;
  const handleChange = (e: any) => {
    const value = e.target.value.trim();
    onFixedChange(value);
  };
  return (
    <Input
      defaultValue={value as string}
      className="right-source-input"
      placeholder={i18n.get('请输入')}
      onChange={handleChange}
    />
  );
}, areEqual);

function areEqual(prevProps: BaseProps, nextProps: BaseProps) {
  return prevProps?.value === nextProps.value;
}

export default FixedValueSelect;
