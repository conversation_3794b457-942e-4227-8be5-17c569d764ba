import React from 'react';
import { Select } from 'antd';
import style from './AssignRule.module.less';
import { cloneDeep } from 'lodash';
const { Option } = Select;

interface Props {
  value?: Array<ISource<string>>;
  onChange?: (values: any) => void;
  optionsList: any;
  rules: Array<string>;
  ruleList: Array<any>;
  readonly bus: any;
  [key: string]: any;
}
export interface ISource<T> {
  targetField: T;
  sourceField: T;
}
const _sourceData: Array<ISource<string>> = [
  { targetField: '', sourceField: 'payMoney' },
  { targetField: '', sourceField: 'supplierArchive' },
  { targetField: '', sourceField: 'payeeInfo' },
  { targetField: '', sourceField: 'entityId' },
  // { targetField: '', sourceField: 'expenseDepartment' },
];
const arr2obj = (value: any, target: any = {}) => {
  value?.forEach((it: any) => (target[it?.sourceField] = it?.targetField));
  return target;
};
const formatRule = (value: Array<any>, show: any, isSettlementDimension: any) =>
  value.filter((item: any) => {
    if (isSettlementDimension && item.sourceField === 'entityId') return false;
    if (!show && item.sourceField === 'payeeInfo') return false;
    return true;
  });
const getSourceData = (sourceData: any, target: any = _sourceData) =>
  sourceData.filter((item: any) => target.find((i: any) => i.sourceField === item.sourceField));
const checkfieldType = (data: any) => {
  if (data?.fieldType === 'organization.Department') return 'department';
  if (data?.fieldType?.startsWith('basedata.Dimension')) return 'basedataDimension';
  return '';
};
const getOtherSourceData = (
  rules: Array<string>,
  ruleList: Array<any>,
  sourceData: Array<any>,
  _val: any,
  onChange: any,
  sourceDataOther: Array<any> = [],
  optionList: Array<any> = [],

) => {
  const ruleItemElemsOther = ruleList
    .filter((item: any) => rules.includes(item.name))
    .map((item: any) => {
      sourceDataOther.push({
        targetField: sourceData.find((i: any) => i.sourceField === item.name)?.targetField ?? '',
        sourceField: item.name,
      });
      return {
        ...item,
        value: _val[item.name],
        optionList: optionList[checkfieldType(item) as any] ?? [],
        onChange,
      };
    });
  return { ruleItemElemsOther, sourceDataOther };
};
const ZY_AssignRule: React.FC<Props> = (props) => {
  const { value = [], onChange, optionsList, show, rules = [], ruleList = [], isSettlementDimension } = props;
  const { textArr = [], PayeeInfoArr = [], moneyArr = [], basedataArr = [] } = optionsList;
  const sd = formatRule(value?.length > 0 ? value : _sourceData, show, isSettlementDimension)
  let sourceData = cloneDeep(sd);
  const _val = arr2obj(sourceData)
  const changeSelect = (selValue: any, name: string) => {
    const index = sourceData.findIndex((i) => i.sourceField === name);
    if (index > -1) {
      sourceData[index].targetField = selValue;
    }
    onChange && onChange(sourceData);
  };
  const { sourceDataOther, ruleItemElemsOther } = getOtherSourceData(
    rules,
    ruleList,
    sourceData,
    _val,
    changeSelect,
    [],
    optionsList,
  );
  let ruleItemElems = [
    {
      name: 'payMoney',
      label: '对账单合计总额',
      value: _val['payMoney'],
      optionList: moneyArr,
      show: true,
      onChange: changeSelect,
    },
    {
      name: 'supplierArchive',
      label: '供应商名称',
      value: _val['supplierArchive'],
      optionList: textArr,
      show: true,
      onChange: changeSelect,
    },
    {
      name: 'payeeInfo',
      label: '收款信息',
      value: _val['payeeInfo'],
      optionList: PayeeInfoArr,
      show,
      onChange: changeSelect,
    },
    {
      name: 'entityId',
      label: '法人实体',
      value: _val['entityId'],
      optionList: basedataArr,
      show: !isSettlementDimension,
      onChange: changeSelect,
    },
    // {
    //   name: 'expenseDepartment',
    //   label: '费用承担部门',
    //   value: _val['expenseDepartment'],
    //   optionList: department,
    //   show: isSettlementDimension,
    //   onChange: changeSelect,
    // },
  ];
  ruleItemElems = ruleItemElems.concat(ruleItemElemsOther);
  sourceData = getSourceData(sourceData).concat(sourceDataOther);


  return (
    <div className={style['assign-rule-wrapper']}>
      {ruleItemElems?.map((item: IAssignRuleItemProps) => (
        <ZY_AssignRuleItem {...item} key={item.label} />
      ))}
    </div>
  );
};

export default ZY_AssignRule;
interface IAssignRuleItemProps {
  name: string;
  value: any;
  label: string;
  optionList: Array<any>;
  show?: boolean;
  onChange: (selValue: any, name: string) => void;
  [key: string]: any;
}
const ZY_AssignRuleItem: React.FC<IAssignRuleItemProps> = (props) => {
  const { value, optionList, label, onChange, show = true, name } = props;
  return (
    <>
      {show && (
        <div className="assign_item">
          <div className="label">{i18n.get(label)}</div>
          <div className="right_view">
            <div>{i18n.get('赋值到单据')}</div>
            <Select
              style={{ width: 238, marginLeft: 16, marginRight: 16 }}
              size={'large'}
              value={value}
              placeholder={i18n.get('请选择字段')}
              onChange={(e) => onChange(e, name)}
            >
              {optionList?.map((line, index) => {
                return (
                  <Option key={index} value={line.name}>
                    {line.label}
                  </Option>
                );
              })}
            </Select>
            <div>{i18n.get('字段')}</div>
          </div>
        </div>
      )}
    </>
  );
};
