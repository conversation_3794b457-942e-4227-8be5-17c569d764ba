/**
 *  Created by pw on 2021/6/2 下午7:40.
 */
import React from 'react';
import { Dynamic } from '@ekuaibao/template';
import { Form } from 'antd';
import Loading from '@ekuaibao/loading';
import { IExtendBus } from '@ekuaibao/template/src/Bus';
import { IComponentDef, IField } from '@ekuaibao/template/src/Cellar';
import { app } from '@ekuaibao/whispered';
const { editable } = app.require('@components/index.editable');

function create(
  T:
    | React.ComponentClass<import('antd/lib/form').FormComponentProps, any>
    | React.SFC<import('antd/lib/form').FormComponentProps>,
) {
  return Form.create({
    withRef: false,
    onValuesChange(props, values) {
      console.log(values);
    },
  })(T);
}

interface Props {
  template: IField[];
  elements?: IComponentDef[];
  bus: IExtendBus;
  layout?: any;
  value?: any;
  tags?: any;
  [key: string]: any;
}

const DynamicWrapper: React.FC<Props> = (props) => {
  const { bus, layout, value, template, elements = [], tags, ...others } = props;
  const cps = elements.concat(editable);
  return (
    <Dynamic
      isNewStyle={true}
      bus={bus as IExtendBus}
      create={create}
      layout={layout}
      value={value}
      loading={Loading}
      template={template}
      elements={cps}
      tags={tags}
      {...others}
    />
  );
};
export default DynamicWrapper;
