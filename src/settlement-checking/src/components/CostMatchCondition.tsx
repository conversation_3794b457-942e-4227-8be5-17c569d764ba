/**
 *  Created by pw on 2021/6/4 下午5:54.
 */
import React, { Component } from 'react';
import { <PERSON>hanceField } from '@ekuaibao/template';
import { app } from '@ekuaibao/whispered';
const { wrapper } = app.require('@components/layout/FormWrapper');
import { EnhanceConnect } from '@ekuaibao/store';
import MessageCenter from '@ekuaibao/messagecenter';
const { required } = app.require('@components/validator/validator');
const ConditionalEditWrapper = app.require<any>('@custom-specification/CustomSpecification/DataLinkConditional');
import { isEqual } from 'lodash';
import { IField } from '@ekuaibao/template/types/Cellar';
import { getDatalinkFields } from '../helpers/FeeGenerateConfigUtil';
import { DatalinkIF } from '@ekuaibao/ekuaibao_types';

interface Props {
  bus: MessageCenter;
  value?: any;
  tag?: any;
  onChange?: (value: any) => void;
}
interface State {
  entityInfoList: any[];
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'cost-match-condition',
  },
  validator: (field: IField) => (rule: any, value: string, callback: () => void) => {
    // @ts-ignore
    callback(required({ ...field, label: i18n.get('消费与费用映射配置') }, value));
  },
  wrapper: wrapper(),
})
@EnhanceConnect((state: any) => ({
  baseDataProperties: state['@common'].globalFields.data,
}))
export default class CostMatchCondition extends Component<Props, State> {
  conditional: any[];
  state = { entityInfoList: [] };

  async componentDidMount() {
    const { value = [[{}]], tag, bus } = this.props;
    this.conditional = value;
    bus.on('generate:fee:datalink:change', this.handleDataLinkChange);
    const entityInfoList: any[] = getDatalinkFields(tag?.dataLinkEntity);
    this.setState({ entityInfoList });
  }

  componentWillUnmount() {
    const { bus } = this.props;
    bus.un('generate:fee:datalink:change', this.handleDataLinkChange);
  }

  handleDataLinkChange = async (datalink: DatalinkIF) => {
    const { onChange } = this.props;
    this.conditional = [];
    if (onChange) {
      onChange([]);
    }
    const entityInfoList: any[] = getDatalinkFields(datalink);
    this.setState({ entityInfoList: entityInfoList.slice() });
  };

  private handleConditionalChange = (value: any) => {
    const { onChange } = this.props;
    if (!isEqual(value, this.conditional)) {
      this.conditional = value;
      onChange && onChange(value);
    }
  };

  render() {
    const { value } = this.props;
    const { entityInfoList } = this.state;
    const filterEntityInfoList = entityInfoList.filter((el: any) => {
      const type = el?.elemType?.type
      const entity = el?.elemType?.entity
      const isDimensions = el?.type === 'list' && type === 'ref' && entity?.startsWith('basedata.Dimension.')
      if (!isDimensions) return el
    })
    return (
      <div className="cost-match-condition-wrapper">
        <ConditionalEditWrapper
          style={{ width: '100%' }}
          useOriginalData={true}
          value={value}
          canDelAllCondition={true}
          entityInfoList={filterEntityInfoList}
          isClear={true}
          onChange={this.handleConditionalChange}
          onUpdateValues={() => {}}
        />
      </div>
    );
  }
}
