/**
 *  Created by pw on 2021/7/26 上午11:35.
 */
import React, { Component } from 'react';
import { app } from '@ekuaibao/whispered';
import { EnhanceField, IExtendBus } from '@ekuaibao/template';
import { IField } from '@ekuaibao/template/types/Cellar';
const { wrapper } = app.require('@components/layout/FormWrapper');
const { required } = app.require('@components/validator/validator');
import { EnhanceConnect } from '@ekuaibao/store';
import { Select } from 'antd';
import { SelectValue } from 'antd/lib/select';
import { DatalinkIF, FeeTypeIF, GlobalFieldIF } from '@ekuaibao/ekuaibao_types';
import { ComponentIF } from '@ekuaibao/ekuaibao_types/src/Specification';
const { Option } = Select;

interface Props {
  value?: any;
  field?: IField;
  bus: IExtendBus;
  tag: any;
  globalFieldsMap: Record<string, GlobalFieldIF>;
  onChange: (value: any) => void;
}

interface State {
  dataSource: GlobalFieldIF[];
  feeType: FeeTypeIF;
  datalink: DatalinkIF;
  selectValue: string | undefined;
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    name: 'relationField',
  },
  validator: (field: IField) => (rule: any, value: any, callback: (err?: string) => void) => {
    // @ts-ignore
    callback(required({ ...field, label: i18n.get('关联字段') }, value));
  },
  wrapper: wrapper(),
})
@EnhanceConnect((state: any) => ({
  globalFieldsMap: state['@common'].globalFields.baseDataPropertiesMap,
}))
export default class RelationFieldComponent extends Component<Props, State> {
  state = {
    dataSource: [] as GlobalFieldIF[],
    feeType: {} as FeeTypeIF,
    datalink: {} as DatalinkIF,
    selectValue: undefined,
  };

  async componentDidMount() {
    const { bus, value, tag } = this.props;
    bus.on('cost:match:feetype:change', this.handleFeeTypeChange);
    bus.on('generate:fee:datalink:change', this.handleDatalinkChange);
    if (!!value && !!tag?.feeTypeId) {
      const { items } = await app.invokeService('@custom-feetype:getFeetypeTemplateById', tag?.feeTypeId);
      const [feeTypeSP] = items;
      this.handleFeeTypeChange(feeTypeSP);
      this.handleDatalinkChange(tag?.dataLinkEntity);
      this.setState({ selectValue: value });
    }
  }

  componentWillUnmount() {
    const { bus } = this.props;
    bus.un('cost:match:feetype:change', this.handleFeeTypeChange);
    bus.on('generate:fee:datalink:change', this.handleDatalinkChange);
  }

  fnOnChange = (value: string | undefined) => {
    const { onChange } = this.props;
    if (onChange) {
      onChange(value);
    }
  };

  fnUpdateDataSource = () => {
    const { globalFieldsMap } = this.props;
    let { datalink, feeType, selectValue } = this.state;
    let changeValue: string | undefined = selectValue;
    if (!datalink) {
      this.handleSelectChange(undefined);
      this.setState({ dataSource: [] });
      return;
    }
    const { id } = datalink;
    const { expenseComponents } = feeType as any;
    if (!id || !expenseComponents?.length) {
      return;
    }
    const relationField = (expenseComponents as ComponentIF[])
      .filter((cp) => {
        const globleField = globalFieldsMap[cp.field];
        const entityId = globleField?.dataType?.entity?.split('.')?.pop();
        return entityId === id;
      })
      .map((cp) => {
        return globalFieldsMap[cp.field];
      });
    const fnUpdateValue = () => {
      const [first] = relationField?.length ? relationField : [];
      changeValue = first?.name;
      this.fnOnChange(first?.name);
    };
    if (selectValue) {
      const field = relationField.find((field) => field.name === selectValue);
      if (!field) {
        fnUpdateValue();
      }
    } else {
      fnUpdateValue();
    }
    this.setState({ dataSource: relationField, selectValue: changeValue });
  };

  handleFeeTypeChange = (feeType: FeeTypeIF) => {
    this.setState({ feeType }, () => {
      this.fnUpdateDataSource();
    });
  };

  handleDatalinkChange = (datalink: DatalinkIF) => {
    const { datalink: datalinkState } = this.state;
    if (datalinkState?.id && datalinkState?.id !== datalink?.id) {
      this.handleSelectChange(undefined);
      this.setState({ selectValue: undefined });
    }
    this.setState({ datalink }, () => {
      this.fnUpdateDataSource();
    });
  };

  handleSelectChange = (value: SelectValue | undefined) => {
    this.fnOnChange(value as string);
    this.setState({ selectValue: value as string });
  };

  render() {
    const { value, field } = this.props;
    const { dataSource, selectValue } = this.state;
    return (
      <Select
        defaultValue={value}
        filterOption={true}
        className="w-100b"
        size={'large'}
        value={selectValue}
        showSearch
        optionFilterProp={'title'}
        placeholder={field?.placeholder}
        onChange={this.handleSelectChange}
      >
        {dataSource.map((field) => {
          return (
            <Option key={field.name} value={field.name} title={field.name}>
              {field.label}
            </Option>
          );
        })}
      </Select>
    );
  }
}
