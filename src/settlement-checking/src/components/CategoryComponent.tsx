/**
 *  Created by pw on 2021/7/21 下午5:16.
 */
import React, { useState, useEffect } from 'react';
import { Button } from 'antd';
import './CategoryComponent.less';
import { T } from '@ekuaibao/i18n';
import { ICategoryId } from '@ekuaibao/ekuaibao_types';

interface Props {
  title?: string;
  accountName?: string;
  currentEntityInfo?: ICategoryId;
  categories?: ICategoryId[];
  className?: string;
  onNewCategory?: () => void;
  onClick?: (category: ICategoryId) => void;
}

export interface ICategory {
  id: string;
  label: string;
}

const CategoryComponent: React.FC<Props> = (props: Props) => {
  const {
    title,
    accountName,
    currentEntityInfo,
    categories: defaultCategories = [],
    className = '',
    onNewCategory,
    onClick,
  } = props;
  const [categories, setCategories] = useState<ICategoryId[]>(defaultCategories);
  useEffect(() => {
    setCategories(defaultCategories);
  }, [defaultCategories?.length]);

  if (!defaultCategories?.length && !onNewCategory) {
    return null;
  }

  const handleOnClick = (category: ICategoryId) => {
    if (onClick) {
      onClick(category);
    }
  };

  const Btn: any = Button;

  return (
    <div className={`category-component-wrapper ${className}`}>
      {title && <div className="category-title">{title}</div>}
      <div className="category-wrapper">
        <div className="category-item-wrapper">
          {categories.map((category) => {
            const cls = currentEntityInfo?.id === category.id ? 'category-select' : 'category-unselect';
            return (
              <div className={`category-normal ${cls}`} key={category.id} onClick={() => handleOnClick(category)}>
                {category?.name?.replace(`${accountName}-`, '')}
              </div>
            );
          })}
        </div>
        {onNewCategory && (
          <Btn type={'primary'} onClick={() => onNewCategory()}>
            <T name={'新建品类'} />
          </Btn>
        )}
      </div>
    </div>
  );
};

export default CategoryComponent;
