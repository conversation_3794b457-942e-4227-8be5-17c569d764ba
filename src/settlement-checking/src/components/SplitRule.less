@import '~@ekuaibao/eui-styles/less/token.less';

.split-rule-wrapper {
  display: flex;
  flex-direction: column;
  .split-rule-group-wrapper {
    margin-bottom: @space-4;
    padding: @space-6 @space-5;
    border-radius: @space-2;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    &:last-child {
      margin-bottom: 0;
    }
    .split-rule-group-title-wrapper {
      margin-bottom: @space-6;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left {
        margin-left: @space-8;
        display: flex;
        align-items: center;
        .title {
          .font-size-2;
          .font-weight-2;
          color: rgba(29, 43, 61, 1);
        }
        .priority-select {
          width: 110px;
          margin-left: @space-4;
        }
      }
      .del-icon {
        width: @space-6;
        height: @space-6;
        cursor: pointer;
      }
    }
    .split-rule-group {
      display: flex;
      flex-direction: column;
    }
    .split-rule-row-wrapper {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
      .split-rule-row {
        padding-left: @space-9;
        padding-right: @space-6;
        display: flex;
        align-items: center;

        .split-rule-row-select {
          display: flex;
          flex: 1;
        }
        .and {
          position: absolute;
          left: @space-4;
          width: @space-6;
          height: @space-6;
          margin-right: @space-4;
          display: flex;
          align-items: center;
          justify-content: center;
          color: @color-white-1;
          flex-shrink: 0;
          text-align: center;
          background: #333333;
        }
      }
      .operator {
        display: flex;
        cursor: pointer;
        margin-left: @space-5;
      }
      .split-rule-row-level {
        .split-rule-row;
        margin-top: 8px;
        padding-right: 60px;
      }
    }
  }
  .add-or {
    .font-size-2;
    .font-weight-2;
    width: @space-12;
    margin-top: @space-4;
    color: var(--brand-base);
    cursor: pointer;
  }
}
