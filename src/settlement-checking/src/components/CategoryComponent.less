@import '~@ekuaibao/eui-styles/less/token.less';

.category-component-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  .category-title {
    .font-size-2;
    .font-weight-2;
    margin-bottom: @space-4;
    color: #142234;
    opacity: 0.76;
  }
  .category-wrapper {
    // height: @space-8;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    justify-content: space-between;
    // overflow-x: hidden;
    .category-item-wrapper {
      margin-right: @space-6;
      display: flex;
      flex-direction: row;
      flex: 1;
      padding-bottom: 10px;
      overflow-x: auto;
      .category-normal {
        .font-size-2;
        .font-weight-2;
        margin-left: @space-4;
        height: @space-8;
        padding: 0 @space-6;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 3px;
        cursor: pointer;
        flex-shrink: 0;
        &:first-child {
          margin-left: 0;
        }
      }
      .category-unselect {
        background: #f5f5f5;
        opacity: 0.76;
        color: #142234;
      }
      .category-select {
        background: @brand-color;
        color: #edf9fb;
      }
    }
  }
}
