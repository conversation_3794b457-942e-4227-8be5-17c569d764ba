import React from 'react';
import { app } from '@ekuaibao/whispered';
import { EnhanceField } from '@ekuaibao/template';
import { IField } from '@ekuaibao/template/types/Cellar';
import { EnhanceConnect } from '@ekuaibao/store';
import { BaseMapping, Mapping } from './helper';
import MappingItem from './MappingItem';
import styles from './index.module.less';
import { getApiPresetFields } from '../../setttlement-checkin-action';
import { cloneDeep } from 'lodash';
const { wrapper } = app.require('@components/layout/FormWrapper');

interface Props extends IField {
  value?: BaseMapping[];
}
interface State {
  mappings: any;
  settleBillFields: any;
  resData: any;
  apiPresetFields: any;
}

const initData = () => ({
  sourceField: '',
  targetElabel: '',
  targetType: 'MATCH',
  targetValue: '',
  sourceEntity: 'datalink.form',
});

@((EnhanceField as any)({
  descriptor: {
    name: 'mappings',
  },
  validator: (field: IField) => (rule: any, values: any, callback: (err?: string) => void) => {
    if (rule.level === 1) {
      return callback();
    }
    if (values) {
      if (values?.length === 0) {
        return callback(`请填写赋值规则`);
      } else {
        for (let item of values) {
          if (!item?.sourceField || !item?.targetElabel) {
            return callback(`赋值规则填写不完整`);
          }
        }
      }
    } else {
      return callback(`请填写赋值规则`);
    }
    return callback();
  },
  wrapper: wrapper(),
}))
@EnhanceConnect((state: any) => ({}))
export default class AssignmentRuleMapping extends React.PureComponent<Props, State> {
  state = {
    mappings: cloneDeep(this.props?.editData?.mappings) || cloneDeep([initData()]),
    settleBillFields: cloneDeep(this.props?.editData?.categoryId?.fields) || [],
    resData: {
      billPlatform: cloneDeep(this.props?.editData?.billPlatform) || '',
      billOrderType: cloneDeep(this.props?.editData?.billOrderType) || '',
    },
    apiPresetFields: {},
  };
  componentDidMount() {
    const { bus, editData } = this.props;
    if (editData?.billPlatform && editData?.billOrderType) {
      this.getApiPresetFieldsInfo(editData);
    }
    bus.on('generate:settle:bill:data', this.handleSettleBillData);
    bus.on('generate:api:fields:data', this.handleApiFieldsData);
    bus.on('generate:supplier:account:change', this.handleSupplierAccountChange);
  }

  componentWillUnmount() {
    const { bus } = this.props;
    bus.un('generate:settle:bill:data', this.handleSettleBillData);
    bus.un('generate:api:fields:data', this.handleApiFieldsData);
    bus.un('generate:supplier:account:change', this.handleSupplierAccountChange);
  }

  getApiPresetFieldsInfo = (resData: { billPlatform: string; billOrderType: string }) => {
    getApiPresetFields(resData).then((res) => {
      this.setState({
        apiPresetFields: res?.value || {},
      });
    });
  };

  handleSettleBillData = (settleBillFields: any) => {
    this.setState({ settleBillFields, mappings: [initData()] });
  };

  handleApiFieldsData = (data: { billPlatform: string; billOrderType: string }) => {
    const { resData } = this.state;

    if (data?.billPlatform) {
      resData.billPlatform = data.billPlatform;
    }
    if (data?.billOrderType) {
      resData.billOrderType = data.billOrderType;
    }
    this.setState({
      resData,
      mappings: [initData()],
      apiPresetFields: {},
    });
    if (resData?.billOrderType && resData?.billPlatform) {
      //获取api预置字段
      this.getApiPresetFieldsInfo(resData);
    }
  };

  handleSupplierAccountChange = async () => {
    const { onChange } = this.props;
    if (onChange) {
      onChange([]);
    }
    this.setState({ mappings: [initData()], settleBillFields: [] });
  };

  handleChange = (index: number, item: Mapping) => {
    const result = [...this.state.mappings];
    result.splice(index, 1, item);
    this.props.onChange?.(result);
    this.setState({ mappings: result });
  };

  handleAdd = (index: number) => {
    const result = [...this.state.mappings];
    result.splice(index + 1, 0, initData());
    this.props.onChange?.(result);
    this.setState({ mappings: result });
  };
  handleDel = (index: number) => {
    const result = [...this.state.mappings];
    result.splice(index, 1);
    this.props.onChange?.(result);
    this.setState({ mappings: result });
  };

  render() {
    const { mappings, settleBillFields, apiPresetFields } = this.state;
    return (
      <div className={styles['requisition-mapping-wrapper']}>
        {mappings.map((item, index) => {
          return (
            <MappingItem
              settleBillFields={settleBillFields?.map((i) => {
                i.disabled = mappings.find((j) => j?.sourceField === i?.name);
                return i;
              })}
              apiPresetFields={apiPresetFields}
              item={item}
              mappings={mappings}
              onChange={(ele) => this.handleChange(index, ele)}
              add={() => this.handleAdd(index)}
              del={() => this.handleDel(index)}
            />
          );
        })}
      </div>
    );
  }
}
