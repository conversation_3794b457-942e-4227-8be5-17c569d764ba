@import '~@ekuaibao/eui-styles/less/token.less';

.requisition-mapping-wrapper {
  display: flex;
  flex-direction: column;
  :global {
    .requisition-mapping-item {
      padding: @space-5 @space-6;
      margin-top: @space-6;
      display: flex;
      background-color: #f7f7f7;
      border-radius: @space-3;
      justify-content: space-between;
      align-items: center;
      &:first-child {
        margin-top: 0;
      }
      .item-title {
        flex-shrink: 0;
        .font-size-2;
        .font-weight-2;
        color: rgba(0, 0, 0, 0.6);
      }
      .left {
        display: flex;
        align-items: center;
        flex: 1;
        .left-select {
          width: 100%;
          .ant-select-selection--single {
            width: 100%;
          }
        }
      }
      .right {
        flex: 1;
        display: flex;
        align-items: center;
        margin-left: @space-4;
        .right-select {
          width: 100%;
          .ant-select-selection--single {
            width: 100%;
          }
        }
        .right-title {
          flex-shrink: 0;
          margin-right: @space-4;
          margin-left: @space-4;
        }
        .select-feetype {
          flex: 1;
        }
      }
      .operation {
        display: flex;
        cursor: pointer;
        margin-left: @space-5;
        flex-shrink: 0;
        .oper {
          cursor: pointer;
          color: var(--brand-base);
        }
        .oper-disabled {
          color: var(--brand-base);
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
}
