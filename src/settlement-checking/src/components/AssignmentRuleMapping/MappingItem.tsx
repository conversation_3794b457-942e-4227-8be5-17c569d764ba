import React, { memo, useEffect, useState } from 'react';
import { T } from '@ekuaibao/i18n';
import { Select } from 'antd';
import { app } from '@ekuaibao/whispered';
import { Mapping } from './helper';
const EKBIcon = app.require<any>('@elements/ekbIcon');

const { Option } = Select;

interface MappingItemProps {
  settleBillFields: any;
  apiPresetFields: any;
  item: Mapping;
  mappings: Mapping[];
  onChange: (item: Mapping) => void;
  add: () => void;
  del: () => void;
}

export const MappingItem: React.FC<MappingItemProps> = memo(
  ({ settleBillFields, apiPresetFields, item, mappings, onChange, add, del }) => {
    const [presetFields, setMappingDetails] = useState([]);

    useEffect(() => {
      handleApiPresetFields(item.sourceField);
    }, [apiPresetFields, item]);

    const handleSettleFieldsChange = (value: any) => {
      item.sourceField = value;
      if (item.targetElabel) {
        item.targetElabel = '';
      }
      handleApiPresetFields(item.sourceField);
      onChange(item);
    };
    const handleApiFieldsChange = (value: { key: string; label: string }) => {
      item.targetElabel = value?.key;
      item.targetClabel = value?.label;
      onChange(item);
    };
    const handleApiPresetFields = (sourceField: any) => {
      if (settleBillFields.length == 0 || !sourceField) return;
      const activeBillFields = settleBillFields?.find((i) => i.name === sourceField);
      if(!!!activeBillFields) return
      let mappingDetails = apiPresetFields?.mappingDetails || [];
      const fieldTypes = ['money', 'date', 'Staff', 'Department'];
      let entity;
      if (activeBillFields?.entity) {
        //员工,部门
        entity = activeBillFields?.entity?.split('.')[1];
      } else if (activeBillFields?.elemType?.entity) {
        //员工,部门多选
        entity = activeBillFields?.elemType?.entity?.split('.')[1];
      }
      if (entity === 'Staff' || entity === 'Department') {
        activeBillFields.type = entity;
      }
      if (activeBillFields?.type === 'dateRange') {
        activeBillFields.type = 'date';
      }
      //不在四种类型里，则统一做text类型处理
      if (!fieldTypes.includes(activeBillFields?.type)) {
        activeBillFields.type = 'text';
      }
      mappingDetails = mappingDetails.filter((j) => j?.labelType === activeBillFields?.type);
      setMappingDetails(mappingDetails);
    };

    const disableDel = mappings.length <= 1;
    const delClass = disableDel ? 'oper-disabled' : 'oper';
    return (
      <div className="requisition-mapping-item" key={item.id}>
        <div className="left">
          <div className="item-title">
            <T name={'对账单字段：'} />
          </div>
          <Select
            className="left-select"
            allowClear={true}
            showSearch
            optionFilterProp={'title'}
            value={item?.sourceField ? item?.sourceField : undefined}
            placeholder={i18n.get('请选择对账单字段')}
            onChange={handleSettleFieldsChange}
          >
            {settleBillFields?.length &&
              settleBillFields.map((item) => {
                return (
                  <Option key={item.name} value={item.name} disabled={item?.disabled} title={item.label}>
                    {item.label}
                  </Option>
                );
              })}
          </Select>
        </div>
        <div className="right">
          <div className="item-title right-title">
            <T name={'取'} />
          </div>
          <Select
            className="right-select"
            labelInValue
            showSearch
            optionFilterProp={'title'}
            allowClear={true}
            value={{
              value: item?.targetClabel ? item?.targetClabel : undefined,
              key: item?.targetElabel ? item?.targetElabel : undefined,
            }}
            placeholder={i18n.get('请选择对账单字段')}
            onChange={handleApiFieldsChange}
          >
            {presetFields?.length &&
              presetFields.map((elm: any) => {
                return (
                  <Option key={elm?.elabel} value={elm?.elabel} title={elm?.clabel}>
                    {elm?.clabel}
                  </Option>
                );
              })}
          </Select>
          <div className="item-title right-title">
            <T name={'字段值'} />
          </div>
        </div>
        <div className="operation">
          <EKBIcon name="#EDico-plus-default" className="oper mr-8" onClick={add} />
          <EKBIcon name="#EDico-scan-b" className={delClass} onClick={() => (disableDel ? () => {} : del())} />
        </div>
      </div>
    );
  },
  (pre, nex) => {
    return (
      pre.settleBillFields === nex.settleBillFields &&
      pre.apiPresetFields === nex.apiPresetFields &&
      pre.mappings.length === nex.mappings.length
    );
  },
);

export default MappingItem;
