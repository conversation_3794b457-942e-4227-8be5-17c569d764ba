@import '~@eku<PERSON>bao/eui-styles/less/token';
.container {
  display: flex;
  flex-direction: column;
  flex: 1;
  :global {
    // .ant-search-input-wrapper{
    //   right: 52px  !important;
    // }
    .text-ellipsis {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .sceneRow {
      display: flex;
      flex-direction: row;
    }
  }
  .header {
    padding: 16px 0 12px;
  }
  .DiDi_header {
    margin: 16px 0 4px;
  }
  .body {
    flex: auto;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 0px; // windows 浏览器兼容
    position: relative;
    max-height: 1000px;
    border-top: 1px solid rgba(29, 43, 61, 0.09);
    :global {
      .search_input {
        right: 16px;
      }
    }
  }
  .grid_wrapper {
    overflow: hidden;
    flex: 1;
  }
  .footer {
    padding: 6px 0;
    display: flex;
    align-items: center;
    background-color: @color-white-1;
  }
  .columnChooser {
    position: absolute;
    width: 26px;
    height: 50px;
    top: 0;
    right: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;
    &:before {
      content: '';
      width: 1px;
      height: 16px;
      position: absolute;
      background: rgba(29, 43, 61, 0.15);
      left: 0;
    }
  }
}
.btn{
  position: absolute;
  top: 10px;
  background: #f7f7f7;
  border-radius: 3px;
  opacity: 0.76;
  font-size: 14px;
  color: #142234;
  right:45px
}


.search-custom{
  left: 12px;
  right: auto !important;
}