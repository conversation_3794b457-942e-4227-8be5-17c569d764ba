import React from 'react';
// @ts-ignore
import styles from './HoseTableWrapper.module.less';
import { app } from '@ekuaibao/whispered';
import { TableWrapperProps } from '@ekuaibao/datagrid/lib/table/TableWrapper';
import { PaginationProps } from '@ekuaibao/datagrid';
import { ColumnChooserProps } from '@ekuaibao/datagrid/esm/widgets/ColumnChooser';
import { SearchProps } from '@ekuaibao/datagrid/esm/Search';
import { ScenesProps } from '@ekuaibao/datagrid/esm/Scenes';
import { Button as EuiBtn, Pagination } from '@hose/eui';
const HoseTable = app.require<any>('@elements/HoseTable/TableResize');
import { ConfigProvider } from '@hose/eui'
import enUS from '@hose/eui/es/locale/en_US';
import zhCN from '@hose/eui/es/locale/zh_CN';
import { Fetch } from "@ekuaibao/fetch";

const withLoader = app.require<any>('@elements/data-grid-v2/withLoader');
const euiLocalValue = Fetch.lang === 'en-US' ? enUS : zhCN;

interface WaitImportListProps {
  tableProps: TableWrapperProps;
  paginationProps?: PaginationProps;
  columnChooserProps?: ColumnChooserProps;
  searchProps?: SearchProps;
  scenesProps?: ScenesProps;
  footerAction?: React.ReactNode;
  exportProps?: any;
  tableLeftAction?: React.ReactNode;
  tableRightAction?: React.ReactNode;
  bodyStyle?: Record<string, string>;
  sceneClassName?: string;
  containerClassName?: string;
  visibleSaveDiff?: React.ReactNode;
}

export const HoseTableWrapper: React.FC<WaitImportListProps> = (props) => {
  const {
    tableProps,
    paginationProps,
    columnChooserProps,
    searchProps,
    scenesProps,
    footerAction,
    children,
    exportProps,
    tableLeftAction,
    tableRightAction,
    bodyStyle = {},
    sceneClassName = '',
    containerClassName = '',
    visibleSaveDiff
  } = props

  return (
    <ConfigProvider
      locale={euiLocalValue}
    >
      <div className={`${styles.container} ${containerClassName}`}>
        {!!scenesProps && (
          <div className={`sceneRow ${sceneClassName}`}>
            <HoseTable.Scenes {...scenesProps} />
          </div>
        )}
        {!!tableProps && (
          <div style={{ ...bodyStyle }} className={styles.body}>
            <HoseTable
              {...tableProps}
              scroll={{ y: 'calc(100vh - 406px)' }}
              toolBarLeft={{
                title: tableLeftAction
              }}
              toolBarRight={{
                columnChooser: columnChooserProps,
                search: searchProps,
                visibleSaveDiff: visibleSaveDiff,
                customChild: tableRightAction
              }}
            />
          </div>
        )}
        {(
          <div className={styles.footer}>
            <div className={styles['footer-left-operate']}>
              {!!footerAction && footerAction}
              {exportProps && (
                <EuiBtn {...exportProps} category="secondary">
                  {i18n.get('导出')}
                </EuiBtn>
              )}
            </div>
            {!!paginationProps && <Pagination {...paginationProps} />}
          </div>
        )}
        {!!children && children}
      </div>
    </ConfigProvider>
  );
};
export default withLoader(() => Promise.resolve({ default: HoseTableWrapper }));
