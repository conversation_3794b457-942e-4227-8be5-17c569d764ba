import loadable from '@loadable/component';
import { getCheckingBill, getSupportRequisition, getConfigSettlementList, getSupplierAccountList } from './setttlement-checkin-action';
export default [
  {
    id: '@settlement-checkin',
    path: '/settlement-checkin-setting',
    ref: '/',
    exact: true,
    onload: () => import('./setting'),
  },
  {
    id: '@hoseTrip-invoiceHub',
    path: '/hoseTrip-invoiceHub',
    ref: '/',
    exact: true,
    onload: () => import('./hoseTrip-invoiceHub-view'),
  },
  {
    id: '@supplier-manager',
    path: '/supplier-manager',
    ref: '/',
    onload: () => import('./supplier-dimension'),
  },
  {
    point: '@@layers',
    prefix: '@settlement-checkin',
    onload: () => require('./layer').default,
  },
  {
    id: '@supplier-account',
    path: '/supplier-account',
    ref: '/',
    onload: () => import('./supplier-account'),
  },
  {
    id: '@supplier-bill',
    path: '/supplier-bill',
    ref: '/',
    onload: () => import('./supplier-bill'),
  },
  {
    id: '@settlement-checking',
    path: '/settlement-checking',
    ref: '/',
    onload: () => import('./settlement-checking'),
  },
  {
    id: '@settlement',
    'get:checking:bill': (id: string) => getCheckingBill(id),
    'get:support:requisition': () => getSupportRequisition(),
    'get:config:settlement:list': getConfigSettlementList,
    'get:supplier:account:list': getSupplierAccountList
  },
  {
    resource: '@settlement',
    value: {
      ['settlementCheckingDetail']: loadable(() => import('./settlement-checking/SettlementCheckingStacker')),
    }
  },
  {
    id: '@departmentReconciliationInfo',
    path: '/department-reconciliation-info',
    ref: '/',
    onload: () => import('./settlement-department'),
  },
];
