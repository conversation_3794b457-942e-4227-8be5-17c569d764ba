/**
 *  Created by pw on 2021/6/2 下午8:58.
 */
import { Fetch, Resource } from '@ekuaibao/fetch';
import { QuerySelect } from 'ekbc-query-builder';
import { showMessage } from '@ekuaibao/show-util';
const checkinAction = new Resource('/api/checking/v2');
// 供应商 账户
const supplierAccount = new Resource('/api/supplier/v3/account');
// 供应商 账户 配置开关
const supplierAccountChecking = new Resource('/api/checking/invoicing/v1');
// 供应商档案
const supplierArchive = new Resource('/api/supplier/v3/archive');
// 供应商账单
const checkingBill = new Resource('/api/checking/v3/checkingBill');
// Excel 导入结果检查
const checkingExcel = new Resource('/api/checking/v3/detail/excel/import');
// 账单详情列表
const statementDetailExtend = new Resource('/api/checking/v3/statementDetailExtend');
// 账单调整日志
const statementDetailLog = new Resource('/api/checking/v3/log');
// 账单调整
const statementAdjustment = new Resource('/api/checking/v3');
// 企业发票抬头列表
const payerInfo = new Resource('/api/v1/corporationset/payerinfo');
// 账期
const billPeriod = new Resource('/api/checking/v3/billPeriod');
// 数据统计
const checkingDetail = new Resource('/api/checking/v2/detail');
// 结算
const settlementAction = new Resource('/api/checking/v2/settlement');
// // 结算单配置
// const configSettlement = new Resource('/api/checking/v2/config/settlement');
// 获取子账单数据：对账单/结算单
const flows = new Resource('/api/flow/v1/flows');
// 对账结算配置列表
const checkingConfig = new Resource('/api/checking/v2/config');
const datalinkAction = new Resource('/api/v2/datalink');
// 保存合思商城字段和品类字段的映射规则
const category = new Resource('/api/checking/v3/category');
const apiSync = new Resource('/api/checking/v3');
const syncBill = new Resource('/api/checking/v3/trip');
const departmentsCheck = new Resource('/api/checking/tobacco/v1/charge');
const deptChecking = new Resource('/api/checking/v1/deptChecking');
const orderMatchConfig = new Resource('/api/checking/v2/orderMatchConfig');
const orderMatch = new Resource('/api/checking/v2/orderMatch');
const travelBasicConfig = new Resource('/api/tpp/v2');
const getProgressAsync = new Resource('/api/v1/excel');

import { getLinkNodeElement, triggerClick } from '@ekuaibao/sdk-bridge/sdk/utils';

/**
 * 获取对账单拆分规则
 */
export function getCheckinList() {
  return checkinAction.GET('/config/split', { active: true });
}

/***
 * 保存拆分规则
 * @param params
 */
export function saveSplitConfig(params: any): Promise<any> {
  const { id } = params;
  return id ? checkinAction.PUT('/config/split', params) : checkinAction.POST('/config/split', params);
}

/**
 * 获取供应商账单配置
 */
export function getStatementConfigList(): Promise<any> {
  return checkinAction.GET('/config/statement');
}

/**
 * 保存供应商账单配置
 * @param params
 */
export async function saveStatementConfig(params: any): Promise<any> {
  const { id } = params;
  if (id) {
    return checkinAction.PUT('/config/statement', params);
  }
  return checkinAction.POST('/config/statement', params);
}

/**
 * 供应商账户添加
 * @param params
 */
export function postSupplierAccount(params: any) {
  return supplierAccount.POST('', params);
}
/**
 * 供应商账户修改
 * @param params
 */
export function putSupplierAccount(params: any) {
  return supplierAccount.PUT('/$id', params);
}
/**
 * 供应商账户停用
 * @param params
 */
export function disableSupplierAccount(params: any) {
  return supplierAccount.PUT('/$id/disable', params);
}
/**
 * 供应商账户启用
 * @param params
 */
export function restoreSupplierAccount(params: any) {
  return supplierAccount.PUT('/$id/restore', params);
}
/**
 * 供应商账户列表
 * @param params
 */
export function getSupplierAccountList(params: any) {
  return supplierAccount.POST('/list', params);
}
/**
 * 供应商账户详情
 * @param id
 */
export function getSupplierAccount(id: string) {
  return supplierAccount.GET('/$id/detail', { id });
}

/**
 * 供应商列表
 */
export function getSupplierArchiveList(params = {}) {
  const query = new QuerySelect(params);
  query.desc('createTime');
  return supplierArchive.POST('/list', query.value());
}
/**
 * 供应商详情
 * @param id
 */
export function getSupplierArchive(id: string) {
  return supplierArchive.GET('/$id/detail', { id });
}
/**
 * 修改供应商 发票抬头和发票类型
 * @param params
 */
export function modifySetting(params: any) {
  return supplierAccount.PUT('/$id/modifySetting', params);
}
/**
 * 商城账户同步
 * @param params
 */
export function syncAccount(params: any) {
  return supplierAccount.PUT('/$id/syncAccount', params);
}
/**
 * 企业发票抬头列表
 */
export function getPayerInfo() {
  return payerInfo.GET();
}

/**
 * 创建供应商档案
 */
export async function createSupplier(value = {}) {
  return supplierArchive.POST('', value);
}
/**
 * 修改供应商档案
 */
export async function modifySupplier(value = {}) {
  return supplierArchive.PUT('/$id', value);
}
/**
 * 获取供应商档案详情
 */
export async function getSupplierInfo(param = {}) {
  return supplierArchive.GET('/$id/detail', param);
}
/**
 * 批量停用供应商档案
 */
export async function batchDisable(ids = []) {
  return supplierArchive.PUT('/disable/batch', { ids });
}
/**
 * 批量启用供应商档案
 */
export async function batchEnable(ids = []) {
  return supplierArchive.PUT('/restore/batch', { ids });
}
/**
 * 单个停用供应商档案
 */
export async function disable(param = {}) {
  return supplierArchive.PUT('/$id/disable', param);
}
/**
 * 单个启用供应商档案
 */
export async function enable(param = {}) {
  return supplierArchive.PUT('/$id/restore', param);
}
/**
 * 获取账期列表
 * */
export async function getBillingPeriodList(params) {
  const query = new QuerySelect(params);
  query.desc('name');
  return billPeriod.POST('/list', query.value());
}

/**
 * 保存账期列表
 * */
export async function saveBillingPeriod(params) {
  return billPeriod.POST('', params);
}
/**
 * 停用账期
 * */
export async function disableBillingPeriod(params) {
  return billPeriod.PUT('/$id/disable', params);
}
/**
 * 启用账期
 * */
export async function restoreBillingPeriod(params) {
  return billPeriod.PUT('/$id/restore', params);
}

/**
 * 供应商账单列表
 */
export function getCheckingBillList(params = {}) {
  const join = {
    join$0: 'id,warn,/checking/v3/checkingBill/log',
  };
  return checkingBill.POST('/list', params, { ...join });
}
/**
 * 供应商账单详情
 * @param id
 */
export function getCheckingBill(id: string) {
  return checkingBill.GET('/$id', { id });
}
/**
 * 供应商账单详情-调整日志
 * @param params
 */
export function searchBudgetLogs(params: any) {
  return statementDetailLog.POST('/search', params)
}
/**
 * 供应商账单详情-发起调整
 * @param params
 */
export function adjustmentBill(params: any) {
  return statementAdjustment.POST('/adjustment', params)
}
/**
 * 供应商账单详情-调整确认
 * @param params
 */
export function adjustmentConfirm(params: any) {
  return statementAdjustment.POST('/adjustment/confirm', params).catch((err) => showMessage.error(i18n.get(err.msg || err.errorMessage)));
}
/**
 * 供应商账单详情-获取账单可调整字段列表
 * @param params
 */
export function adjustableField(entityId: any, checkingBillId: string) {
  return statementAdjustment.GET('/adjustment/adjustableField/$entityId', { entityId, checkingBillId }).catch((err) => showMessage.error(i18n.get(err.msg || err.errorMessage)));
}
/**
 * 删除 供应商账单
 * @param id
 */
export function deleteCheckingBill(id: string) {
  return checkingBill.DELETE('/$id', { id });
}
/**
 * Excel 导入结果检查
 * @param checkingBillId
 */
export function getCheckingExcel(checkingBillId: string) {
  return checkingExcel.GET('/$checkingBillId/record', { checkingBillId });
}
/**
 * 账单详情列表
 * @param
 */
export function getStatementDetailExtend(params = {}, qs: {} | undefined) {
  return statementDetailExtend.POST('/list', params, qs);
}
/**
 * 账单详情列表
 * @param
 */
export function getAllStatementDetailExtend(params = {}, qs: {} | undefined) {
  return statementDetailExtend.POST('/getAll', params, qs);
}
/**
 * 账单详情列表导出
 * @param
 */
export function exportStatementDetail(params = {}) {
  return statementDetailExtend.POST('/export/async', params);
}

/**
 * 供应商账单新增
 * @param params
 */
export function addCheckingBill(params = {}) {
  return checkingBill.POST('', params);
}
/**
 * 获取明细拆分配置列表
 */
export function getFeeConfigList() {
  return checkingConfig.POST('/detail/list');
}

export function saveFeeGenerateConfig(params: any) {
  const { id } = params;
  return id ? checkinAction.PUT('/config/detail', params) : checkinAction.POST('/config/detail', params);
}
/**
 * 获取企业账单详情信息
 * */
export function getCheckingBillDetail(params) {
  return checkingDetail.GET('/$id', params);
}

/***
 * 获取对账概览统计信息
 * @param params
 */
export function getCheckingStatistics(params) {
  let qs = {};
  if (params?.legalEntityId) {
    qs = { legalEntityId: params?.legalEntityId };
  } else if (params?.expenseDepartment) {
    qs = { expenseDepartment: params?.expenseDepartment };
  }
  return checkingDetail.GET('/statistical/$id/checking', params, qs);
}
/***
 * 获取费用统计信息
 * @param params
 */
export function getFeeTypeStatistics(params) {
  return checkingDetail.GET('/statistical/$id/legalEntity', params);
}
/***
 * 获取开票统计信息
 * @param params
 */
export function getInvoiceStatistics(params) {
  let qs = {};
  if (params?.legalEntityId) {
    qs = { legalEntityId: params?.legalEntityId };
  } else if (params?.expenseDepartment) {
    qs = { expenseDepartment: params?.expenseDepartment };
  }
  return checkingDetail.GET('/statistical/$id/invoice', params, qs);
}

/***
 * 发起对账
 */
export function launchChecking(params) {
  return checkingDetail.POST('/generate/$id', params);
}
/**
 * 查询生成字段账单、发起对账、发起结算的状态
 * @param params
 */
export function checkingSaveBill(params) {
  return checkingDetail.GET('/record/$checkingBillId/$type', params, undefined, { hiddenLoading: true });
}

export function checkingCheckingBillProgress(params: any) {
  return checkingDetail.GET('/progress/$checkingBillId', params, undefined, { hiddenLoading: true });
}

/**
 * 确认报错信息下次查询不再提示
 * @param params
 */
export function confirmError(params) {
  return checkingDetail.PUT('/record/$checkingBillId/$recordId', params);
}
/***
 * 本期不对账
 * @param ids
 */
export function unChecking(ids) {
  return checkingDetail.PUT('/disable/batch', { ids });
}
/***
 * 本期对账账单状态判断
 * @param params
 */
export function getBillState(params) {
  return checkingBill.GET('/confirmStatus/$id', params);
}
/***
 * 确认本期对账
 * @param params
 */
export function confirmChecking(params) {
  return checkingBill.POST('/$id/confirm', params);
}
/***
 * 取消本期对账
 * @param params
 */
export function confirmCancelChecking(params) {
  return checkingBill.POST('/$id/confirmCancel', params);
}
/***
 * 获取自对账单ids
 * @param params
 */
export function getBillIds(params) {
  let qs = {};
  if (params?.legalEntityId) {
    qs = { legalEntityId: params?.legalEntityId };
  } else if (params?.expenseDepartment) {
    qs = { expenseDepartment: params?.expenseDepartment };
  }
  return checkingDetail.GET('/flow/$id', params, qs);
}

/***
 * 子对账单发起对账
 * @param params: {checkingBillId: '' //对账单id, flowIds: [] // 子对账单/单据的id}
 */
export function submitBillChecking(params) {
  return checkingDetail.PUT('/submit', params);
}
/***
 * 删除子对账单
 * @param params: {checkingBillId: '' //对账单id, flowIds: [] // 子对账单/单据的id}
 */
export function deleteBillChecking(params) {
  return checkingDetail.POST('/del', params);
}
export function batchModify(params) {
  return checkingDetail.PUT('/batch', params);
}

/***
 * 结算统计信息
 * @param params {checkingBillId : ''}
 */
export function getSettlementStatisticalInfo(params: any) {
  return settlementAction.GET('/$checkingBillId/statistical', params);
}

/***
 * 结算统计信息
 * @param params {checkingBillId : ''}
 */
export function getSettlementBaseInfo(params: any) {
  return settlementAction.GET('/$checkingBillId/info', params);
}

/***
 * 结算统计信息
 * @param params {checkingBillId : '', entityId: "指定法人实体时不为空，不指定必为空"}
 */
export function generateSettlement(params: any) {
  return settlementAction.POST('/generate', params);
}

/***
 * 获取供应商结算单配置
 * @param params:
 */
export function getConfigSettlementList(params = {}) {
  return checkingConfig.POST('/settlement/list', params);
}
/***
 * 新增结算单配置
 * @param params:
 */
export function setConfigSettlement(params) {
  return checkingConfig.POST('/settlement', params);
}
/***
 * 修改结算单配置
 * @param params:
 */
export function putConfigSettlement(params) {
  return checkingConfig.PUT('/settlement/$id', params);
}
/***
 * 删除结算单配置
 * @param params:
 */
export function deleteConfigSettlement(params) {
  return checkingConfig.PUT('/settlement/$id/disable', params);
}

/***
 * 获取结算单的单据id
 * @param params
 */
export function getSettlementBIllIds(params) {
  return settlementAction.POST('/flow/$checkingBillId', params);
}

export function getBills(params) {
  return flows.POST('/search', params);
}
/***
 * 提交结算单
 * @param params : { "checkingBillId": "", "flowIds": [] }
 */
export function submitSettlement(params: any) {
  return settlementAction.POST('/submit', params);
}
/***
 * 删除结算单
 * @param params : { "checkingBillId": "", "flowIds": [] }
 */
export function delSettlementBill(params: any) {
  return settlementAction.POST('/del', params);
}

/***
 * 获取结算概览
 * @param params
 */
export function getSettlemetStatistics(params: any) {
  let qs = {};
  if (params?.legalEntityId) {
    qs = { legalEntityId: params?.legalEntityId };
  } else if (params?.expenseDepartment) {
    qs = { expenseDepartment: params?.expenseDepartment };
  }
  return checkingDetail.GET('/statistical/$id/settlement', params, qs);
}

/***
 * 根据账单ID获取品类
 * @param params : { accountId:"" }
 */
export function getCategoryByAccountId(params: any) {
  return checkingDetail.GET('', params);
}

/**
 * 保存(新增or修改 包括预置机酒火车在内的 和 用户自定义的）账单模板、商品品类及关联
 * @param params
 */
export function saveUserBillTemplate(params: any) {
  return supplierAccount.POST('/$supplierAccountId/saveUserBillTemplate', params);
}

/**
 * 获取商城字段映射规则
 * @param params
 */
export function getFieldsMapping(params: any) {
  return category.GET('/apiMapping/$entityId', params);
}
/**
 * 报错商城字段和品类字段映射规则
 * @param params
 */
export function saveFieldsMapping(params: any) {
  return category.POST('/updateApiMapping', params);
}

/**
 * 获取预置的机酒火车账单模板列表
 */
export function listSystemBillTemplates() {
  return supplierAccount.GET('/listSystemBillTemplates');
}

export function removeBillTemplate(params: any) {
  return supplierAccount.DELETE('/$supplierAccountId/$dataLinkEntityId/removeBillTemplate', params);
}

/**
 * 获取业务对象应用数据
 * @param params
 */
export function fetchDatalinkReferenceData(params: any) {
  const join = {
    join$0: 'platformId,platformId,/v2/datalink/platform',
  };
  return datalinkAction.GET('/entity/$id', { ...params, ...join });
}

/***
 * 费用生成规则停启用
 * @param params {id:string,active:bool}
 */
export function changeDetailConfigActive(params: any) {
  return checkinAction.PUT('/config/detail/$id/$active', params);
}

export function refreshCheckingBills(params: { checkingBillId: string }) {
  return checkingBill.POST('/$checkingBillId/refresh', params);
}

/**
 *  检查费用生成子对账单进度
 * @param params { checkingBillId: string }
 */
export function checkCheckingBillProgress(params: { checkingBillId: string }) {
  return checkingBill.GET('/progress/$checkingBillId', params);
}

/**
 * 获取对账单模版可以关联的申请单模版
 */
export function getSupportRequisition() {
  return checkingConfig.GET('/support/requisition');
}

/***
 * 保存API同步对账单配置
 * @param params
 */
export function saveApiSyncConfig(params: any): Promise<any> {
  const { id } = params;
  return id ? apiSync.PUT('/syncBill/$id', params) : apiSync.POST('/syncBill', params);
}

/***
 * 删除API同步对账单配置
 * @param params
 */
export function deleteApiSyncConfig(params: any): Promise<any> {
  const { id } = params;
  return apiSync.DELETE('/syncBill/$id', { id });
}

/**
 * 获取API同步对账单配置列表
 */
export function getApiSyncList() {
  return apiSync.GET('/syncBill/list');
}

export function getSyncHoseTrip() {
  return apiSync.GET('/syncBill/checkSyncBill');
}


/**
 * 获取API预置字段
 */
export function getApiPresetFields(params: any) {
  const { billPlatform, billOrderType } = params;
  return apiSync.GET(`/$billPlatform/$billOrderType/preset`, { billPlatform, billOrderType });
}
// KA-烟草行业对账结算 获取数据
export function getDepartementCheckConfig(id: any) {
  return departmentsCheck.GET('', { corpId: id });
}
// 手动同步对账单
export function manuallySyncBill(params: any) {
  return syncBill.POST('/manual/import', params);
}
// 部门对账本期对帐完成
export function settlementSuccess(params: any) {
  return deptChecking.POST('/confirm', params);
}
// 获取部门对账明细汇总金额
export function getFeeDetailAmount(id: any) {
  return checkingDetail.GET('/feeDetailAmount/$id', { id });
}
// 撤回部门对账本期对帐
export function settlementCancel(params: any) {
  return deptChecking.POST('/cancelConfirm', params);
}
// 部门对账概览
export function depSettlement(id: any) {
  return deptChecking.GET('/$id', { id });
}

// 新增订单匹配配置
export function addOrderMatchConfig(params: any) {
  return orderMatchConfig.POST('', params);
}

// 获取订单匹配配置列表
export function getOrderMatchConfigList(params: any) {
  return orderMatchConfig.GET('/list', params);
}

// 停启用订单匹配配置
export function activeOrderMatchConfig(params: any) {
  const { id, operate } = params;
  return orderMatchConfig.POST('/enable', { id, operate });
}

// 编辑订单匹配配置
export function setOrderMatchConfig(params: any) {
  return orderMatchConfig.PUT('/$id', params);
}

// 获取订单匹配配置详情
export function getOrderMatchConfig(params: any) {
  return orderMatchConfig.GET('/$id', params);
}
// 发起订单匹配任务
export function matchTaskLaunch(params: any) {
  return orderMatch.POST('/matchTaskLaunch', params);
}
// 手动匹配匹配订单
export function manualMatchTask(params: any) {
  return orderMatch.POST('/manualMatchTask', params);
}
// 解绑订单匹配
export function matchUnbind(params: any) {
  return orderMatch.POST('/matchUnbind/$checkingDetailId', params);
}

// 确认订单匹配任务
export function matchConfirm(params: any) {
  return orderMatch.POST('/matchConfirm/$checkingBillId', params);
}
// 是否存在正在匹配订单任务
export function existMatchTask(params: any) {
  return orderMatch.GET('/existMatchTask/$checkingBillId', params);
}
// 是否允许进入对账
export function enterChecking(params: any) {
  return orderMatch.GET('/enterChecking/$id', params);
}
// 重新同步部门账单
export function depSyncSettlement(params: any) {
  return syncBill
    .POST(`/manual/department/import`, params, { hiddenLoading: true })
    .catch((err) => showMessage.error(i18n.get(err.msg || err.errorMessage)));
}
// 部门同步账单记录列表
export function depSyncSettlementHistory(params: any) {
  return syncBill
    .POST(`/manual/department/record`, params, { hiddenLoading: true })
    .catch((err) => showMessage.error(i18n.get(err.msg || err.errorMessage)));
}
/**
 * 获取吉利/携程基础配置
 */
export function getApiSyncBasicConfig(params: any) {
  const { billPlatform } = params;
  return travelBasicConfig.GET(`/travel/$billPlatform/switch`, { billPlatform });
}
/**
 * 获取商旅对账单同步配置
 */
export function getBillSwitchConfig(params: any) {
  return travelBasicConfig.GET(`/travel/$billPlatform/switch/billSwitch`, params);
}

/**
 * 获取操作日志
 * @param params {checkingBillId:'对账单ID'}
 */
export function getCheckBillLog(params: any) {
  return checkingBill.GET('/log/$checkingBillId', params);
}
export function execlImportCheck(params: any) {
  return apiSync
    .POST(`/detail/excel/check/$checkingBillId`, params)
    .catch((err) => showMessage.error(i18n.get(err.msg || err.errorMessage)));
}

export function execlImportAnalysis(params: any) {
  return apiSync.POST(`/detail/excel/analysis/$checkingBillId`, params).catch((err) => {
    showMessage.error(i18n.get(err.msg || err.errorMessage));
    return err;
  });
}

export function execlImportUnique(params: any) {
  return apiSync.POST(`/detail/excel/unique/$checkingBillId`, params).catch((err) => {
    showMessage.error(i18n.get(err.msg || err.errorMessage));
    return err;
  });
}

export function getProgress(params: any) {
  return getProgressAsync
    .GET(`/getProgress`, params, null, { hiddenLoading: true })
    .catch((err) => showMessage.error(i18n.get(err.msg || err.errorMessage)));
}

export function exportSupplierExcel(params: Record<string, any>) {
  const corpId = encodeURIComponent((Fetch as any).ekbCorpId);
  const el = getLinkNodeElement();
  const url = `${Fetch.fixOrigin(location.origin)}/api/supplier/v3/archive/excel/export?corpId=${corpId}&accessToken=${Fetch.accessToken
    }`;
  fetch(url, {
    method: 'POST',
    body: JSON.stringify(params),
    headers: new Headers({
      Accept: 'application/octet-stream',
      'Content-Type': 'application/json',
    }),
  })
    .then((res) =>
      res.blob().then((blob) => {
        const url = window.URL.createObjectURL(blob);
        const filename = res.headers.get('Content-Disposition')?.split(';')[1].split('=')[1];
        const name = decodeURIComponent(filename as string);
        el.setAttribute('href', url);
        el.setAttribute('download', name);
        triggerClick(el);
      }),
    )
    .catch((err) => showMessage.error(i18n.get(err.msg || err.errorMessage)));
}

export function checkSupplierImportError() {
  return supplierArchive.GET('/excel/error').catch((err) => showMessage.error(i18n.get(err.msg || err.errorMessage)));
}

export function deleteSupplierErrorExcel() {
  return supplierArchive
    .DELETE('/delete/excel/error')
    .catch((err) => showMessage.error(i18n.get(err.msg || err.errorMessage)));
}

export function supplierAccountBillPersiodRefresh(params: any) {
  return supplierAccount
    .GET('/$id/billPeriod/refresh', params)
    .catch((err) => showMessage.error(i18n.get(err.msg || err.errorMessage)));
}

export function supplierAccountConfig() {
  return supplierAccountChecking
    .GET('/isOpen')
    .catch((err) => showMessage.error(i18n.get(err.msg || err.errorMessage)));
}
