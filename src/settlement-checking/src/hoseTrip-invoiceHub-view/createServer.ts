import { Fetch } from '@ekuaibao/fetch'
import { Server } from '@ekuaibao/rpc'
import { app as api } from '@ekuaibao/whispered'
import { formatTemplate } from './templateHelp'
import { get } from 'lodash'

const specificationJoin = () => ({
  join: `components.assignmentRule,assignmentRule,/v1/mapping/fieldMapping`,
  join$1: `components.referenceData,referenceData,/v2/datalink/entity?join=platformId,platformId,/v2/datalink/platform`
})

class Services {
  app: any
  constructor(app: any) {
    this.app = app
  }
  'Mall:Bills' = (data: any) => {
    api?.invokeService('@layout5:active:menu', 'myBill')
    return data
  }
  'Mall:CheckBill' = (data: any) => {
    api?.invokeService('@layout5:active:menu', 'myBill')
    setTimeout(() => api?.go(`/bills/Check/${data?.flowId}`), 1000)
    return data
  }
  'Mall:EditBill' = (data: any) => {
    api?.invokeService('@layout5:active:menu', 'myBill')
    setTimeout(() => api?.go(`/bills/Edit/${data?.flowId}`), 1000)
    return data
  }
  'Mall:NewBill' = (data: any) => {
    api?.invokeService('@layout5:active:menu', 'myBill')
    setTimeout(() => api?.go(`/bills/New/null`), 1000)
    return data
  }
  'Mall:customSpecification' = (data: any) => {
    api?.invokeService('@layout5:active:menu', 'customSpecification')
    setTimeout(() => api?.go(`/custom-specification/${data?.id}`), 1000)
    return data
  }
  'Web:Router' = (path: string) => {
    return api.go(path)
  }
  'Mall:Open' = (url: string) => {
    return api.emit('@vendor:open:link', url)
  }
  'Mall:template' = async (resp: any = {}) => {
    const defaultDetailsShowFields = [
      'text',
      'textarea',
      'ref:organization.Staff',
      'ref:organization.Department',
      'ref:basedata.Dimension',
      'dataLink'
    ]

    const {
      flowId,
      specificationId,
      mallField = [],
      value = {},
      defaultFields = {},
      detailsShowFields = defaultDetailsShowFields,
      companyRealPayFromOrder,
      travelerFromOrder,
    } = resp
    const dataFromOrder = {
      companyRealPayFromOrder,
      travelerFromOrder,
      detailsShowFields,
      showImportInPermit: value?.['expenseLinks']?.length > 0,
    }

    if (!specificationId) {
      console.warn('--Mall:template-商城传参错误:-', resp)
      return
    }
    const params = {
      id: specificationId,
      ...specificationJoin()
    }
    const resData = await Fetch.GET(
      `/api/form/v1/specificationVersions/$${specificationId}`,
      params,
    )
    const specification = resData?.value
    let flowForm = undefined
    if (flowId) {
      // 申请单
      const { value } = await api.invokeService('@bills:get:flow-info-lite', { id: flowId })
      flowForm = value?.form
    }
    const { newSpecification, flowValue = {} } = formatTemplate({
      specification,
      flowForm,
      flowId,
      mallField,
      defaultFields,
    })
    const data = {
      title: i18n.get('成本归属'),
      formType: specification?.type,
      state: 'new',
      currentSpecification: newSpecification,
      originSpecification: specification,
      requisitionInfo: undefined,
      flowValue: { ...flowValue, ...value },
      openFrom: 'permit-form', // 商城预置单填单
      flowPlanConfigId: '',
      dataFromOrder,
    }
    api.open('@bills:BillEditableDrawer', {
      data,
      riskData: undefined,
      callback: (res) => {
        const specificationForm = get(res, 'value.specificationForm')
        const specificationMap = get(res, 'value.specificationMap')
        const value = get(res, 'value.value')
        const data = { value, specificationMap, specificationForm }
        console.log(res, '---向商城传数据---', data);
        // 向商城传输局
        let TmcView = document.getElementById('TmcView')
        TmcView?.contentWindow?.postMessage({
          name: 'EKB:template:form',
          data,
        }, '*');
      }
    })
  }
}

export default function createServer(view: any) {
  return new Server(new Services(view))
}
