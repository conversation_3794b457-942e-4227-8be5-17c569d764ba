import './tmc-view-style.less'
import React, { useEffect, useState } from 'react'
import { app as api } from '@ekuaibao/whispered'
import createServer from './createServer'
import { Fetch } from '@ekuaibao/fetch'
import { IllustrationMiddleNetworkFailure } from '@hose/eui-icons'
import { Button } from '@hose/eui'
import Loading from '@ekuaibao/loading'

const FixedTmcView = () => {
  const [sURL, setSURL] = useState('')
  const [showError, setShowError] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const server = createServer()
    server.listen()

    getFrameUrl()

    return () => {
      server.unlisten()
      api.invokeService('@bills:save:mall:redirect:params')
    }
  }, [])

  const getFrameUrl = async () => {
    setLoading(true)
    try {
      const [intentRes, jwtRes] = await Promise.all([
        api.invokeService('@custom-triptype:get:travel:intent', { type: 'MENU_MALL_ADMIN' }),
        api.invokeService('@custom-triptype:get:travel:intent:jwt', { type: 'MENU_MALL_ADMIN' }),
      ])
  
      const items = intentRes?.items || []
      const token = jwtRes?.id || ''
      if (items.length === 0) throw new Error('No items found')
  
      const mallRedirectParams = api.getState('@bills.mallRedirectParams')
      let url = items[0].source
  
      if (mallRedirectParams?.orderTabKey) {
        url = url.replace('wmpc', `wmpc/orderList`)
      } else {
        url = url.replace('wmpc', 'wmpc/')
      }
  
      const desiredPath = '/consume-apply/apply'

      url = url.replace(/\/home\?/, desiredPath)
  
      const typeChar = '?'
      url += `${typeChar}token=${token}&corpId=${Fetch.ekbCorpId}&language=${i18n.currentLocale}`
      if (Fetch.accessToken) {
        url += `&ekbAccessToken=${Fetch.accessToken}`
      }
  
      if (mallRedirectParams?.orderTabKey) {
        url += `&tab=${mallRedirectParams.orderTabKey}`
      }

      setSURL(url)
      setShowError(false)
    } catch (e) {
      console.error('加载失败', e)
      setShowError(true)
    } finally {
      setLoading(false)
    }
  }

  const renderErrorPage = () => (
    <div className="travel-manage-error-page">
      <IllustrationMiddleNetworkFailure className="network-failure-icon" />
      <div className="error-message">{i18n.get('网络错误，请检查网络后重试。')}</div>
      <Button onClick={getFrameUrl}>{i18n.get('刷新重试')}</Button>
    </div>
  )

  if (loading) {
    return <Loading className="dis-f jc-c flex-1 center h-600" color="var(--brand-base)" />
  }

  if (showError) {
    return renderErrorPage()
  }

  return (
    <div id="travel-manage-center" className="tmc-viewer">
      <iframe
        id="TmcView"
        sandbox="allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-same-origin allow-scripts allow-popups allow-downloads"
        src={sURL}
        frameBorder="0"
        style={{ display: 'inline-block', width: '100%' }}
      />
    </div>
  )
}

export default FixedTmcView