@import '~@ekuaibao/web-theme-variables/styles/default.less';
@import '~@ekuaibao/web-theme-variables/styles/colors.less';
@import '~@ekuaibao/eui-styles/less/token.less';

.supplier_bill_list {
  width: 100%;
  background-color: rgba(247, 247, 247, 1);
  flex: 1;
  min-width: 1000px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  :global {
    .content_view {
      flex: 1;
      display: flex;
      flex-direction: column;

      .header {
        background: white;
        flex-shrink: 0;
        height: 52px;
        width: 100%;
        display: flex;
        padding: 0 16px;
        justify-content: space-between;
        align-items: center;

        .title {
          font-size: 20px;
          font-weight: 500;
          max-width: 70%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .right_view {
          .ico {
            margin-right: 8px;
          }
        }
      }

      .query_view {
        background-color: white;
        padding: @space-6;

        .row {
          display: flex;

          .item {
            width: 100%;
          }
        }

        .form_item {
          height: 32px;
          margin-bottom: @space-6;
        }
      }

      .lists_view {
        display: flex;
        flex: 1;
        flex-direction: column;
        padding: 0 16px 16px 10px;

        .ic_v {
          position: absolute;
          z-index: 1;
          right: 6px;
          top: 6px;
          width: 32px;
          height: 32px;
          border-radius: 16px;
          cursor: pointer;
          display: flex;
          align-items: center;
          color: #333333;
          justify-content: center;

          .ic {
            width: 20px;
            height: 20px;
          }
        }

        .ic_v:hover {
          color: #f06e6e;
          background: #ffecec;
        }

        .item_view {
          margin-top: 16px;
          color: #999999;
          display: flex;
          flex-direction: column;
          height: 168px;
          border-radius: 6px;
          background: #eaeaea;
          box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          position: relative;
          cursor: pointer;
          flex-shrink: 0;

          .ic_v {
            display: none;
            transition: all 0.3s ease 0s;
          }

          .content_view {
            display: flex;
            flex-direction: column;
            background: #fff;
            flex: 1;
            padding: 10px 16px 0 16px;

            .bill_period {
              display: flex;
              opacity: 0.92;
              font-size: 14px;
              color: #666666;

              .bill-period-warn {
                margin-left: @space-6;
                display: flex;
                align-items: center;

                .warnning-icon {
                  width: @space-6;
                  height: @space-6;
                  color: #faad14;
                }

                .bill-period-warn-text {
                  .font-size-2;
                  .font-weight-2;
                  margin-left: @space-2;
                  color: #faad14;
                }
              }
            }

            .content {
              display: flex;
              align-items: flex-end;

              margin-top: 10px;

              .category_view {
                display: flex;
                flex: 1;
                overflow: auto;
                margin-left: 40px;
                margin-right: 100px;
              }

              .text_ellipsis {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .mb20 {
                margin-bottom: 20px;
              }

              .item_v {
                display: flex;
                flex-direction: column;
                opacity: 0.92;
                font-size: 14px;
                color: #666666;

                .money_xf {
                  font-size: 40px;
                  font-family: DINAlternate, DINAlternate-Bold, sans-serif;
                  font-weight: 700;
                  color: #333333;
                  margin-bottom: -8px;
                }

                .money_sy {
                  font-size: 24px;
                  font-family: DINAlternate, DINAlternate-Bold, sans-serif;
                  font-weight: 700;
                  color: #333333;
                }

                .transportation_view {
                  display: flex;
                  font-size: 14px;
                  color: #666666;
                  align-items: center;

                  .ml8 {
                    margin-left: 8px;
                  }

                  .fj {
                    width: 25px;
                    height: 20px;
                  }

                  .jd {
                    width: 22px;
                    height: 20px;
                  }

                  .hc {
                    width: 18px;
                    height: 20px;
                  }
                }

                .df {
                  display: flex;
                  align-items: center;
                  opacity: 0.92;
                  font-size: 14px;
                  color: #666666;
                }
              }

              .dip {
                width: 1px;
                height: 46px;
                opacity: 1;
                background: #e6e6e6;
              }
            }
          }

          .dip_view {
            cursor: pointer;
            height: 24px;
            background: #f7f7f7;
            text-align: right;
            line-height: 24px;
            opacity: 0.44;
            font-size: 12px;
            color: #142234;
            padding-right: 16px;
          }

          .supplier_view {
            position: absolute;
            left: 0;
            bottom: 0;
            height: 30px;
            display: flex;
            align-items: center;

            .banner {
              position: absolute;
              left: 0;
              top: 0;
              right: 0;
              bottom: 0;
              width: 420px;
            }

            .supplier {
              margin-left: 13px;
              width: 204px;
              opacity: 0.92;
              font-size: 12px;
              color: #ffffff;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-top: 3px;
            }

            .type {
              margin-left: 19px;
              font-size: 12px;
              color: #ffffff;
              display: flex;
              justify-content: center;
              align-items: center;
              z-index: 1;
              margin-top: 3px;

              img {
                margin-right: 4px;
              }
            }
          }
        }

        .item_view:hover {
          transition: all 0.3s ease 0s;

          .ic_v {
            display: flex;
          }
        }

        .order-match-btn {
          position: absolute;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          right: 20px;
          z-index: 999;
          color: var(--brand-base);
          align-items: center;
          line-height: 200%;

          .cant {
            cursor: not-allowed;
            color: #aaa;
          }
        }

        .item_new {
          cursor: pointer;
          margin-top: 16px;
          color: #999999;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 168px;
          border-radius: 6px;
          background: #eaeaea;
          flex-shrink: 0;

          .content {
            display: flex;
            align-items: center;

            .icon {
              width: 30px;
              height: 30px;
            }

            .new {
              margin-left: 16px;
              font-size: 14px;
            }
          }
        }
      }
    }

    .bottom_view {
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      padding-bottom: 16px;

      .ant-pagination-next .ant-pagination-item-link,
      .ant-pagination-prev .ant-pagination-item-link {
        border: none;
        border-radius: 6px;
        width: 32px;
        height: 32px;
      }

      .ant-pagination-next .ant-pagination-item-link:after,
      .ant-pagination-prev .ant-pagination-item-link:after {
        width: 32px;
        height: 32px;
      }

      .ant-pagination-next:not(.ant-pagination-disabled):focus .ant-pagination-item-link,
      .ant-pagination-next:not(.ant-pagination-disabled):hover .ant-pagination-item-link,
      .ant-pagination-prev:not(.ant-pagination-disabled):focus .ant-pagination-item-link,
      .ant-pagination-prev:not(.ant-pagination-disabled):hover .ant-pagination-item-link {
        color: #999999;
        background: #ebecf0;
      }

      .ant-pagination-item {
        border: none;
        border-radius: 6px;
      }

      .ant-pagination-item:hover {
        background: #ebecf0;

        a {
          color: #999999;
        }
      }

      .ant-pagination-item-active {
        background: rgba(9, 30, 66, 0.95);

        a {
          color: white;
        }
      }

      .ant-pagination-item-active:hover {
        background: rgba(9, 30, 66, 0.95);

        a {
          color: white;
        }
      }

      a {
        color: #999999;
        font-size: 16px;
        line-height: 32px;
        text-decoration: none;
      }
    }
  }
}