/*
 * @Description: 供应商账户详情
 * @Creator: chencan<PERSON><PERSON>
 * @Date: 2021-05-28 10:59:52
 */
import React from 'react';
import { app, app as api } from '@ekuaibao/whispered';
import { Form, Input, Select, Button, Tooltip, Icon } from 'antd';
const FormItem = Form.Item;
const { Option } = Select;
import { injectKeel, KeelVM } from '@ekuaibao/keel';
import { get, isBoolean } from 'lodash';
const { billSelectStaff } = api.require('@bills/util/billUtils')
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
import {
  getStatementDetailExtend,
  getAllStatementDetailExtend,
  adjustmentConfirm,
  adjustableField,
  adjustmentBill
} from '../setttlement-checkin-action';
import { getColumns, fnASyncExportAction, columnsFilter } from '../helpers/utils';
import { QuerySelect } from 'ekbc-query-builder';
import { showMessage } from '@ekuaibao/show-util';
import DataTable from './DataTable';
import { parseSorters, parseFilter } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil';
import { parseQuery2Select } from '@ekuaibao/lib/lib/parseQuery2Select';
import { Button as EuiBtn } from '@hose/eui';

import DynamicWrapper from '../components/DynamicWrapper';
const fetchFixer = app.require<any>('@elements/data-grid/fetchFixer').default;
import { Resource } from '@ekuaibao/fetch';
import { T } from '@ekuaibao/i18n';
const receiptResource = new Resource('/api/flow/v2/filter');

interface SupplierBillProps {
  categoryIds: any[];
  hiddenForm?: boolean;
  id: any;
  bus?: any;
  fixCol?: Function;
  showMatch?: boolean;
  columnType?: string;
  supplierArchiveType?: string;
  importMethod?: string;
}
interface SupplierBillState {
  orderType: any;
  checkingBillId: any;
  sorters: any;
  filters: any;
  path: any;
  fieldMap: any;
  columns: any;
  pageMode: any;
  pagination: any;
  totalLength: any;
  dataSource: any;
  originColumns: any;
  visibleColumns: any;
  isVisibleSaveDiff: boolean;
  columnType: string;
  difScenes: any;
  selectedKeys: any;
  selectedDatas: any;
}
// @ts-ignore
@EnhanceFormCreate()
export default class SupplierBill extends React.Component<SupplierBillProps, SupplierBillState> {
  @injectKeel()
  keel: KeelVM | undefined;
  constructor(props) {
    super(props);
    const categoryIds = props?.categoryIds;
    let orderType = '';
    if (categoryIds?.length > 0) {
      orderType = get(categoryIds[0], 'id', '');
    }
    this.state = {
      orderType: orderType,
      checkingBillId: props?.id,
      totalLength: 0,
      pageMode: 'pagination',
      pagination: { current: 1, size: 10 },
      dataSource: [],
      columns: [],
      originColumns: [],
      sorters: {},
      filters: {},
      path: {},
      fieldMap: {},
      visibleColumns: [],
      isVisibleSaveDiff: false,
      selectedKeys: [],
      columnType: props?.columnType || 'STATEMENT_DETAIL_EXTEND',
      selectedDatas: {}
    };
  }

  componentDidMount() {
    this.getList({}, undefined, true);
    let { bus } = this.props;
    bus?.watch('refresh:data', this.handleSearch);
    bus?.watch('element:ref:select:staff', this.fnSelectStaff);
    bus?.watch('element:ref:select:staffs', this.fnSelectStaffs);
  }
  componentWillUnmount() {
    let { bus } = this.props;
    bus?.un('element:ref:select:staff', this.fnSelectStaff);
    bus?.un('element:ref:select:staffs', this.fnSelectStaffs);
    bus?.un('refresh:data', this.handleSearch);
  }
  refreshData = () => {
    this.getList({});
  };
  fnSelectStaff = data => {
    return billSelectStaff(data, data.multiple)
  }

  fnSelectStaffs = data => {
    return billSelectStaff(data, true)
  }
  componentWillReceiveProps(nextProps: any) {
    const { categoryIds = [] } = this.props;
    if (categoryIds?.length === 0) {
      const nextCategoryIds = nextProps.categoryIds;
      if (nextCategoryIds?.length > 0) {
        const orderType = get(nextCategoryIds[0], 'id', '');
        this.setState({ orderType }, () => {
          this.getList({}, undefined, true);
        });
      }
    }
  }
  private containsArray = ['form|E_system_checking_requisitionCode', 'form|E_system_checking_orderNo'];
  private getList = (data, ex = false, isChangeScene = false) => {
    const { orderType, checkingBillId, sorters, filters, path, fieldMap, columns, pageMode, pagination, columnType } =
      this.state;
    const { hiddenForm, showMatch = false, supplierArchiveType, importMethod } = this.props;
    const sor = parseSorters(sorters, path);
    const fil = parseFilter(filters, path);
    if (!orderType) return;
    const q = new QuerySelect()
      .select('`...`')
      .filterBy(`orderType=="${orderType}"`)
      .filterBy(`checkingBillId=="${checkingBillId}"`);
    if (showMatch) {
      q.filterBy(`matchFlag==false`);
    }
    const accountPeriod = 'form.E_system_checking_accountPeriod';
    let accountPeriodQsParam = fil[accountPeriod];
    delete fil[accountPeriod];
    const adjustState = 'form.E_system_checking_adjustState';
    if (fil && fil[adjustState]) {
      q.filterBy(`adjustState.contains("${fil[adjustState]}")`);
      delete fil[adjustState];
    }
    const fixParams = fetchFixer({ filters: fil, sorters: sor }, fieldMap);
    const filkeys = Object.keys(fil) || [];
    this.setState({ selectedKeys: [] });
    const departmentStr = 'form.E_system_checking_costAscriptionDepartment';
    if (filkeys.length && filkeys.includes(departmentStr)) {
      q.filterBy(`${departmentStr}.name.contains("${fil[departmentStr]}")`);
      delete fil[departmentStr];
    }
    const qq = parseQuery2Select(
      {
        sorters: fixParams.sorters || {},
        filters: fixParams.filters || {},
        page: { currentPage: pagination?.current, pageSize: pagination?.size, pageMode: pageMode },
      },
      q,
      columns,
      null,
      null,
      fieldMap,
    );
    const keys = Object.keys(data);
    for (const key of keys) {
      console.log(key)
      const value = get(data, key);
      if (isBoolean(value) || value) {
        if (this.containsArray?.includes(key)) {
          qq.filterBy(`${key?.replace('|', '.')}.contains("${value}")`);
        } else {
          if (key === 'adjustState') {
            qq.filterBy(`${key?.replace('|', '.')}=="${value}"`);
          } else if (key === 'active') {
            qq.filterBy(`${key?.replace('|', '.')}==${value}`);
          } else if (key === 'form|E_system_checking_accountPeriod') {
            accountPeriodQsParam = data['form|E_system_checking_accountPeriod']
          } else {
            qq.filterBy(`${key?.replace('|', '.')}=="${value}"`);
          }
        }
      }
    }
    const obj = { ...qq.value() };
    if (ex) {
      delete obj['limit'];
      fnASyncExportAction({ query: { ...obj }, orderType, exportHead: this.getVisibleColumns() ?? [] })
        .then((res) => {
          showMessage.info(i18n.get('创建异步导出任务成功,您可在右上角「我的个人信息-导出管理」中查看任务进度。'));
        })
        .catch((err) => {
          showMessage.error(err?.errorMessage || err?.message);
        });
    } else {
      let defer = getStatementDetailExtend;
      if (hiddenForm) {
        defer = getAllStatementDetailExtend;
      }
      defer(obj, { entityId: orderType, accountPeriod: accountPeriodQsParam })
        .then((res) => {
          const items = get(res, 'items');
          const componentMap: any = {};
          if (items?.components?.length) {
            items.components.forEach((cp: any) => {
              componentMap[cp.field] = cp;
            });
          }
          const totalLength = get(items, 'total', 0) || 0;
          const dataSource = get(items, 'data', []) || [];
          if (isChangeScene) {
            const template = get(items, 'template');
            const path = get(items, 'path');
            const { columns } = getColumns({ template, path });

            const col = columnsFilter(columns);
            const defaultVisibleColumns = col.map((item) => item.dataIndex);
            const arr = get(template, 'content.selectedFields') || [];
            let fieldMap: Record<string, any> = {};
            for (const el of arr) {
              const key = `form.${el?.name}`;
              el.dataType = { type: el.type, entity: el.entity };
              const cp = componentMap[el.name];
              if (cp && cp.referenceData) {
                el.referenceData = cp.referenceData;
              }
              fieldMap[key] = el;
            }

            // 如果是API的，表格会加入多选框，多选框在列比较多时第一次会显示不出来，变更一下咧就可以了
            const needUpdateColumn = col?.length > 20;
            let temColumns = col;
            if (needUpdateColumn) {
              temColumns = col.slice(0, col.length - 1);
            }

            this.setState(
              {
                totalLength,
                dataSource,
                columns: temColumns,
                visibleColumns: defaultVisibleColumns,
                originColumns: col,
                path,
                fieldMap: fieldMap,
              },
              () => {
                if (needUpdateColumn) {
                  this.setState({ columns: col });
                }
              },
            );
            //判断是否有设置过表头字段的展示，有的话以设置的为准
            receiptResource.GET('/$type', { type: columnType }).then((scenesRes) => {
              if (scenesRes?.value && scenesRes?.value?.filter?.length > 0) {
                const sceneInfo = scenesRes?.value?.filter.map((v) => JSON.parse(v));
                this.setState({ difScenes: sceneInfo });
                const activeSceneInfo = sceneInfo.find((item: any) => item?.sceneIndex === orderType) || {};
                if (activeSceneInfo?.defaultColumns) {
                  this.setState({
                    visibleColumns: activeSceneInfo?.defaultColumns,
                  });
                }
              }
            });
          } else {
            this.setState({ totalLength, dataSource, fieldMap });
          }
        })
        .catch((err) => {
          showMessage.error(err?.errorMessage || err?.message);
        });
    }
  };
  getVisibleColumns() {
    const { visibleColumns = [], columns = [] } = this.state;
    return columns
      .filter((it: any) => visibleColumns.includes(it.value))
      .map((it: any) => ({ label: it.label, name: it.dataIndex }));
  }
  private handleSearch = ({ ex = false, isChangeScene = false }) => {
    const {
      form: { validateFieldsAndScroll },
    } = this.props;
    validateFieldsAndScroll(async (err, values) => {
      if (err) {
        return;
      }
      this.getList(values, ex, isChangeScene);
    });
  };
  private handleReset = () => {
    const {
      form: { resetFields }
    } = this.props;
    resetFields();
    this.setState({ filters: {}, sorters: {} }, () => {
      this.getList({});
    });
  };

  private CheckingState = [
    { key: '', name: i18n.get('全部') },
    { key: 'CHECKING_TODO', name: i18n.get('待对账') },
    { key: 'CHECKING_DOING', name: i18n.get('对账中') },
    { key: 'CHECKING_DONE', name: i18n.get('已对账') },
    { key: 'NONE', name: i18n.get('未开始') },
  ];

  private AdjustState = [
    { key: '', name: i18n.get('全部') },
    { key: 'UNADJUSTED', name: i18n.get('未调整') },
    { key: 'ADJUSTING', name: i18n.get('调整中') },
    { key: 'ADJUST_SUCCESS', name: i18n.get('调整成功') },
    { key: 'ADJUST_FAIL', name: i18n.get('调整失败') },
    { key: 'ADJUST_CONFIRMED', name: i18n.get('已确认') },
  ];

  private InvoiceState = [
    { key: '', name: i18n.get('全部') },
    { key: 'NONE', name: i18n.get('未开票') },
    { key: 'INVOICE_TODO', name: i18n.get('待开票') },
    { key: 'INVOICE_DOING', name: i18n.get('开票中') },
    { key: 'INVOICE_DONE', name: i18n.get('已开票') },
  ];

  private SettlementState = [
    { key: '', name: i18n.get('全部') },
    { key: 'NONE', name: i18n.get('未结算') },
    { key: 'SETTLEMENT_TODO', name: i18n.get('待结算') },
    { key: 'SETTLEMENT_DOING', name: i18n.get('结算中') },
    { key: 'SETTLEMENT_DONE', name: i18n.get('已结算') },
  ];
  private ActiveState = [
    { key: '', name: i18n.get('全部') },
    { key: 'nextPeriod', name: '是' },
    { key: 'currentPeriod', name: '否' },
  ];

  private onChangeScene = (Scene) => {
    const { hiddenForm } = this.props;
    this.setState(
      { orderType: Scene, filters: {}, sorters: {}, pagination: { current: 1, size: 10 }, isVisibleSaveDiff: false, dataSource: [] },
      () => {
        if (hiddenForm) {
          return this.getList({}, false, true);
        }
        this.handleSearch({ ex: undefined, isChangeScene: true });
      },
    );
  };
  private onPaginationChange = (pagination, pageMode) => {
    const { hiddenForm } = this.props;
    this.setState({ pagination, pageMode }, () => {
      if (hiddenForm) {
        return this.getList({}, false, true);
      }
      this.handleSearch({});
    });
  };
  private onSorterChange = (sorters) => {
    this.setState({ sorters }, () => {
      this.handleSearch({});
    });
  };
  private categoryIdsToScenes = () => {
    const { categoryIds = [] } = this.props;
    return categoryIds.map((it) => {
      return { text: it?.name, sceneIndex: it?.id };
    });
  };
  private onColumnsChange = (arr: any) => {
    const { originColumns } = this.state;
    const col = originColumns?.filter((it: any) => arr?.includes(it?.dataIndex));
    this.setState({ columns: col, visibleColumns: arr, isVisibleSaveDiff: true });
  };
  private onFilterChange = (filters: any) => {
    const { hiddenForm } = this.props;
    this.setState({ filters }, () => {
      if (hiddenForm) {
        return this.getList({});
      }
      this.handleSearch({});
    });
  };
  private onExport = () => {
    const { dataSource } = this.state;
    if (!dataSource?.length) {
      showMessage.warning(i18n.get('暂无数据可导出'));
      return;
    }
    this.handleSearch({ ex: true, isChangeScene: false });
  };
  private handleSelectedChange = (selectedKeys: string[], selectedDatas: any) => {
    this.setState({ selectedKeys, selectedDatas });
  };
  private onSaveDiffScenes = () => {
    const { orderType, visibleColumns, columnType, difScenes } = this.state;
    const scenesInfo = this.categoryIdsToScenes();
    for (let item of scenesInfo) {
      if (difScenes && difScenes.length > 0) {
        const exist = difScenes.find((i) => {
          return i?.sceneIndex == item?.sceneIndex;
        });
        //将已经设置过自定义表头字段的数据保存
        if (exist && exist?.defaultColumns) {
          item.defaultColumns = exist.defaultColumns;
        }
      }
      if (item.sceneIndex === orderType) {
        item.defaultColumns = visibleColumns;
      }
    }
    const filter = scenesInfo.map((scene: any) => JSON.stringify(scene));
    receiptResource
      .POST('/$type', {
        type: columnType,
        filter,
      })
      .then(() => {
        showMessage.success(i18n.get('保存成功'));
        this.setState({ isVisibleSaveDiff: false });
      })
      .catch((err) => {
        showMessage.error(err.message);
      });
  };

  private parseMeta = (property: any) => {
    if (property?.type === 'list' && property?.elemType?.entity === 'organization.Staff') {
      return 'list:ref:organization.Staff';
    } else if (property?.type === 'ref' && property?.entity === 'organization.Staff') {
      return 'ref:organization.Staff';
    } else if (property?.type === 'ref' && property?.entity === 'organization.Department') {
      return 'ref:organization.Department';
    } else if (property?.type === 'ref' && property?.entity === 'basedata.city') {
      return 'city';
    } else if (property?.type === 'ref' && property?.entity) {
      return `ref:${property?.entity}`
    } else {
      return property?.elemType?.type || property.type;
    }
  };

  private formatColumns = async () => {
    const { dataSource, columns, checkingBillId, orderType, selectedKeys, originColumns } = this.state;
    const filterColumns = await adjustableField(orderType, checkingBillId);
    const items = filterColumns?.items ?? [];
    const editColumns = items.map((i: any) => i.name)
    const currentsNames = columns.map((i: any) => i?.property?.name)
    if (!currentsNames.includes(`E_${orderType}_code`)) {
      const codeCol = originColumns.find((item: any) => item?.property?.name === `E_${orderType}_code`)
      codeCol && columns.unshift(codeCol)
    }
    if (!currentsNames.includes(`E_system_checking_accountPeriod`)) {
      const periodCol = originColumns.find((item: any) => item?.property?.name === `E_system_checking_accountPeriod`)
      periodCol && columns.unshift(periodCol)
    }
    const dataOther = dataSource
      .filter((i) => selectedKeys.includes(i?.dataLink?.id))
      .map((item, index) => {
        return { ...item.dataLink, index };
      });
    const columnsOther = columns.map((item) => {
      item.field = item.dataIndex = item.key = item?.property?.name;
      item.optional = true;
      item.editable = editColumns.includes(item?.property?.name)
      item.type = this.parseMeta(item?.property);
      if (item.field === 'E_system_checking_accountPeriod') {
        item.entity = 'basedata.Enum';
        item.type = 'basedata.Enum';
        item.property.type = 'ref';
        item.property.entity = 'basedata.Enum';
        item.property.customData = [
          { label: i18n.get('本期对账'), value: 'currentPeriod', name: i18n.get('本期对账'), code: 'currentPeriod' },
          { label: i18n.get('本期不对账'), value: 'nextPeriod', name: i18n.get('本期不对账'), code: 'nextPeriod' },
        ];
      }
      if (item.field === 'E_system_checking_adjustState') {
        item.entity = 'basedata.Enum';
        item.type = 'basedata.Enum';
        item.property.type = 'ref';
        item.property.entity = 'basedata.Enum';
        item.property.customData = [
          { value: 'UNADJUSTED', label: i18n.get('未调整'), name: i18n.get('未调整'), code: 'UNADJUSTED' },
          { value: 'ADJUSTING', label: i18n.get('调整中'), name: i18n.get('调整中'), code: 'ADJUSTING' },
          { value: 'ADJUST_SUCCESS', label: i18n.get('调整成功'), name: i18n.get('调整成功'), code: 'ADJUST_SUCCESS' },
          { value: 'ADJUST_FAIL', label: i18n.get('调整失败'), name: i18n.get('调整失败'), code: 'ADJUST_FAIL' },
          { value: 'ADJUST_CONFIRMED', label: i18n.get('已确认'), name: i18n.get('已确认'), code: 'ADJUST_CONFIRMED' },
        ];
      }

      return { ...item.property, ...item, dataType: { entity: item.property.entity } };
    }).sort((a: any, b: any) => {
      if (!a.field.includes('_code') && b.field.includes('_code')) {
        return 1
      } else if (a.field.includes('_code') && !b.field.includes('_code')) {
        return -1
      }

      if (!a.field.includes('E_system_checking_accountPeriod') && b.field.includes('E_system_checking_accountPeriod')) {
        return 1
      } else if (a.field.includes('E_system_checking_accountPeriod') && !b.field.includes('E_system_checking_accountPeriod')) {
        return -1
      }
      return 0
    });
    return { columnsOther, dataOther }
  }

  private handleBill = async () => {
    const { checkingBillId, orderType, importMethod, supplierArchiveType } = this.state;
    const { columnsOther, dataOther } = await this.formatColumns()
    const isApi = supplierArchiveType === 'HOSE_TRIP' && importMethod === 'api'
    app.open('@settlement-checkin:AdjustingBillsDrawer', {
      components: columnsOther,
      dataSource: dataOther,
      bus: this.props?.bus,
      isEdit: true,
      checkingBillId,
      entityId: orderType,
      isApi
    });
  };

  private handleAdjustmentConfirm = async () => {
    const { checkingBillId, selectedKeys } = this.state;
    if (selectedKeys.length !== 0) {
      const res = await adjustmentConfirm({ checkingBillId, checkingDetailIds: selectedKeys });
      if (res && res.value?.success) {
        showMessage.success('确认成功');
        this.getList({}, undefined, true);
      }
    } else {
      showMessage.warning('请选择至少一条明细');
    }
  };
  handleAdjustment = async (checkedData: any[]) => {
    const { checkingBillId, orderType, supplierArchiveType, importMethod } = this.state;
    const isApi = supplierArchiveType === 'HOSE_TRIP' && importMethod === 'api'
    try {
      const res = await adjustmentBill({
        checkingBillId: checkingBillId,
        entityId: orderType,
        checkingDetails: checkedData,
      });
      if (res?.value?.success) {
        isApi ? showMessage.success(i18n.get('已发起调整, 请稍后查看'), 5) : showMessage.success(i18n.get('调整成功'), 5)
        this.handleSearch({ ex: false, isChangeScene: true })
      } else {
        showMessage.error(res?.value?.msg, 5)
      }
    } catch (err: any) {
      if (err) {
        showMessage.error(err?.errorMessage, 5)
      }
    }
  };

  handleBatchAdjust = async () => {
    const { columnsOther } = await this.formatColumns()
    const fields = columnsOther.map((cp) => {
      return { ...cp.property, editable: cp.editable };
    }).filter((item: any) => item.editable);
    const data = await app.open('@settlement-checkin:BatchModifyBillFieldValue', { changeFields: fields });
    const checkedData = this.allDataUpdate(data)
    this.handleAdjustment(checkedData)
  };


  allDataUpdate = (data: any) => {
    let checkingDetails: any = [];
    const { selectedKeys } = this.state;
    selectedKeys.forEach((key: string, index: number) => {
      let itemObj: any = {}
      data.forEach((d: any) => {
        itemObj['id'] = key
        if (d.field === 'E_system_checking_accountPeriod') {
          itemObj['belongPeriod'] = d.value?.value
        } else {
          itemObj[d.field] = Array.isArray(d.value) ? d.value?.map(v => ({ id: v.id, name: v.name })) : d.value?.id ? { id: d.value.id, name: d.value.name } : d.value
        }
      })
      checkingDetails.push(itemObj)
    })
    return checkingDetails
  }

  render() {
    const formItemLayout = {
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    };
    const formItemLayout2 = {
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
    };
    const {
      form: { getFieldDecorator },
      hiddenForm,
      fixCol,
      showMatch
    } = this.props;
    const {
      orderType,
      totalLength,
      pageMode,
      pagination,
      dataSource,
      columns,
      sorters,
      filters,
      originColumns,
      visibleColumns,
      isVisibleSaveDiff,
      columnType,
      selectedKeys,
      checkingBillId,
    } = this.state;
    return (
      <>
        {hiddenForm ? null : (
          <div className="query_view">
            <Form>
              <div className="row">
                <div className="item">
                  <FormItem {...formItemLayout} className={'form_item'} label="订单号">
                    {getFieldDecorator('form|E_system_checking_orderNo', {
                      rules: [],
                    })(<Input placeholder={i18n.get('请输入订单号')} />)}
                  </FormItem>
                </div>
                <div className="item">
                  <FormItem {...formItemLayout} className={'form_item'} label="申请单号">
                    {getFieldDecorator('form|E_system_checking_requisitionCode', {
                      rules: [],
                    })(<Input placeholder={i18n.get('请输入申请单号')} />)}
                  </FormItem>
                </div>
                <div className="item">
                  <FormItem {...formItemLayout} className={'form_item'} label="对账状态">
                    {getFieldDecorator('form|E_system_checking_checkingState', {
                      initialValue: '',
                      rules: [],
                    })(
                      <Select allowClear placeholder={i18n.get('请选择对账状态')}>
                        {this.CheckingState?.map((it) => {
                          return (
                            <Option key={it?.key} value={it?.key}>
                              {it.name}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </div>
                <div className="item">
                  <FormItem {...formItemLayout} className={'form_item'} label="调整状态">
                    {getFieldDecorator('adjustState', {
                      initialValue: '',
                      rules: [],
                    })(
                      <Select allowClear placeholder={i18n.get('请选择调整状态')}>
                        {this.AdjustState?.map((it) => {
                          return (
                            <Option key={it?.key} value={it?.key}>
                              {it.name}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </div>
              </div>
              <div className="row">
                <div className="item">
                  <FormItem {...formItemLayout} className={'form_item'} label="开票状态">
                    {getFieldDecorator('form|E_system_checking_invoiceState', {
                      initialValue: '',
                      rules: [],
                    })(
                      <Select allowClear placeholder={i18n.get('请选择开票状态')}>
                        {this.InvoiceState?.map((it) => {
                          return (
                            <Option key={it?.key} value={it?.key}>
                              {it?.name}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </div>
                <div className="item2">
                  <FormItem {...formItemLayout2} className={'form_item'} label="结算状态">
                    {getFieldDecorator('form|E_system_checking_settlementState', {
                      initialValue: '',
                      rules: [],
                    })(
                      <Select allowClear placeholder={i18n.get('请选择结算状态')}>
                        {this.SettlementState?.map((it) => {
                          return (
                            <Option key={it?.key} value={it?.key}>
                              {it?.name}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </div>
                <div className="item3">
                  <FormItem {...formItemLayout2} className={'form_item'} label={<span>
                    本期不对账&nbsp;
                    <Tooltip title="「本期不对账」账单明细不可在本期对账结算。账单金额、费用金额不计入本账期，且不可生成子对账单。">
                      <Icon type="question-circle-o" />
                    </Tooltip>
                  </span>} >
                    {getFieldDecorator('form|E_system_checking_accountPeriod', {
                      initialValue: '',
                      rules: [],
                    })(
                      <Select allowClear placeholder={i18n.get('请选择本期不对账')}>
                        {this.ActiveState?.map((it) => {
                          return (
                            <Option key={it?.key} value={it?.key}>
                              {it?.name}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </div>

                <div className="item2">
                  <FormItem className={'form_item'}>
                    <Button type="primary" style={{ marginLeft: 14 }} onClick={() => this.handleSearch({})}>
                      {i18n.get('查询')}
                    </Button>
                    <Button style={{ marginLeft: 8 }} onClick={this.handleReset}>
                      {i18n.get('重置')}
                    </Button>
                  </FormItem>
                </div>
              </div>
            </Form>
          </div>
        )}
        <DataTable
          footerAction={(
            <div>
              <EuiBtn
                size="small"
                category="secondary"
                style={{ marginRight: '8px' }}
                disabled={!selectedKeys.length}
                onClick={this.handleBatchAdjust}
              >
                <T name="批量调整账单" />
              </EuiBtn>
              <EuiBtn
                size="small"
                category="secondary"
                style={{ marginRight: '8px' }}
                disabled={!selectedKeys.length}
                onClick={this.handleBill}
              >
                <T name="逐条调整账单" />
              </EuiBtn>
              <EuiBtn
                size="small"
                category="secondary"
                style={{ marginRight: '8px' }}
                disabled={!selectedKeys.length}
                onClick={this.handleAdjustmentConfirm}
              >
                <T name="确认" />
              </EuiBtn>
              <EuiBtn
                size="small"
                category="secondary"
                onClick={() => {
                  api.open('@settlement-checkin:BillAdjustLogsModal', { checkingBillId, entityId: orderType });
                }}
              >
                <T name="账单调整日志" />
              </EuiBtn>
            </div>
          )
          }
          selectedRowKeys={selectedKeys}
          onSelectedChange={this.handleSelectedChange}
          orderType={orderType}
          onChangeScene={this.onChangeScene}
          totalLength={totalLength}
          pagination={pagination}
          pageMode={pageMode}
          scenes={this.categoryIdsToScenes()}
          sorters={hiddenForm ? {} : sorters}
          filters={filters}
          onSorterChange={hiddenForm ? null : this.onSorterChange}
          onFilterChange={this.onFilterChange}
          onPaginationChange={this.onPaginationChange}
          onColumnsChange={this.onColumnsChange}
          dataSource={dataSource}
          visibleColumns={visibleColumns}
          columns={columns}
          originColumns={originColumns}
          onExport={this.onExport}
          hiddenForm={hiddenForm}
          fixCol={fixCol}
          showMatch={showMatch}
          isVisibleSaveDiff={isVisibleSaveDiff}
          onSaveDiffScenes={this.onSaveDiffScenes}
          columnType={columnType}
        />
      </>
    );
  }
}
