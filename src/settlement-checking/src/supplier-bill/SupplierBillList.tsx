/*
 * @Description: 供应商账单列表
 * @Creator: chencan<PERSON><PERSON>
 * @Date: 2021-06-04 17:40:22
 */
import React from 'react';
import style from './SupplierBillList.module.less';
import { app } from '@ekuaibao/whispered';
import { get, debounce } from 'lodash';
import {
  addCheckingBill,
  getSupplierArchiveList,
  getCheckingBillList,
  getBillingPeriodList,
  deleteCheckingBill,
  manuallySyncBill,
  matchTaskLaunch,
  existMatchTask,
  matchConfirm,
  getApiSyncBasicConfig,
  getApiSyncList,
  getSyncHoseTrip
} from '../setttlement-checkin-action';
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
import { showMessage, showModal } from '@ekuaibao/show-util';
import { injectKeel, KeelVM } from '@ekuaibao/keel';
import { QuerySelect } from 'ekbc-query-builder';
import import_svg from '../images/import.svg';
import CREDIT from '../images/CREDIT.svg';
import banner from '../images/banners/banner.svg';
import can from '../images/banners/can.svg';
import didi from '../images/banners/didi.svg';
import ekb from '../images/banners/ekb.svg';
import elm from '../images/banners/elm.svg';
import jd from '../images/banners/jd.svg';
import jtb from '../images/banners/jtb.svg';
import yql from '../images/banners/yql.svg';
import EXPORT from '../images/EXPORT.svg';
import API from '../images/API.svg';
import DIRECTLY from '../images/DIRECTLY.svg';
import { Form, Popconfirm } from 'antd';
import { Input, Select, Button, Pagination, Tooltip } from '@hose/eui'
import { T } from '@ekuaibao/i18n';
const FormItem = Form.Item;
const { Option } = Select;
const Money = app.require<any>('@elements/puppet/Money');
const EKBIcon = app.require<any>('@elements/ekbIcon');
import { EnhanceConnect } from '@ekuaibao/store';
import { getSteps } from './ImportProgressContent';

interface SupplierBillListProps {
  [key: string]: any;
  form: any;
}
interface SupplierBillListState {
  [key: string]: any;
}
@EnhanceConnect((state: any) => ({
  KA_ZY_Reconciliation_Settlement: state['@common'].powers.KA_ZY_Reconciliation_Settlement,
}))
@EnhanceFormCreate()
export default class SupplierBillList extends React.Component<SupplierBillListProps, SupplierBillListState> {
  @injectKeel()
  keel: KeelVM | undefined;

  constructor(props: SupplierBillListProps | Readonly<SupplierBillListProps>) {
    super(props);
    this.state = {
      billPeriodList: [],
      billPeriod: [],
      archiveList: [],
      pagination: { current: 1, size: 5 },
      dataCount: 0,
      loading: false
    };
  }

  componentDidMount() {
    this.getList({});
    // 供应商下拉
    this.getArchiveList();
    // 账期下拉
    this.getBillingPeriod();
    //吉利和携程基础配置获取
    this.getBasicConfig();
  }
  getList = async (data) => {
    this.setState({ loading: true });
    const { pagination } = this.state;
    const q = new QuerySelect().select(
      'supplierAccountId(id,name,settlementType,importMethod,balanceAmount,categoryIds(id,name)),supplierArchiveId(id,name),billPeriod(`...`),`...`',
    );
    q.limit((pagination.current - 1) * pagination.size, pagination.size);
    q.desc('createTime');
    if (data) {
      if (data?.name) {
        q.filterBy(`supplierAccountId.name.contains("${data?.name}")`);
      }
      if (data?.supplierArchiveId) {
        q.filterBy(`supplierArchiveId=="${data?.supplierArchiveId}"`);
      }
      if (data?.settlementType) {
        q.filterBy(`settlementType=="${data?.settlementType}"`);
      }
      if (data?.billPeriod) {
        q.filterBy(`billPeriod.id=="${data?.billPeriod}"`);
      }
      if (data?.settlementState) {
        q.filterBy(`settlementState=="${data?.settlementState}"`);
      }
    }
    return new Promise((resolve, reject) => {
      getCheckingBillList({ ...q.value() })
        .then((res) => {
          if (res?.errorCode) {
            showMessage.error(res?.message);
          } else {
            this.setState({ billPeriodList: get(res, 'items', []), dataCount: res.count });
          }
          resolve(res);
          this.setState({ loading: false });
        })
        .catch((err) => {
          reject(err);
          this.setState({ loading: false });
          showMessage.error(err?.errorMessage || err?.message);
        });
    });
  };
  addBill = async () => {
    app
      .open('@settlement-checkin:AddBillModal', {
        title: i18n.get('导入账单'),
        footer: null,
      })
      .then((result: any) => {
        const params = { billPeriod: result?.billPeriod, supplierAccountId: result?.supplierAccountId };
        addCheckingBill(params)
          .then(async (resp) => {
            app.open('@layout5:ImportProgressModal', {
              defaultSteps: getSteps({
                ...resp,
                ...result,
                addBill: this.addBill,
                getList: this.getList,
                keel: this?.keel,
              }),
            });
          })
          .catch((err) => {
            showMessage.error(err?.errorMessage || err?.message);
          });
      })
      .catch((err) => {
        showMessage.error(err?.errorMessage || err?.message || '请重新导入');
      });
  };
  async getArchiveList() {
    getSupplierArchiveList({ select: 'id,name' })
      .then((res) => {
        this.setState({ archiveList: get(res, 'items', []) });
      })
      .catch((err) => {
        showMessage.error(err?.errorMessage || err?.message);
      });
  }
  async getBillingPeriod() {
    getBillingPeriodList({})
      .then((res) => {
        this.setState({ billPeriod: get(res, 'items', []) || [] });
      })
      .catch((err) => {
        showMessage.error(err?.errorMessage || err?.message);
      });
  }

  getBasicConfig = async () => {
    let apiSyncCtrip = await this.getApiSyncBasic('CTRIP')
    let apiSyncGeely = await this.getApiSyncBasic('GEELY_TRIP')
    let apiSyncTC = await this.getApiSyncBasic('TC_TRIP')
    let apiSyncTO = await this.getApiSyncBasic('TRAVEL_ONE')
    let syncConfigList = await getApiSyncList()
    let syncHoseTrip = await getSyncHoseTrip()
    this.setState({
      apiSyncCtrip: apiSyncCtrip?.value?.apiSyncSwitch ?? false,
      apiSyncGeely: apiSyncGeely?.value?.apiSyncSwitch ?? false,
      apiSyncTC: apiSyncTC?.value?.apiSyncSwitch ?? false,
      apiSyncTO: apiSyncTO?.value?.apiSyncSwitch ?? false,
      syncConfigList: syncConfigList?.items ?? [],
      syncHoseTrip: syncHoseTrip?.value
    })
  }

  getApiSyncBasic = async (type: string) => {
    try {
      return await getApiSyncBasicConfig({ billPlatform: type })
    } catch (err) {
      return {}
    }
  }
  private name2icon(name) {
    let obj = { icon: banner };
    switch (name) {
      case '滴滴公司':
        obj = { icon: didi };
        break;
      case '佳能采购':
        obj = { icon: can };
        break;
      case '银企联':
        obj = { icon: yql };
        break;
      case '聚通宝':
        obj = { icon: jtb };
        break;
      case '京东企业购':
        obj = { icon: jd };
        break;
      case '东莞市友泰智能科技有限公司':
        obj = { icon: banner };
        break;
      case '南京合思':
        obj = { icon: ekb };
        break;
      case '饿了么':
        obj = { icon: elm };
        break;
      default:
        obj = { icon: banner };
    }
    return obj;
  }
  private SettlementState = [
    { key: '', name: i18n.get('全部') },
    { key: 'NONE', name: i18n.get('未结算') },
    { key: 'SETTLEMENT_TODO', name: i18n.get('待结算') },
    { key: 'SETTLEMENT_DOING', name: i18n.get('结算中') },
    { key: 'SETTLEMENT_DONE', name: i18n.get('已结算') },
  ];
  private SettlementType = [
    { key: '', name: i18n.get('全部') },
    { key: 'CREDIT', name: i18n.get('授信') },
    { key: 'PRIECHARGE', name: i18n.get('预存') },
  ];
  private SettlementStateObj = {
    NONE: i18n.get('未结算'),
    SETTLEMENT_TODO: i18n.get('待结算'),
    SETTLEMENT_DOING: i18n.get('结算中'),
    SETTLEMENT_DONE: i18n.get('已结算'),
  };
  private handleSearch = () => {
    const {
      form: { validateFieldsAndScroll },
    } = this.props;
    validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }
      this.getList(values);
    });
  };
  private handleReset = () => {
    const {
      form: { resetFields },
    } = this.props;
    // setFieldsValue({ name: '', supplierArchiveId: '', settlementType: '', active: '' })
    resetFields();
    this.getList();
  };
  private delete = (id, importMethod) => {
    showModal.confirm({
      title: i18n.get('提示'),
      content: importMethod === 'api' ? i18n.get('该账单为API同步，删除后可再次通过右上角「手动同步账单」功能重新拉取。确认要删除账单？') : i18n.get('确认要删除账单？'),
      cancelText: i18n.get('取消'),
      okText: i18n.get('确定'),
      onOk: () => {
        deleteCheckingBill(id)
          .then((res) => {
            showMessage.success(i18n.get('账单删除成功'));
            this.handleSearch();
          })
          .catch((err) => {
            showMessage.error(err?.errorMessage || err?.message);
          });
      },
    });
  };
  private getIcon = (name: string) => {
    if (name.indexOf('飞机') > -1) return '#ico-7-icon_aircraft';
    if (name.indexOf('火车') > -1) return '#ico-7-icon_train';
    if (name.indexOf('酒店') > -1) return '#ico-7-icon_hotel';
    if (name.indexOf('用车') > -1) return '#ico-7-icon_car';
    return '#ico-7-icon_customize';
  };
  private paginationChange = (page, pageSize) => {
    this.setState({ pagination: { current: page, size: pageSize } }, () => {
      this.handleSearch();
    });
  };
  handlerManuallySyncBill = () => {
    const { apiSyncCtrip, apiSyncGeely, apiSyncTC, apiSyncTO, syncHoseTrip } = this.state;
    app
      .open('@settlement-checkin:ManuallySyncBillModal', {
        footer: null,
        apiSyncCtrip,
        apiSyncGeely,
        apiSyncTC,
        apiSyncTO,
        syncHoseTrip
      })
      .then((res: any) => {
        if (res?.key === 'isHose') {
          showMessage.success(res.value);
        } else {
          this.manuallySyncBill(res);
        }
      });
  };
  manuallySyncBill = async (params: any) => {
    try {
      const { value } = await manuallySyncBill(params);
      showMessage.success(value);
    } catch (error) {
      showMessage.error(error?.errorMessage || error?.message);
    }
  };
  isTasking = async (data: any, type?: any) => {
    const {
      value: { existTask },
    } = await existMatchTask({ checkingBillId: data.id });
    let content = i18n.get('正在执行匹配任务，不可操作匹配');
    if (type === 0) {
      content = i18n.get('正在执行匹配任务，暂不可查看匹配结果');
    }
    existTask &&
      showModal.info({
        title: i18n.get('正在执行匹配任务'),
        content,
      });
    return existTask;
  };
  handlerStartMatch = async (data: any) => {
    if (await this.isTasking(data)) return;
    const { time } = await app.open('@settlement-checkin:OrderSelectModal', { data });
    await matchTaskLaunch({ ...time, checkingBillId: data.id });
    await app.open('@settlement-checkin:OrderShowTaskModal');
    this.getList({});
  };
  handlerSureMatch = async (line: any) => {
    await matchConfirm({ checkingBillId: line.id });
    this.getList({});
  };
  handlerSearchMatch = async (line: any) => {
    if (await this.isTasking(line, 0)) return;
    this?.keel?.open('OrderMatchDetail', {
      data: { line },
    });
  };

  renderType(it) {
    const importMethodMap = {
      api: { label: i18n.get('API同步'), img: API },
      excel: { label: i18n.get('手动导入'), img: EXPORT },
      directly: { label: i18n.get('API写入'), img: DIRECTLY },
    }
    const type: 'api' | 'export' | 'directly' | 'flow' | 'ebot' = get(it, 'importMethod', 'export')
    const importMethod = importMethodMap[type] || { label: i18n.get('手动导入'), img: EXPORT }
    return <div className="type">
      <img src={importMethod.img} />
      <div>{importMethod.label}</div>
    </div>
  }
  render() {
    const formItemLayout = {
      labelCol: { span: 24 },
      wrapperCol: { span: 20 },
    };
    const {
      form: { getFieldDecorator },
      KA_ZY_Reconciliation_Settlement,
    } = this.props;
    const { billPeriodList = [], billPeriod = [], dataCount, pagination, syncConfigList = [], syncHoseTrip } = this.state;
    return (
      <div className={style['supplier_bill_list']}>
        <div className="content_view">
          <div className="header">
            <div className="title">{this.props.title}</div>
            <div className="right_view">
              {(syncConfigList?.length || syncHoseTrip) && (
                <Button category="primary" style={{ marginRight: '8px' }} onClick={this.handlerManuallySyncBill}>
                  {i18n.get('手动同步账单')}
                </Button>
              )}
              <Button category="primary" onClick={this.addBill}>
                <EKBIcon name="#ico-7-icon_input_w" className="ico" />
                {i18n.get('导入账单')}
              </Button>
            </div>
          </div>
          <div className="query_view">
            <Form>
              <div className="row">
                <div className="item">
                  <FormItem {...formItemLayout} className={'form_item'} label="账户名称">
                    {getFieldDecorator('name', {
                      rules: [],
                    })(<Input placeholder={i18n.get('请输入账户名称')} />)}
                  </FormItem>
                </div>
                <div className="item">
                  <FormItem {...formItemLayout} className={'form_item'} label="供应商">
                    {getFieldDecorator('supplierArchiveId', {
                      rules: [],
                    })(
                      <Select allowClear placeholder={i18n.get('请选择供应商')} style={{ width: "100%" }}>
                        {this.state?.archiveList?.map((it) => {
                          return (
                            <Option key={it.id} value={it.id}>
                              <Tooltip placement="bottom" title={it.name}>
                                {it.name}
                              </Tooltip>
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </div>
                <div className="item">
                  <FormItem {...formItemLayout} className={'form_item'} label="账户类型">
                    {getFieldDecorator('settlementType', {
                      rules: [],
                    })(
                      <Select allowClear style={{ width: "100%" }} placeholder={i18n.get('请选择账户类型')}>
                        {this.SettlementType?.map((it) => {
                          return (
                            <Option key={it?.key} value={it?.key}>
                              {it?.name}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </div>
              </div>
              <div className="row" style={{ marginTop: 16 }}>
                <div className="item">
                  <FormItem {...formItemLayout} className={'form_item'} label="账期">
                    {getFieldDecorator('billPeriod', {
                      rules: [],
                    })(
                      <Select style={{ width: "100%" }} allowClear placeholder={i18n.get('请选择账期')}>
                        {billPeriod?.map((it) => {
                          return (
                            <Option key={it?.id} value={it?.id}>
                              {it?.name}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </div>
                <div className="item">
                  <FormItem {...formItemLayout} className={'form_item'} label="结算状态">
                    {getFieldDecorator('settlementState', {
                      rules: [],
                    })(
                      <Select allowClear style={{ width: "100%" }} placeholder={i18n.get('请选择结算状态')}>
                        {this.SettlementState?.map((it) => {
                          return (
                            <Option key={it?.key} value={it?.key}>
                              {it?.name}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </div>
                <div className="item" style={{ height: 62, display: 'flex', alignItems: 'end' }}>
                  <div>
                    <Button category="primary" onClick={this.handleSearch}>
                      {i18n.get('查询')}
                    </Button>
                    <Button style={{ marginLeft: 8 }} category="secondary" onClick={this.handleReset}>
                      {i18n.get('重置')}
                    </Button>
                  </div>
                </div>
              </div>
            </Form>
          </div>
          <div className="lists_view">
            {billPeriodList?.map((it) => {
              const categoryIds = get(it, 'supplierAccountId.categoryIds');
              const statisticsInfo = get(it, 'statisticsInfo');
              const supplierAccount = get(it, 'supplierAccountId.name', '');
              const matchNum = get(it, 'orderMatchInfo.matchNum', '0');
              const notMatchNum = get(it, 'orderMatchInfo.notMatchNum', '0');
              const confirmMatch = get(it, 'confirmMatch', '');
              const matchConfigEnable = get(it, 'matchConfigEnable', false);
              const cantClick = matchNum == 0 && notMatchNum == 0;
              const FinishSpan = () => (
                <span className={confirmMatch ? 'cant' : ''} style={{ marginLeft: '10px' }}>
                  确认
                </span>
              );
              return (
                <div key={it?.id} className={`item_view`}>
                  <div
                    className="ic_v"
                    onClick={(e) => {
                      this.delete(it?.id, it?.importMethod);
                      e?.preventDefault();
                    }}
                  >
                    <EKBIcon name="#EDico-icon1beifen2" className="ic" />
                  </div>
                  {KA_ZY_Reconciliation_Settlement && matchConfigEnable && (
                    <div className="order-match-btn">
                      <div>
                        <span onClick={debounce(this.handlerStartMatch.bind(this, it), 1000)}>匹配订单</span>
                        {!confirmMatch ? (
                          <Popconfirm
                            title={<T name={'请确认，所有对账单均已匹配完成'} />}
                            onConfirm={() => this.handlerSureMatch(it)}
                          >
                            {FinishSpan()}
                          </Popconfirm>
                        ) : (
                          FinishSpan()
                        )}
                      </div>
                      <div
                        className={cantClick ? 'cant' : ''}
                        onClick={() => !cantClick && this.handlerSearchMatch(it)}
                      >
                        {i18n.get('已匹配：{__k0}条', { __k0: matchNum })},<br />
                        {i18n.get('未匹配：{__k0}条', { __k0: notMatchNum })}
                      </div>
                    </div>
                  )}
                  <div
                    className="content_view"
                    onClick={(e) => {
                      const canRefresh = it?.tppBillNo && (it?.tppState === 'todo' || it?.tppState === 'reject');
                      e?.preventDefault();
                      this?.keel?.open('SupplierBillDetail', {
                        data: { id: it?.id, canRefresh, canImport: it.importMethod !== 'api' },
                        title: `${i18n.get('供应商：')}${get(it, 'supplierAccountId.name', '')}`,
                      });
                    }}
                  >
                    <div className="bill_period">
                      账期：{get(it, 'billPeriod.name', '')}{' '}
                      {this.SettlementStateObj[get(it, 'settlementState', 'NONE') || 'NONE']}
                      {KA_ZY_Reconciliation_Settlement && matchConfigEnable && (
                        <span style={{ marginLeft: '10px' }}>
                          {confirmMatch ? i18n.get('已匹配') : i18n.get('未匹配')}
                        </span>
                      )}
                      {it?.warn?.message?.length ? (
                        <div className={'bill-period-warn'}>
                          <EKBIcon name="#EDico-fail1" className="warnning-icon" />
                          <div className={'bill-period-warn-text'}>{i18n.get('部分账单未正确生成费用')}</div>
                        </div>
                      ) : null}
                    </div>
                    <div className="content">
                      <div className="item_v" style={{ minWidth: 220 }}>
                        <Money
                          className="money_xf"
                          showSymbol={false}
                          value={get(it, 'statisticsInfo.TOTAL.totalAmount', 0) || 0}
                        />
                        <div className="">消费总额/元</div>
                      </div>
                      <div className="item_v" style={{ width: 160, marginLeft: 5 }}>
                        <Money
                          className="money_sy"
                          showSymbol={false}
                          value={get(it, 'supplierAccountId.balanceAmount', 0) || 0}
                        />
                        <div className="">剩余额度/元</div>
                      </div>
                      <div className="dip" />
                      <div className="category_view">
                        {categoryIds
                          .filter((line) => !!line)
                          .map((item: any, i: number) => {
                            return (
                              <div
                                key={`${item?.id}-${i}`}
                                className="item_v"
                                style={{ maxWidth: 170, minWidth: 170, marginLeft: 5 }}
                              >
                                <div className="transportation_view">
                                  <div className="text_ellipsis">{item?.name?.replace(`${supplierAccount}-`, '')}</div>
                                  <EKBIcon name={this.getIcon(item?.name)} className="ml8 fj" />
                                </div>
                                <div className="df">
                                  <Money
                                    className="money_sy"
                                    showSymbol={false}
                                    value={get(statisticsInfo, `${item?.id}.totalAmount`, 0) || 0}
                                  />
                                  <div style={{ marginLeft: 4 }}>元</div>
                                </div>
                                <div className="">共 {get(statisticsInfo, `${item?.id}.totalNum`, 0) || 0} 条</div>
                              </div>
                            );
                          })}
                      </div>
                    </div>
                  </div>
                  <div
                    className="dip_view"
                    onClick={(e) => {
                      e?.preventDefault();
                      this?.keel?.open('SupplierBillDetail', {
                        data: { id: it?.id },
                        title: `${i18n.get('供应商：')}${get(it, 'supplierAccountId.name', '')}`,
                      });
                    }}
                  >{`${i18n.get('供应商：')}${get(it, 'supplierArchiveId.name', '')}`}</div>
                  <div
                    className="supplier_view"
                    onClick={(e) => {
                      e?.preventDefault();
                      this?.keel?.open('SupplierBillDetail', {
                        data: { id: it?.id },
                        title: `${i18n.get('供应商账户：')}${get(it, 'supplierAccountId.name', '')}`,
                      });
                    }}
                  >
                    <img className="banner" src={get(this.name2icon(get(it, 'supplierArchiveId.name', '')), 'icon')} />
                    <div className="supplier">
                      {i18n.get('供应商账户：')}
                      {get(it, 'supplierAccountId.name', '')}
                    </div>
                    <div className="type">
                      <img src={CREDIT} />
                      <div>
                        {get(it, 'supplierAccountId.settlementType') === 'PRIECHARGE'
                          ? i18n.get('预存账户')
                          : i18n.get('授信账户')}
                      </div>
                    </div>
                    {this.renderType(it)}
                  </div>
                </div>
              );
            })}
            {!this.state.loading && <div className="item_new" onClick={this.addBill}>
              <div className="content">
                <img className="icon" src={import_svg} />
                <div className="new">{i18n.get('导入账单')}</div>
              </div>
            </div>}
          </div>
        </div>
        {dataCount && (
          <div className="bottom_view">
            <Pagination
              total={dataCount}
              current={pagination.current}
              pageSize={pagination.size}
              onChange={this.paginationChange}
            />
          </div>
        )}
      </div>
    );
  }
}
