/*
 * @Description: 供应商账单 table
 * @Creator: chencan<PERSON>han
 * @Date: 2021-06-08 15:06:25
 */
import React from 'react';
import style from './SupplierBillDetail.module.less';
import { app } from '@ekuaibao/whispered';
import { cloneDeep } from 'lodash';
import * as DataGrid from '@ekuaibao/datagrid';
const withLoader = app.require<any>('@elements/data-grid-v2/withLoader');
import { Button } from 'antd';

interface SupplierBillProps {
  orderType: any;
  onChangeScene: any;
  totalLength: any;
  pagination: any;
  pageMode: any;
  scenes: any[];
  onPaginationChange: any;
  onColumnsChange: any;
  dataSource: any;
  columns: any;
  originColumns: any;
  onExport: any;
  sorters: any;
  filters: any;
  onSorterChange: any;
  onFilterChange: any;
  onReachBottom: any;
  hiddenForm: any;
  fixCol: any;
  visibleColumns: any;
  onSaveDiffScenes: any;
  isVisibleSaveDiff: boolean;
  columnType: any;
  footerAction?: React.ReactNode;
  selectedRowKeys: string[];
  onSelectedChange: (selectedKeys: string[]) => void;
  isMultiSelect: boolean
}
interface SupplierBillState {}
class DataTable extends React.Component<SupplierBillProps, SupplierBillState> {
  render() {
    const {
      orderType,
      onChangeScene,
      totalLength = 1,
      pagination = { current: 1, size: 10 },
      pageMode = 'pagination',
      scenes = [],
      onPaginationChange,
      onColumnsChange,
      dataSource,
      columns,
      originColumns,
      onExport,
      sorters,
      filters,
      onSorterChange,
      onFilterChange,
      onReachBottom,
      hiddenForm = false,
      fixCol,
      visibleColumns,
      onSaveDiffScenes,
      isVisibleSaveDiff,
      columnType,
      footerAction,
      selectedRowKeys,
      onSelectedChange,
      isMultiSelect
    } = this.props;



    let cols = columns;
    if (fixCol && typeof fixCol === 'function') {
      cols = fixCol(columns);
    }

    const realVisibleColumns = visibleColumns.filter((item: any) => item !== 'matchState' && item !== 'action');
    const allVisibleColumns = cloneDeep(visibleColumns);
    allVisibleColumns.unshift('matchState');
    allVisibleColumns.push('action');

    return (
      <div className={style['supplier_bill_table']}>
        <div className="data_header">
          <DataGrid.Scenes
            scenes={scenes}
            activeScene={orderType}
            onChangeScene={(sceneIndex) => {
              onChangeScene && onChangeScene(sceneIndex);
            }}
          />
          <div className="data_right">
            {isVisibleSaveDiff && (
              <div
                className={columnType === 'STATEMENT_DETAIL_MATCH' ? 'diff_scenes_match' : 'diff_scenes'}
                onClick={onSaveDiffScenes}
              >
                {i18n.get('保存变更')}
              </div>
            )}
            {!hiddenForm && (
              <Button className={'btn'} onClick={onExport}>
                {i18n.get('导出')}
              </Button>
            )}
            <DataGrid.ColumnChooser
              columns={originColumns}
              defaultVisibleColumns={realVisibleColumns}
              onChange={onColumnsChange}
              onReset={onColumnsChange}
            />
          </div>
        </div>
        <DataGrid.TableWrapper
          className={'table-wrapper'}
          dataSource={dataSource || []}
          columns={cols}
          rowKey={'dataLink.id'}
          defaultVisibleColumns={allVisibleColumns}
          pageIndex={pagination?.current}
          pageSize={pagination?.size}
          pageMode={pageMode}
          sorters={sorters}
          filters={filters}
          scrolling={{
            mode: 'virtual',
          }}
          onReachBottom={onReachBottom}
          onSorterChange={onSorterChange}
          onFilterChange={onFilterChange}
          onSelectedChange={onSelectedChange}
          selectedRowKeys={selectedRowKeys}
          allowColumnResizing
        />
        <div className="data_footer">
          {!!footerAction && footerAction}
          <DataGrid.Pagination
            totalLength={totalLength}
            pagination={pagination}
            scrollPagination={pagination}
            pageMode={pageMode}
            disabledScroll
            onChange={onPaginationChange}
          />
        </div>
      </div>
    );
  }
}

export default withLoader(() => Promise.resolve({ default: DataTable }));
