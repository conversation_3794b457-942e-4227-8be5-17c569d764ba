
export const getTemps = (entityInfo: any, entity: any) => {
    let temps = entity.filter((i: any) => i.id === entityInfo.id || entityInfo)
    let tempobj: any = {}
    if (temps.length > 0) {
        temps.forEach((i: any) => {
            if (i.children && i.children.length > 0) {
                i.children.forEach((t: any) => {
                    tempobj[t.id] = t.name
                })
            }
        })
    }
    return tempobj
}
export const formatStaff = (staffs: any[] = []) => {
    let staffMap: any = {}
    staffs &&
        staffs.forEach((line) => {
            staffMap[line.id] = line
        })
    return staffMap
}
export const formatTripDataLinkEntity = (list: any[], orderTripWhiteList: any[]) => {
    if (list?.length === 0) return list
    const _list = list.find(i => i.type === 'ORDER')
    // _list.children = _list?.children?.filter((i: any) => orderTripWhiteList.includes(i.type))
    return _list
}