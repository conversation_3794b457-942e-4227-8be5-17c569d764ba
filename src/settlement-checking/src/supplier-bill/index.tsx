/*
 * @Description:
 * @Creator: chencan<PERSON>han
 * @Date: 2021-05-28 14:19:57
 */

import { app } from '@ekuaibao/whispered';
import React, { Component } from 'react';
import { Keel, registerComponentsCellar } from '@ekuaibao/keel';
const KeelSingleViewHeader = app.require<any>('@elements/puppet/KeelSingleViewHeader');
const KeelViewBody = app.require<any>('@elements/puppet/KeelViewBody');

@registerComponentsCellar([
  {
    key: 'SupplierBillDetail',
    getComponent: () => import('./SupplierBillDetail'),
    title: i18n.get('供应商账单详情'),
  },
  {
    key: 'SupplierBillList',
    getComponent: () => import('./SupplierBillList'),
    title: i18n.get('供应商账单'),
  },
  {
    key: 'OrderMatchDetail',
    getComponent: () => import('./OrderMatchDetail'),
    title: i18n.get('匹配订单'),
  },
])
export default class ContactsView extends Component {
  render() {
    return (
      <Keel>
        <KeelSingleViewHeader viewKey="SupplierBillList" showHeader={false} />
        <KeelViewBody classNameKey="content-main" />
      </Keel>
    );
  }
}
