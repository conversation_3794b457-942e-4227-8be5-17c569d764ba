import { TableVm } from '@ekuaibao/collection-definition'
import moment from 'moment'
import { observable, reaction, action, IReactionDisposer } from 'mobx'
import { PageMode, PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination'
import { showMessage } from '@ekuaibao/show-util'
import { Column } from '@ekuaibao/datagrid/lib/types/column'
import { app, app as api } from '@ekuaibao/whispered'
import { parseQuery2Select } from '@ekuaibao/lib/lib/parseQuery2Select'
import { getLogsColumns } from '../BillAdjustLogsUtils'
import { searchBudgetLogs } from '../../setttlement-checkin-action'

const fetchFixer = app.require<any>('@elements/data-grid/fetchFixer').default
export class BillAdjustLogsVM extends TableVm<any> {
  static NAME = Symbol('Budget_Operate_Logs')
  @observable columns: Column[] = []
  @observable selectedKeys: string[] = []
  isLoading = false
  public disposer: IReactionDisposer | null = null
  checkingBillId: any
  entityId: any

  init({ checkingBillId,entityId }) {
    this.checkingBillId = checkingBillId
    this.entityId = entityId
    this.initColumns()
    this.filters = this.getDefaultFilter()
    reaction(
      () => [this.currentPage, this.pageSize, this.sorters, this.filters, this.pageMode],
      () => {
        this.fetch()
      },
      { fireImmediately: true },
    )
  }

  initColumns() {
    this.columns = getLogsColumns()
  }

  getDefaultFilter = () => {
    return {
      createTime: [moment().subtract(1, 'years'),moment()]
    }
  }

  buildParams() {
    const p = this.params()
    let operateType
    let operateSource
    if (p?.filters?.operateType) {
      operateType = p.filters.operateType
      delete p.filters.operateType
    }
    if (p?.filters?.operateSource) {
      operateSource = p.filters.operateSource
      delete p.filters.operateSource
    }
    const param = fetchFixer(p)
    const query = parseQuery2Select(param)
    // if(this.entityId) {
    //   query.filterBy(`entityId=="${this.entityId}"`)
    // }
    if (this.checkingBillId) {
      query.filterBy(`checkingBillId=="${this.checkingBillId}"`)
    }
    if (operateType?.length) {
      query.filterBy(`operateType.in(${operateType.map(el => `"${el}"`).join()})`)
    }
    if (operateSource?.length) {
      query.filterBy(`operateSource.in(${operateSource.map(el => `"${el}"`).join()})`)
    }
    query.select(`staffId(...),...`)
    return { ...query.value() }
  }

  fetch = async () => {
    if (this.isLoading) {
      return
    }
    try {
      this.isLoading = true
      const { count, items } = await searchBudgetLogs(this.buildParams())
      this.isLoading = false
      this.dataTotal = count
      this.dataSource = this.pageMode === 'scroll' ? [...this.dataSource, ...items] : items
      this.selectedKeys = []
    } catch (e) {
      this.isLoading = false
      showMessage.error(e?.msg || e?.errorMessage)
    }
  }

  resetSelectedKeys = () => {
    this.selectedKeys = []
  }

  refreshData = () => {
    this.resetSelectedKeys()
    this.fetch()
  }

  @action
  handleFilterChange = (filter: any) => {
    if (
      filter?.createTime?.length &&
      filter.createTime[1].clone().subtract(1, 'year').subtract(1, 'day').valueOf() > filter.createTime[0].clone().valueOf()
    ) {
      showMessage.warning(i18n.get('操作日期最长跨度不得超过一年'))
      return
    }
    this.selectedKeys = []
    this.filters = filter
    this.dataTotal = 0
    this.dataSource = []
    this.currentPage = 1
  }
  @action
  handlePageChange = (pagination: PaginationConfig, pageMode: PageMode) => {
    this.pageSize = pagination.size
    this.currentPage = pagination.current
    if (this.pageMode !== pageMode) {
      this.pageMode = pageMode
      this.selectedKeys = []
      this.dataTotal = 0
      this.dataSource = []
      this.currentPage = 1
    }
  }
}
