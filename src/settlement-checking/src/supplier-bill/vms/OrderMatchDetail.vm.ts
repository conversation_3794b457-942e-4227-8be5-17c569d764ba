import { TableVm } from '@ekuaibao/collection-definition';
import { observable } from 'mobx';
import { app } from '@ekuaibao/whispered';
import {
    getTemps,
    formatStaff,
    formatTripDataLinkEntity
} from "../utils"
import { getSupplierAccount } from "../../setttlement-checkin-action"
const orderTripWhiteList = ['FLIGHT', 'HOTEL', 'TRAIN', 'TAXI']


export class OrderMatchDetailVm extends TableVm<any>{
    static NAME = Symbol('ORDER_MATCH_DETAIL');
    @observable dataSource: any[] = []
    @observable columns: any[] = [

    ]
    @observable filters: any[] = []
    @observable allColumns: any[] = []
    @observable selectedKeys: any[] = []
    @observable total: number = 0
    @observable SRDMap: any = {}
    @observable entityInfo = {}
    @observable entityInfoMap = {}
    @observable type: string = "TRAVEL_MANAGEMENT"
    async init(value: any) {
        await this.getStaffs()
        await this.getTripDataLinkEntityList()
        await this.getTripDataLinkEntityTemps(this.entityInfo)
    }
    async getStaffs() {
        const staffs = (await app.invokeService('@common:get:staffs:roleList:department')) ?? []
        this.SRDMap = formatStaff(staffs[0] ?? [])
    }
    async getTripDataLinkEntityList() {
        const { items = [] } = await app.invokeService('@tpp-v2:get:tripDataLinkEntityList', { type: this.type })
        this.entityInfo = formatTripDataLinkEntity(items, orderTripWhiteList)
    }
    async getTripDataLinkEntityTemps(entityInfo: any) {
        const platformId = typeof entityInfo.platformId === 'string' ? entityInfo.platformId : entityInfo.platformId.id
        const entity = await app.invokeService('@third-party-manage:get:entity:list', {
            id: platformId ?? ""
        })
        this.entityInfoMap = getTemps(entityInfo, entity)
    }
}

export class SettlementMatchDetailVm extends TableVm<any>{
    static NAME = Symbol('SETTLEMENT_MATCH_DETAIL');
    @observable categoryIds: any = []
    async init(val: any) {
        const { supplierAccountId } = val
        const { value } = await getSupplierAccount(supplierAccountId?.id)
        this.categoryIds = this.formatCategoy(value)
    }
    formatCategoy = (value: any) => {
        const accoutName = value?.name ?? '';
        const categoryIds = value?.categoryIds ?? [];
        return categoryIds.map((ca: any) => {
            const name = ca?.name?.replace(`${accoutName}-`, '')
            return { ...ca, name: name }
        })
    }
}