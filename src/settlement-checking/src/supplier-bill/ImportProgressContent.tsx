import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, Popconfirm } from 'antd'
import {
  execlImportAnalysis,
  execlImportCheck,
  execlImportUnique,
  getProgress
} from '../setttlement-checkin-action'
import './ImportProgressContent.less'
import { app as api } from '@ekuaibao/whispered'
import success_svg from "../images/importExcel/success.svg"
import warning_svg from "../images/importExcel/warning.svg"
import arrows_svg from "../images/importExcel/arrows.svg"
import finish_svg from "../images/importExcel/finish-icon.svg"
import succed_svg from "../images/importExcel/succed-icon.svg"
import step_default_icon from "../images/importExcel/step-default-icon.svg"
import step_finish_icon from "../images/importExcel/step-finish-icon.svg"
import step_warninng_icon from "../images/importExcel/step-warninng-icon.svg"
import step_error_icon from "../images/importExcel/step-error-icon.svg"
import step_process_icon from "../images/importExcel/step-process-icon.svg"
import SVGIcon from "../components/SVGIcon";
import {get} from "lodash";
import MessageCenter from '@ekuaibao/messagecenter'

const bus = new MessageCenter()

const TipsPopconfirm = () => {
  return (
    <div>
      <div>你要取消导入流程吗?</div>
      <div>可能会丢失未保存的数据</div>
    </div>
  )
}

const ErrorFooterCategory: React.FC = (props) => {
  const { onCloseModal, onNextStep, addBill, value, file } = props
  const handleNext = async () => {
    const resultCheck = await execlImportCheck({checkingBillId: value?.id, fileKey: file?.key, checkFlag: true})
    if(resultCheck?.value?.flag) {
      bus.emit('onAway', false)
      bus.emit('onLose', true)
      onNextStep()
    }
  }
  const handleAnew = () => {
    onCloseModal()
    addBill && addBill()
  }
  return (
    <div className="modal-footer">
      <Button className="mr-8" onClick={handleAnew}>
        {i18n.get('重新导入')}
      </Button>
      <Button type="primary" onClick={handleNext}>
        {i18n.get('忽略异常，继续导入')}
      </Button>
    </div>
  )
}

const ImportingFooterCategory: React.FC = (props) => {
  const { onCloseModal } = props
  return (
    <div className="modal-footer">
        <Popconfirm
          placement="top"
          title={<TipsPopconfirm />}
          onCancel={onCloseModal}
          okText="点错了"
          cancelText="仍然关闭">
          <Button className="mr-8">{i18n.get('取消')}</Button>
        </Popconfirm>
      <Button type="primary" disabled={true}>
        {i18n.get('导入中...')}
      </Button>
    </div>
  )
}

const ErrorDownloadFooter: React.FC = (props) => {
  const { onCloseModal, addBill, keyfile } = props
  const handleAnew = () => {
    onCloseModal()
    addBill && addBill()
  }
  const handleDowload = () => {
    const fileName = props?.fileName || '数据格式标错文件'
    return api.emit('@vendor:download', keyfile, fileName)
  }
  return (
    <div className="modal-footer" style={{display: 'flex', justifyContent: 'space-between'}}>
      {
        keyfile && <a style={{color: '#22B2CC', fontSize: '14px'}} onClick={handleDowload}>{i18n.get('下载标错文件')}</a>
      }
      <Button onClick={handleAnew} >
        {i18n.get('重新导入')}
      </Button>
    </div>
  )
}

const WriteFooter: React.FC = (props) => {
  const { onCloseModal, addBill, keyfile, getList, keel, _ignoreCatch, value, isDetail } = props
  const handleDowload = () => {
    const fileName = '标错文件'
    return api.emit('@vendor:download', keyfile, fileName)
  }
  const handleResult = async () => {
    if(isDetail) {
      await getList()
      return onCloseModal()
    }
    const result = await getList({})
    const item = result?.items.find(i => i?.id === value?.id)
    if(item) {
      const canRefresh = item?.tppBillNo && (item?.tppState === 'todo' || item?.tppState === 'reject')
      keel?.open('SupplierBillDetail', {
        data: { id: item?.id, canRefresh, canImport: item.importMethod !== 'api' },
        title: `${i18n.get('供应商：')}${get(item, 'supplierAccountId.name', '')}`,
      });
    }
    onCloseModal()
  }
  return (
    <div className="modal-footer" style={{display: 'flex', justifyContent: 'space-between'}}>
      {keyfile ? <a style={{color: '#22B2CC', fontSize: '14px'}} onClick={handleDowload}>{i18n.get('下载标错文件')}</a> : <div></div>}
      <div>
        <Button type="primary" onClick={handleResult}>
          {i18n.get('查看导入结果')}
        </Button>
      </div>
    </div>
  )
}

const ErrorContent: React.FC = (props) => {
  const [isShow, setIsShow] = useState<boolean>(true)
  const [isLose, setIsLose] = useState<boolean>(false)
  useEffect(() => {
    bus.on('onAway', handleAway)
    bus.on('onLose', onLose)
    return () => {
      bus.un('onAway', handleAway)
      bus.un('onLose', onLose)
    }
  })
  const { errorMaps } = props
  const handleAway = (isShow: boolean) => {
    setIsShow(isShow)
  }
  const onLose = (isLose: boolean) => {
    setIsLose(isLose)
  }
  return <>
    <div className='__import-error-content'>
      <div className='import-error-header'>
        <div className='header-left'>账单品类异常</div>
        <div className='header-right' onClick={() => handleAway(!isShow)}>
          <span className='mr-8'>{
            isShow ? i18n.get('收起') : isLose ? <span className='away'>{i18n.get('已忽略异常')} <SVGIcon className='ml-4' src={warning_svg} /></span> : i18n.get('展开')
          }</span>
          <SVGIcon className={isShow ? '': 'rotate-arrows'} src={arrows_svg} />
        </div>
      </div>
      {
        isShow ?
          <div className='import-error-body'>
            <div className='import-name'>
              导入品类与系统品类不一致
            </div>
            <div className='import-tr'>
              <div className='tr-c'>系统品类</div>
              <div className='tr-c'>导入品类</div>
              <div className='tr-c'></div>
            </div>
            <div className='import-body'>
              {
                errorMaps.map((item) =>
                  <div className='import-td'>
                    <div className='td-c'>{item.targetCode ? item.targetCode : '-'}</div>
                    <div className={[item.targetCode !== item.sourceCode ? 'td-c td-color': 'td-c']}>{item.sourceCode ? item.sourceCode : '-'}</div>
                    <div className='td-c'>
                      {
                        item.targetCode === item.sourceCode ? <SVGIcon src={success_svg} /> : <SVGIcon src={warning_svg} />
                      }
                    </div>
                  </div>)
              }
            </div>
          </div> : null
      }
    </div>
  </>
}

const IdentContent: React.FC = (props) => {
  const { _ignoreCatch, keyfile } = props
  return <div className='__import-ident-content'>
    {
      (_ignoreCatch || keyfile) ?
        <>
          <SVGIcon src={finish_svg} />
          <div className='import-success'>{i18n.get('导入完成')}</div>
        </> :
        <>
          <SVGIcon src={succed_svg} />
          <div className='import-success'>{i18n.get('导入成功')}</div>
        </>
    }

  </div>
}

const stepsDispose = (params: any) => {
  params._ignoreCatch = false
  const steps = [
    {
      content: '等待账单品类检查',
      status: 'start',
      dot: <SVGIcon src={step_default_icon} />,
      onProcess: () => {
        return new Promise(async (resovle, reject) => {
          try {
            let resultCheck
            if (params?.file?.key && params?.value?.id) {
              resultCheck = await execlImportCheck({checkingBillId: params?.value?.id, fileKey: params?.file?.key})
            }
            if(resultCheck?.value?.flag) {
              resovle({
                excelName: params?.excelName,
                isCompleted: true,
                content: `已完成账单品类检查`,
                dot: <SVGIcon src={step_finish_icon} style={{width: '16px', height: '16px'}} />
              })
            }else if(resultCheck?.value?.flag === false) {
              params._ignoreCatch = true
              const errorMaps = resultCheck?.value?.errorMaps
              const content = errorMaps ? <ErrorContent errorMaps={errorMaps} /> : '未找到对应的品类信息'
              reject({
                excelName: params?.excelName,
                isCompleted: false,
                content,
                minimizeContent: '品类校验异常',
                dot: <SVGIcon src={step_warninng_icon} />,
                getFooter: errorMaps ? ({ onCloseModal, onNextStep }) => {
                  return <ErrorFooterCategory onCloseModal={onCloseModal} onNextStep={onNextStep} addBill={params?.addBill} {...params}/>
                } : null
              })
            }else {
              reject({
                excelName: params?.excelName,
                isCompleted: false,
                content: `未找到对应的品类信息`,
                dot: <SVGIcon src={step_error_icon} />
              })
            }
          }catch (err) {
            reject({
              excelName: params?.excelName,
              isCompleted: false,
              content: `未找到对应的品类信息`,
              dot: <SVGIcon src={step_error_icon} />
            })
          }
        })
      },
      getFooter: ({ onCloseModal, onNextStep }) => {
        return <ImportingFooterCategory onCloseModal={onCloseModal} onNextStep={onNextStep} />
      }
    },
    {
      content: '等待检查数据格式',
      status: 'start',
      dot: <SVGIcon src={step_default_icon} />,
      onProcess: () => {
        return new Promise(async (resovle, reject) => {
          try{
            const result = await getProgress({fileKey: params?.file?.key})
            if(result) {
              const {check_size, total_size, status, keyfile} = result?.value
              if(keyfile) {
                return reject({
                  excelName: params?.excelName,
                  isCompleted: false,
                  content: `数据格式出错`,
                  dot: <SVGIcon src={step_error_icon} />,
                  getFooter: ({ onCloseModal, onNextStep }) => {
                    return <ErrorDownloadFooter onCloseModal={onCloseModal} onNextStep={onNextStep} addBill={params?.addBill} keyfile={keyfile}/>
                  }
                })
              }
              if(total_size === check_size && status === 'SUCCESS') {
                await execlImportUnique({ checkingBillId: params?.value?.id, fileKey: params?.file?.key })
                resovle({
                  excelName: params?.excelName,
                  isCompleted: true,
                  content: `已完成数据格式检查`,
                  dot: <SVGIcon src={step_finish_icon} style={{width: '16px', height: '16px'}} />
                })
              } else if (status === 'FAILURE') {
                reject({
                  excelName: params?.excelName,
                  isCompleted: false,
                  content: `完整性校验出错`,
                  dot: <SVGIcon src={step_error_icon} />,
                  getFooter: ({ onCloseModal, onNextStep }) => {
                    return <ErrorDownloadFooter onCloseModal={onCloseModal} onNextStep={onNextStep} addBill={params?.addBill} keyfile={keyfile}/>
                  }
                })
              } else {
                const percent = getPercent(check_size, total_size)
                resovle({
                  excelName: params?.excelName,
                  isCompleted: false,
                  content: `正在校验数据格式... ${percent}%`,
                  dot: <SVGIcon src={step_process_icon} />,
                  getFooter: ({ onCloseModal, onNextStep }) => {
                    return <ImportingFooterCategory onCloseModal={onCloseModal} onNextStep={onNextStep} />
                  }
                })
              }
            }
          } catch (err) {
            reject({
              excelName: params?.excelName,
              isCompleted: false,
              content: `数据格式出错`,
              dot: <SVGIcon src={step_error_icon} />
            })
          }
        })
      },
      getFooter: ({ onCloseModal, onNextStep }) => {
        return <ImportingFooterCategory onCloseModal={onCloseModal} onNextStep={onNextStep} />
      }
    },
    {
      content: '等待检查数据唯一性',
      status: 'start',
      dot: <SVGIcon src={step_default_icon} />,
      onProcess: () => {
        return new Promise(async (resovle, reject) => {
          try {
            const result = await getProgress({ fileKey: params?.file?.key })
            if (result) {
              const { check_size, total_size, status, keyfile, unique_size, unique_filekey } = result?.value
              if (unique_filekey) {
                return reject({
                  excelName: params?.excelName,
                  isCompleted: false,
                  content: `账单明细重复（请下载标错文件）`,
                  dot: <SVGIcon src={step_error_icon} />,
                  getFooter: ({ onCloseModal, onNextStep }) => {
                    return <ErrorDownloadFooter onCloseModal={onCloseModal} onNextStep={onNextStep} addBill={params?.addBill} keyfile={unique_filekey} fileName={'账单明细重复标错文件'} />
                  }
                })
              }
              if (total_size === unique_size && status === 'SUCCESS') {
                await execlImportAnalysis({ checkingBillId: params?.value?.id, fileKey: params?.file?.key })
                resovle({
                  excelName: params?.excelName,
                  isCompleted: true,
                  content: `已完成数据重复性校验`,
                  dot: <SVGIcon src={step_finish_icon} style={{ width: '16px', height: '16px' }} />
                })
              } else if (status === 'FAILURE') {
                reject({
                  excelName: params?.excelName,
                  isCompleted: false,
                  content: `数据重复性校验出错`,
                  dot: <SVGIcon src={step_error_icon} />,
                  getFooter: ({ onCloseModal, onNextStep }) => {
                    return <ErrorDownloadFooter onCloseModal={onCloseModal} onNextStep={onNextStep} addBill={params?.addBill} fileName={'账单明细重复标错文件'} keyfile={unique_filekey} />
                  }
                })
              } else {
                const percent = getPercent(unique_size, total_size)
                resovle({
                  excelName: params?.excelName,
                  isCompleted: false,
                  content: `正在校验数据重复性... ${percent}%`,
                  dot: <SVGIcon src={step_process_icon} />,
                  getFooter: ({ onCloseModal, onNextStep }) => {
                    return <ImportingFooterCategory onCloseModal={onCloseModal} onNextStep={onNextStep} />
                  }
                })
              }
            }
          } catch (err) {
            reject({
              excelName: params?.excelName,
              isCompleted: false,
              content: `数据重复性校验出错`,
              dot: <SVGIcon src={step_error_icon} />
            })
          }
        })
      },
      getFooter: ({ onCloseModal, onNextStep }) => {
        return <ImportingFooterCategory onCloseModal={onCloseModal} onNextStep={onNextStep} />
      }
    },
    {
      content: '等待写入数据',
      status: 'start',
      dot: <SVGIcon src={step_default_icon} />,
      onProcess: () => {
        return new Promise(async (resovle, reject) => {
          try {
            const resultProgress = await getProgress({fileKey: params?.file?.key})
            const {analysis_size, total_size, keyfile, status} = resultProgress?.value
            if(total_size === analysis_size && status === 'SUCCESS') {
              resovle({
                excelName: params?.excelName,
                isCompleted: true,
                content: `已写入数据`,
                dot: <SVGIcon src={step_finish_icon} style={{width: '16px', height: '16px'}} />,
                getIdent: () => {
                  return <IdentContent {...params}/>
                },
                getFooter: ({ onCloseModal, onNextStep }) => {
                  return <WriteFooter onCloseModal={onCloseModal} onNextStep={onNextStep} keyfile={keyfile} {...params} />
                }
              })
            } else if (status === 'FAILURE') {
              reject({
                excelName: params?.excelName,
                isCompleted: false,
                content: `数据写入异常`,
                dot: <SVGIcon src={step_error_icon} />,
                getFooter: ({ onCloseModal, onNextStep }) => {
                  return <ErrorDownloadFooter onCloseModal={onCloseModal} onNextStep={onNextStep} addBill={params?.addBill} keyfile={keyfile}/>
                }
              })
            } else {
              const percent = getPercent(analysis_size, total_size)
              resovle({
                excelName: params?.excelName,
                isCompleted: false,
                content: `正在写入数据... ${percent}%`,
                dot: <SVGIcon src={step_process_icon} />,
                getFooter: ({ onCloseModal, onNextStep }) => {
                  return <ImportingFooterCategory onCloseModal={onCloseModal} onNextStep={onNextStep} />
                }
              })
            }
          }catch (err) {
            reject({
              excelName: params?.excelName,
              isCompleted: false,
              content: `数据写入异常`,
              dot: <SVGIcon src={step_error_icon} />,
              getFooter: ({ onCloseModal, onNextStep }) => {
                return <ErrorDownloadFooter onCloseModal={onCloseModal} onNextStep={onNextStep} addBill={params?.addBill} keyfile={''}/>
              }
            })
          }
        })
      },
      getFooter: ({ onCloseModal, onNextStep }) => {
        return <ImportingFooterCategory onCloseModal={onCloseModal} onNextStep={onNextStep} />
      }
    },
  ]
  return steps
}

export const getSteps = (params: any) => {
  return stepsDispose(params)
}

function getPercent(num: any, total: any) {
  num = parseFloat(num);
  total = parseFloat(total);
  if (isNaN(num) || isNaN(total)) {
    return 0;
  }
  if (num === total) {
    return 99;
  }
  return total <= 0 ? "0" : (Math.round(num / total * 10000) / 100).toFixed();
}
