import React, { useEffect } from 'react';
import { app } from '@ekuaibao/whispered';
import { useObserver } from 'mobx-react-lite';
import { SettlementMatchDetailVm } from '../vms/OrderMatchDetail.vm';
import { useInstance, provider } from '@ekuaibao/react-ioc';
import SupplierBill from '../SupplierBill';
import { showModal } from '@ekuaibao/show-util';
import MessageCenter from '@ekuaibao/messagecenter';
import style from './OrderMatchDetail.module.less';
import { matchUnbind } from '../../setttlement-checkin-action';
interface IProps {
  [key: string]: any;
}
const bus = new MessageCenter();
const SettlementDataLinkTable: React.FC<IProps> = (props) => {
  const { data = {}, showMatch = false, columnType }: any = props;
  const settlementVm = useInstance<SettlementMatchDetailVm>(SettlementMatchDetailVm.NAME);
  useEffect(() => {
    settlementVm?.init(data);
    return () => { };
  }, []);
  useEffect(() => {
    bus?.emit('refresh:data')
  }, [showMatch])
  const onBind = async (line: any) => {
    await app.open('@settlement-checkin:OrderMatchModel', { data, line });
    bus?.emit('refresh:data');
  };
  const unBind = (line: any) => {
    showModal.confirm({
      title: i18n.get('确定解绑？'),
      onOk: async () => {
        await matchUnbind({ checkingDetailId: line?.dataLink?.id });
        bus?.emit('refresh:data');
      },
    })
  };
  const fixCol = (columns: any[]) => {
    let col = [
      {
        title: i18n.get('匹配状态'),
        key: 'matchState',
        dataIndex: 'matchState',
        width: 150,
        label: i18n.get('匹配状态'),
        value: 'matchState',
        render(_: any, line: any) {
          const f = line?.dataLink?.matchFlag;
          return (
            <div className={style[f ? 'action-btn' : 'warn-btn']}>{f ? i18n.get('已匹配') : i18n.get('未匹配')}</div>
          );
        },
      },
    ];
    return renderAction(col.concat(columns));
  };
  const renderAction = (columns: any[]) => {
    return columns.concat([
      {
        title: i18n.get('操作'),
        key: 'action',
        dataIndex: 'action',
        width: 100,
        label: i18n.get('操作'),
        value: 'action',
        fixed: 'right',
        render(_: any, line: any) {
          const f = line?.dataLink?.matchFlag;
          return (
            <div className={style['action-btn']} onClick={() => (f ? unBind(line) : onBind(line))}>
              {f ? i18n.get('解绑') : i18n.get('手动匹配')}
            </div>
          );
        },
      },
    ]);
  };

  return useObserver(() => (
    <SupplierBill
      categoryIds={settlementVm?.categoryIds}
      hiddenForm={true}
      id={data?.id}
      fixCol={fixCol}
      bus={bus}
      showMatch={showMatch}
      columnType={columnType}
    />
  ));
};
export default provider([SettlementMatchDetailVm.NAME, SettlementMatchDetailVm])(SettlementDataLinkTable);
