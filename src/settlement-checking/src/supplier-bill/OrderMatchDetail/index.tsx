import React, { useState, useEffect } from 'react';
import { app } from '@ekuaibao/whispered';
import { useObserver } from 'mobx-react-lite';
import { TableWrapper } from '../../components/TableWrapper';
const EKBIcon = app.require<any>('@elements/ekbIcon');
import { T } from '@ekuaibao/i18n';
import { KEELVM, IKeel } from '@ekuaibao/keel';
import { useInstance } from '@ekuaibao/react-ioc';
import styles from './OrderMatchDetail.module.less';
import { Checkbox } from 'antd';
import OrderDataLinkTable from './OrderDataLinkTable';
import SettlementDataLinkTable from './SettlementDataLinkTable';
interface IProps {
  [key: string]: any;
}

const OrderMatchDetail: React.FC<IProps> = (props) => {
  const {
    data: { line },
  } = props;
  const keel: IKeel = useInstance(KEELVM);
  const [showMatch, setShowMatch] = useState(false);
  const showMatchData = (val: any) => {
    setShowMatch(val?.target?.checked);
  };
  useEffect(() => {
    return () => {};
  }, []);
  return useObserver(() => (
    <div className={styles['order-match-detail']}>
      <div className={styles['order-match-title']}>
        <div
          onClick={() => {
            keel?.closeTo(0);
          }}
        >
          <EKBIcon name="#EDico-APP-back" className="icc" />
          <T name="对账单详情" />
        </div>
        <div>
          <Checkbox onChange={showMatchData}>
            <T name="仅展示未匹配的数据" />
          </Checkbox>
        </div>
      </div>
      <div className={styles['order-match-content']}>
        <div className={styles['content-tab']}>
          <OrderDataLinkTable renderAction={false} data={line} columnType="TRIP_ORDER_DETAIL_MATCH"/>
        </div>
        <div className={styles['content-tab']}>
          <SettlementDataLinkTable data={line} showMatch={showMatch} columnType="STATEMENT_DETAIL_MATCH"/>
        </div>
        {/* <TableVm vm={orderVm} className="content-tab"></TableVm>
        <TableVm vm={settlementVm} className="content-tab"></TableVm> */}
      </div>
    </div>
  ));
};
export default OrderMatchDetail;

const TableVm = (vm: any) => {
  return (
    <TableWrapper
      tableProps={
        {
          dataSource: vm.dataSource ?? [],
          columns: vm.columns ?? [],
          filters: vm.filters,
          scrolling: vm.scrolling,
          pageSize: vm.dataSource?.length ?? 0,
          groupPanel: { visible: true, allowColumnDragging: false },
          selectedRowKeys: vm.selectedKeys,
          fixedSelect: true,
        } as any
      }
    ></TableWrapper>
  );
};
