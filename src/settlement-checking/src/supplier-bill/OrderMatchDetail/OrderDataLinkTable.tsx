import React, { useEffect } from 'react';
import { app } from '@ekuaibao/whispered';
import { useObserver } from 'mobx-react-lite';
import { OrderMatchDetailVm } from '../vms/OrderMatchDetail.vm';
import { useInstance, provider } from '@ekuaibao/react-ioc';
import style from '../SupplierBillDetail.module.less';
import MessageCenter from '@ekuaibao/messagecenter';
const DataLinkTableWrapper = app.require('@elements/DataLinkTable/DataLinkTableWrapper');
interface IProps {
  [key: string]: any;
}

const OrderDataLinkTable: React.FC<IProps> = (props) => {
  const { selectedRowKeys, onSelectedChange } = props;
  const bus = props.bus || new MessageCenter();
  const { id } = props.data;
  const orderVm = useInstance<OrderMatchDetailVm>(OrderMatchDetailVm.NAME);
  useEffect(() => {
    orderVm?.init(props);
    return () => {};
  }, []);

  
  return useObserver(() => (
    <div className={style['supplier_bill_table']}>
      <DataLinkTableWrapper
        locale={{ emptyText: i18n.get('暂无数据') }}
        bus={bus}
        getLocal={() => {}}
        SRDMap={orderVm.SRDMap}
        action={props.renderAction}
        entityInfo={orderVm.entityInfo}
        entityInfoMap={orderVm.entityInfoMap}
        type={orderVm.type}
        checkingBillId={id}
        selectedRowKeys={selectedRowKeys}
        onSelectedChange={onSelectedChange}
      />
    </div>
  ));
};
export default provider([OrderMatchDetailVm.NAME, OrderMatchDetailVm])(OrderDataLinkTable);
