@import '~@ekuaibao/web-theme-variables/styles/default.less';
@import '~@ekuaibao/web-theme-variables/styles/colors.less';
@import '~@ekuaibao/eui-styles/less/token.less';

.supplier_bill_detail {
  width: 100%;
  display: flex;
  flex: 1;
  min-width: 1152px;
  flex-direction: column;
  background-color: #f7f7f7;
  position: relative;

  :global {
    .d_f {
      display: flex;
    }

    .f_c {
      display: flex;
      flex-direction: column;
    }

    .all_center {
      justify-content: center;
      align-items: center;
    }

    .j_center {
      justify-content: center;
    }

    .a_center {
      align-items: center;
    }

    .r_view {
      position: absolute;
      top: 52px;
      right: @space-6;
    }

    .header {
      background: white;
      height: 52px;
      width: 100%;
      display: flex;
      padding: 0 24px;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 20px;
        font-weight: 500;
        max-width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .icc {
          height: 20px;
          margin-right: 5px;
          cursor: pointer;
        }
      }

      .right_view {
        display: flex;

        .ico {
          margin-right: 8px;
        }
      }
    }

    .content_view {
      display: flex;
      flex-direction: column;
      padding: @space-6;
      flex: 1;

      .bill_date {
        opacity: 0.92;
        font-size: 16px;
        font-weight: 600;
        color: #142234;

        .ic_c {
          margin-right: 8px;
          cursor: pointer;
        }
      }

      .bill_info_view {
        display: flex;
        height: 110px;
        background: var(--brand-base);
        border-radius: 4px;
        margin-top: 8px;
        align-items: center;
        padding: 0 32px;
        position: relative;
        box-shadow: 0px 2px 6px 0px rgba(34, 178, 204, 0.25);

        .item {
          color: #ffffff;

          .top_ {
            opacity: 0.92;
            font-size: 24px;
            font-family: DINAlternate, DINAlternate-Bold, sans-serif;
            font-weight: 700;
          }

          .bot {
            opacity: 0.76;
            font-size: 14px;
          }
        }

        .sip {
          width: 1px;
          height: 45px;
          opacity: 0.1;
          background: #e6e6e6;
          margin: 0 32px;
        }

        .iconcc {
          width: 111px;
          height: 111px;
          position: absolute;
          right: 0;
          bottom: -40px;
        }
      }

      .suppler_view {
        width: 342px;
        height: 206px;
        background: #ffffff;
        border-radius: 4px;
        padding: 16px;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);

        .label {
          opacity: 0.76;
          font-size: 14px;
          text-align: right;
          color: #142234;
        }

        .content {
          margin-left: 17px;
          opacity: 0.92;
          font-size: 14px;
          color: #142234;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .table {
        overflow: hidden;
        height: 206px;
        margin-left: 16px;
        display: flex;
        flex: 1;
        flex-direction: column;
        background: #ffffff;
        border-radius: 4px;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);

        .list_view {
          height: 168px;
          overflow: auto;

          .item {
            display: flex;
            height: 56px;
            border-bottom: 1px solid #ebebeb;
          }

          &>div:nth-child(odd) {
            background-color: #f7f7f7;
          }

          &>div:last-child {
            border-bottom: 0;
          }

          &>div:only-child {
            background-color: #f7f7f7;
            height: 168px;
          }
        }

        .b_t {
          border-top: 1px solid #ebebeb;
        }

        .b_b {
          border-bottom: 1px solid #ebebeb;
        }

        .b_l {
          border-left: 1px solid #ebebeb;
        }

        .col1 {
          width: 200px;
          height: 100%;
        }

        .col {
          flex: 1;
          height: 100%;
        }

        .heade {
          opacity: 0.92;
          font-size: 14px;
          color: #263344;
        }

        .column {
          opacity: 0.92;
          font-size: 14px;
          color: #374353;

          .number {
            opacity: 0.92;
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Semibold, sans-serif;
            font-weight: 600;
            color: #46515f;
          }
        }
      }
    }

    .query_view {
      margin-top: 16px;
      background: #ffffff;
      border-radius: 4px;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
      padding: @space-6 @space-6 0;

      .row {
        display: flex;

        .item {
          width: 250px;
        }

        .item2 {
          width: 200px;
        }

        .item3 {
          width: 250px;
          margin-left: 12px;
        }
      }

      .form_item {
        height: 32px;
        margin-bottom: @space-6;
      }
    }
  }
}

.supplier_bill_table {
  display: flex;
  flex-direction: column;
  flex: 1;
  background: white;
  margin-top: 16px;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
  // height: 200px;

  :global {
    .table-wrapper {
      height: 500px;
    }

    .data_header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .data_right {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 40px;
        margin-top: -8px;
        padding-right: 16px;

        .btn {
          margin-right: 14px;
          background: #f7f7f7;
          border-radius: 3px;
          opacity: 0.76;
          font-size: 14px;
          color: #142234;
        }
      }
    }

    .data_footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
    }

    .diff_scenes {
      font-size: 14px;
      color: var(--brand-base);
      cursor: pointer;
      line-height: 32px;
      margin-right: 14px
    }

    .diff_scenes_match {
      font-size: 14px;
      color: var(--brand-base);
      cursor: pointer;
      margin-right: 14px;
      height: 24px;
    }
  }
}