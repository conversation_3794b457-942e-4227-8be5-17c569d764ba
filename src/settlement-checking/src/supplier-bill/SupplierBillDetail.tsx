/*
 * @Description: 供应商账户详情
 * @Creator: chencan<PERSON>han
 * @Date: 2021-05-28 10:59:52
 */
import React from 'react';
import style from './SupplierBillDetail.module.less';
import { app } from '@ekuaibao/whispered';
import { Button, Row, Col, Tooltip } from 'antd';
const EKBIcon = app.require<any>('@elements/ekbIcon');
import { injectKeel, KeelVM } from '@ekuaibao/keel';
import { get } from 'lodash';
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
import SupplierBill from './SupplierBill';
import {
  getCheckingBill,
  getSupplierAccount,
  refreshCheckingBills,
  getCheckBillLog,
  checkCheckingBillProgress
} from './../setttlement-checkin-action';
import { showMessage } from '@ekuaibao/show-util';
import ekb from '../images/icons/ekb.svg';
const Money = app.require<any>('@elements/puppet/Money');
import MessageCenter from '@ekuaibao/messagecenter';
import {getSteps} from "./ImportProgressContent";
import { LayerDef } from '@ekuaibao/enhance-layer-manager/types/EnhanceLayerBase';

interface SupplierBillDetailProps {}
interface SupplierBillDetailState {
  accout: any
  bill: any
  categoryIds: []
  currentTab: string
  supplierArchiveType?: string
  importMethod?: string
}
@EnhanceFormCreate()
export default class SupplierBillDetail extends React.Component<SupplierBillDetailProps, SupplierBillDetailState> {
  @injectKeel()
  keel: KeelVM | undefined;
  state:SupplierBillDetailState = {
    accout: {},
    bill: {}, // 账单详情
    categoryIds: [],
    currentTab: 'billDetail',
  };
  bus = new MessageCenter();
  async componentDidMount() {
    this.get_CheckingBill();
    const { value } = await this.fnCheckCheckBillLog();
    if (value?.message?.length) {
      this.handleOperateLog();
    }
  }

  private get_CheckingBill() {
    const id = this.props?.data?.id;
    getCheckingBill(id).then((res) => {
      const bill = res?.value || {};
      this.setState({ bill });
      getSupplierAccount(bill?.supplierAccountId).then((resp) => {
        const accout = resp?.value || {};
        const accoutName = accout?.name || '';
        const categoryIds = accout?.categoryIds.filter((line) => !!line) || [];
        let temp: any[] = [];
        categoryIds.forEach((ca: any) => {
          const name = ca?.name?.replace(`${accoutName}-`, '');
          temp.push({ ...ca, name: name });
        });
        this.setState({ accout, categoryIds: temp });
      });
    });
  }

  refresh = async () => {
    refreshCheckingBills({ checkingBillId: this.props?.data?.id })
      .then((res) => {
        if (res.value) {
          app.open('@settlement-checkin:SubmitCheckingModal', {
            result: 'PENDING',
            failureReasons: [],
            type: 'CHECKING_FEE_PROGTESS',
            progressMode: 'progress',
            checkingBillId: this.props?.data?.id,
            closeText: '确定',
            action: checkCheckingBillProgress,
          });
        }
      })
      .catch((err) => {
        showMessage.error(err?.errorMessage);
      });
  };

  handleAfreshFee = () => {};

  handleOperateLog = () => {
    app.open('@bills:LogModal', {
      title: i18n.get('错误日志'),
      hasPagination: false,
      logWarnTip: {
        title: i18n.get('部分账单未正确生成费用'),
        content: i18n.get('请检查以下字段的配置是否正确，配置完成后需返回账单详情页重新生成费用'),
        actionText: i18n.get('费用生成配置'),
        action: (layer: LayerDef) => {
          layer.emitCancel();
          app.go('/settlement-checkin-setting');
        },
      },
      showHistoryIcon: false,
      onGetLog: async () => {
        const { value } = await this.fnCheckCheckBillLog();
        const result = { items: [] };
        if (value?.message?.length) {
          result.items = value.message.map((msg: string) => {
            return { __left: msg };
          });
        }
        return result;
      },
    });
  };

  fnCheckCheckBillLog = () => {
    return getCheckBillLog({ checkingBillId: this.props?.data?.id });
  };

  importExcel = async () => {
    app.open('@settlement-checkin:AddBillModal', {
      title: i18n.get('导入账单'),
      isDetail: true
    }).then((result: any) => {
      app.open('@layout5:ImportProgressModal', { defaultSteps: getSteps({ value: { id: this.props?.data?.id }, isDetail: true, ...result, addBill: this.importExcel, getList: this.get_CheckingBill?.bind(this) }) })
    }).catch((err) => {
      showMessage.error(err?.errorMessage || err?.message || '请重新导入');
    });
  };
  private CheckingStateObj = {
    NONE: i18n.get('未对账'),
    CHECKING_TODO: i18n.get('待对账'),
    CHECKING_DOING: i18n.get('对账中'),
    CHECKING_DONE: i18n.get('已对账'),
  };
  private InvoiceStateObj = {
    NONE: i18n.get('未开票'),
    INVOICE_TODO: i18n.get('待开票'),
    INVOICE_DOING: i18n.get('开票中'),
    INVOICE_DONE: i18n.get('已开票'),
  };
  private SettlementStateObj = {
    NONE: i18n.get('未结算'),
    SETTLEMENT_TODO: i18n.get('待结算'),
    SETTLEMENT_DOING: i18n.get('结算中'),
    SETTLEMENT_DONE: i18n.get('已结算'),
  };

  render() {
    const { bill, accout, categoryIds } = this.state;
    const statisticsInfo = get(bill, 'statisticsInfo', {});
    return (
      <div className={style['supplier_bill_detail']}>
        <div className="header">
          <div className="title">
            <EKBIcon
              name="#EDico-APP-back"
              className="icc"
              onClick={() => {
                this.keel?.closeTo(0);
              }}
            />
            {i18n.get('账单详情')}
          </div>
          <div className="right_view">
            {this.props.data?.canImport && <Button onClick={this.importExcel}>{i18n.get('导入')}</Button>}
            <Button style={{ marginLeft: 8 }} onClick={this.handleOperateLog}>
              {i18n.get('错误日志')}
            </Button>
            <Button style={{ marginLeft: 8 }} type={'primary'} onClick={this.refresh}>
              {i18n.get('重新生成费用')}
            </Button>
            <Tooltip trigger={'hover'} overlay={'未生成子对账单的费用可以重新生成'} placement={'bottom'}>
              <div className={'horizontal'}>
                <EKBIcon name="#EDico-help2" className={'ml-4 w-20 h-20'} />
              </div>
            </Tooltip>
          </div>
        </div>
        <div className="content_view">
          <div className="bill_date">
            <EKBIcon name="#ico-7-icon_date" className="ic_c" />
            {get(bill, 'name')}
          </div>
          <div className="bill_info_view">
            <div className="item">
              <div className="top_">
                <Money showSymbol={false} value={get(statisticsInfo, 'TOTAL.consumptionAmount', 0) || 0} />
              </div>
              <div className="bot">消费总额/元</div>
            </div>
            <div className="item" style={{ marginLeft: 55 }}>
              <div className="top_">{get(statisticsInfo, 'TOTAL.consumptionNum', 0) || 0}</div>
              <div className="bot">消费条数</div>
            </div>
            <div className="sip"></div>
            <div className="item">
              <div className="top_">
                <Money showSymbol={false} value={get(statisticsInfo, 'TOTAL.totalAmount', 0) || 0} />
              </div>
              <div className="bot">企业支付/元</div>
            </div>
            <div className="item" style={{ marginLeft: 55 }}>
              <div className="top_">{get(statisticsInfo, 'TOTAL.totalNum', 0) || 0}</div>
              <div className="bot">支付条数</div>
            </div>
            <img className="iconcc" src={ekb} />
          </div>
          <div className="d_f" style={{ marginTop: 16 }}>
            <div className="suppler_view">
              <Row>
                <Col span={8} className="label">
                  供应商名称：
                </Col>
                <Col span={16}>
                  <Tooltip placement="topLeft" title={get(accout, 'supplierArchiveObj.name')}>
                    <div className="content">{get(accout, 'supplierArchiveObj.name')}</div>
                  </Tooltip>
                </Col>
              </Row>
              <Row style={{ marginTop: 8 }}>
                <Col span={8} className="label">
                  账户类型：
                </Col>
                <Col span={16}>
                  <div className="content">
                    {get(accout, 'settlementType') === 'PRIECHARGE' ? i18n.get('预存账户') : i18n.get('授信账户')}
                  </div>
                </Col>
              </Row>
              <Row style={{ marginTop: 8 }}>
                <Col span={8} className="label">
                  对账状态：
                </Col>
                <Col span={16}>
                  <div className="content">{get(this.CheckingStateObj, get(bill, 'checkingState', 'NONE'))}</div>
                </Col>
              </Row>
              <Row style={{ marginTop: 8 }}>
                <Col span={8} className="label">
                  开票状态：
                </Col>
                <Col span={16}>
                  <div className="content">{get(this.InvoiceStateObj, get(bill, 'invoiceState', 'NONE'))}</div>
                </Col>
              </Row>
              <Row style={{ marginTop: 8 }}>
                <Col span={8} className="label">
                  结算状态：
                </Col>
                <Col span={16}>
                  <div className="content">{get(this.SettlementStateObj, get(bill, 'settlementState', 'NONE'))}</div>
                </Col>
              </Row>
              <Row style={{ marginTop: 8 }}>
                <Col span={8} className="label">
                  支付金额：
                </Col>
                <Col span={16}>
                  <div className="content">
                    <div className="d_f a_center">
                      <Money showSymbol={false} value={get(bill, 'paymentAmount', 0) || 0} />元
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
            <div className="table">
              <div className="row d_f all_center heade b_b" style={{ height: 38 }}>
                <div className="d_f all_center col1"></div>
                <div className="d_f all_center col b_l ">企业支付金额</div>
                <div className="d_f all_center col b_l">企业支付条数</div>
              </div>
              <div className="list_view">
                {categoryIds.map((it, i) => {
                  return (
                    <div
                      key={it?.id}
                      className="row d_f all_center column item"
                      style={categoryIds?.length === 2 ? { height: 78 } : {}}
                    >
                      <div className="d_f all_center col1">{it?.name}</div>
                      <div className="d_f all_center col b_l number">
                        <Money showSymbol={false} value={get(statisticsInfo, `${it?.id}.totalAmount`, 0) || 0} />
                      </div>
                      <div className="d_f all_center col b_l number">
                        {get(statisticsInfo, `${it?.id}.totalNum`, 0) || 0}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          <SupplierBill 
            bus={this?.bus} 
            id={this.props?.data?.id} 
            categoryIds={categoryIds} 
            supplierArchiveType={bill.supplierArchiveType}
            importMethod={bill.importMethod}  
        />
        </div>
      </div>
    );
  }
}
