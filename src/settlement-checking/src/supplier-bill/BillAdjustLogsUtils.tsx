import moment from 'moment'
import { Tooltip } from 'antd'
import { app } from '@ekuaibao/whispered'


const operateModule = () => {
  return [
    { label: i18n.get('对账单调整'), value: 'CHECKING_DETAIL_ADJUST' },
  ]
}

const operateSource = () => {
  return [
    { label: i18n.get('OpenAPI'), value: 'OPENAPI' },
    { label: i18n.get('手动'), value: 'MANUAL' },
    { label: i18n.get('自动'), value: 'AUTO' },
  ]
}

const operateStatus = () => {
  return [
    { label: i18n.get('成功'), value: 'SUCCESS' },
    { label: i18n.get('失败'), value: 'FAIL' },
  ]
}

export enum OperateType {
  LAUNCH_ADJUST = 'LAUNCH_ADJUST',
  CONFIRM_ADJUST = 'CONFIRM_ADJUST',
}

const operateType = () => {
  return [
    { label: i18n.get('发起调整'), value: OperateType.LAUNCH_ADJUST },
    { label: i18n.get(' 确认调整'), value: OperateType.CONFIRM_ADJUST },
  ]
}

export const getLogsColumns = () => {
  return [
    {
      title: i18n.get('操作模块'),
      label: i18n.get('操作模块'),
      dataIndex: 'operateModule',
      dataType: 'text',
      width: 150,
      key: 'operateModule',
      filterType: 'list',
      filter: operateModule(),
      lookup: {
        dataSource: operateModule(),
        displayExpr: 'label',
        valueExpr: 'value',
      },
      // eslint-disable-next-line react/display-name
      render: (value) => operateModule().find((el) => el.value === value)?.label || '-',
    },
    {
      title: i18n.get('操作人'),
      label: i18n.get('操作人'),
      dataIndex: 'staffName',
      dataType: 'text',
      width: 200,
      filterType: 'text',
      key: 'staffName',
      // eslint-disable-next-line react/display-name
      render: (value, row) => {
        const getStaffShowByConfig = app.require('@elements/staffs/staffShowFn')
        const name = getStaffShowByConfig(row.staffId) || value
        return name ? (
          <Tooltip title={name}>
            <div className="text-nowrap-ellipsis">{name}</div>
          </Tooltip>
        ) : (
          '-'
        )
      },
    },
    {
      title: i18n.get('操作类型'),
      label: i18n.get('操作类型'),
      dataIndex: 'operateType',
      dataType: 'text',
      width: 150,
      key: 'operateType',
      filterType: 'list',
      filter: operateType(),
      lookup: {
        dataSource: operateType(),
        displayExpr: 'label',
        valueExpr: 'value',
      },
      // eslint-disable-next-line react/display-name
      render: (value) => operateType().find((el) => el.value === value)?.label || '-',
    },
    {
      title: i18n.get('操作对象'),
      label: i18n.get('操作对象'),
      dataIndex: 'operateTarget',
      dataType: 'text',
      width: 200,
      key: 'operateTarget',
      filterType: 'text',
      // eslint-disable-next-line react/display-name
      render: (value) => {
        return value ? (
          <Tooltip title={<div className='ta-l'>{value}</div>}>
            <div className="text-nowrap-ellipsis">{value}</div>
          </Tooltip>
        ) : (
          '-'
        )
      },
    },
    {
      title: i18n.get('操作IP'),
      label: i18n.get('操作IP'),
      dataIndex: 'operateIp',
      dataType: 'text',
      width: 150,
      filterType: 'text',
      key: 'operateIp',
    },
    {
      title: i18n.get('操作内容'),
      label: i18n.get('操作内容'),
      dataIndex: 'operateContent',
      dataType: 'text',
      width: 200,
      key: 'operateContent',
      filterType: 'text',
      // eslint-disable-next-line react/display-name
      render: (value) => {
        const infosLeft = value ? value?.replace?.(/\n|\r\n/g, "<br />") : ''
        return value ? (
          <Tooltip title={<div className='ta-l' dangerouslySetInnerHTML={{ __html: infosLeft }}></div>}>
            <div className="text-nowrap-ellipsis">{value}</div>
          </Tooltip>
        ) : (
          '-'
        )
      },
    },
    {
      title: i18n.get('操作结果'),
      label: i18n.get('操作结果'),
      dataIndex: 'operateStatus',
      dataType: 'text',
      width: 120,
      key: 'operateStatus',
      filterType: 'list',
      filter: operateStatus(),
      lookup: {
        dataSource: operateStatus(),
        displayExpr: 'label',
        valueExpr: 'value',
      },
      // eslint-disable-next-line react/display-name
      render: (value) => operateStatus().find((el) => el.value === value)?.label || '-',
    },

    {
      title: i18n.get('操作日期'),
      label: i18n.get('操作日期'),
      dataIndex: 'createTime',
      key: 'createTime',
      dataType: 'date',
      filterType: 'date',
      width: 200,
      render: (value) => moment(value).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: i18n.get('操作来源'),
      label: i18n.get('操作来源'),
      dataIndex: 'operateSource',
      dataType: 'text',
      width: 120,
      key: 'operateSource',
      filterType: 'list',
      filter: operateSource(),
      lookup: {
        dataSource: operateSource(),
        displayExpr: 'label',
        valueExpr: 'value',
      },
      // eslint-disable-next-line react/display-name
      render: (value) => operateSource().find((el) => el.value === value)?.label || '-',
    },
  ]
}
