.__import-error-content {
  .import-error-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    .header-right {
      font-weight: 400;
      color: rgba(39, 46, 59, 0.48);
      cursor: pointer;
      display: flex;
      .away {
        display: flex;
      }
      .rotate-arrows {
        transform: rotate(90deg);
      }
    }
  }
  .import-error-body {
    margin-top: 20px;
    font-weight: 400;
    font-size: 14px;
    color: rgba(39, 46, 59, 0.72);
    .import-name {
      margin-bottom: 6px;
    }
    .import-tr {
      background: #F7F8FA;
      display: flex;
      height: 40px;
      border-top: 1px solid rgba(39, 46, 59, 0.06);
      .tr-c {
        border-right: 1px solid rgba(39, 46, 59, 0.06);
        width: 168px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .tr-c:last-child {
        border-right: none;
        width: 80px;
      }
    }
    .import-body {
      height: 180px;
      overflow-y: auto;
      color: rgba(39, 46, 59, 0.88);
      .import-td {
        height: 40px;
        display: flex;
        background: #fff;
        border-bottom: 1px solid rgba(39, 46, 59, 0.06);
        .td-c {
          border-right: 1px solid rgba(39, 46, 59, 0.06);
          width: 168px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .td-color {
          color: #FAAD14;
        }
        .td-c:last-child {
          border-right: none;
          width: 80px;
        }
      }
    }
  }
}

.__import-footer {
  display: flex;
  justify-content: space-between;
}

.__import-ident-content {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  margin-top: 40px;
  .import-success {
    font-weight: 500;
    font-size: 16px;
    color: rgba(39, 46, 59, 0.96);
    margin: 18px 0 40px;
  }
}
