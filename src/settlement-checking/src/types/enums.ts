export enum ECategory {
  ALL = 'all',
  ENABLE = 'enable',
  DISABLE = 'disable',
}

export enum CheckingDetailTab {
  FEETDETAIL = 'FEETDETAIL',
  NO_CHECKING_DETAIL = 'NO_CHECKING_DETAIL',
  DEP_CHECKING_DETAIL = 'DEP_CHECKING_DETAIL',
}

export enum FeeDetailFilterType {
  ALL = 'ALL', // 全部费用
  DisabledFee = 'DisabledFee', // 引用了停用数据的费用
  IncompleteFee = 'IncompleteFee', // 填写不完整的费用
}

export enum SettlementGuideStep {
  FIRST = '0',   //引导第一步
  SECOND = '1',  //引导第一步
  THIRD = '2',   //引导第一步
  FOURTH = '3',  //引导第一步
  CLOSE = '4'    //最后一步，点击关闭
}
