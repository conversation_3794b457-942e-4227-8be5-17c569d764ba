import { ECategory } from './enums';
import { MoneyIF } from '@ekuaibao/ekuaibao_types';

export interface ICategory {
  value: ECategory;
  label: string;
}

export type StatisticsKey = 'all' | 'doing' | 'done' | 'todo';

export type ICheckingBillStatistics = {
  [key in StatisticsKey]: { total: number | MoneyIF; amount: MoneyIF; };
};

export interface IStatisticsValue {
  name: string;
  value?: MoneyIF | number;
  type?: string;
  state: string
  num: number
  id?: string
}
