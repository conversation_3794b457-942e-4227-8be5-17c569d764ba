/*
 * @Author: <PERSON>
 * @Date: 2022-01-05 17:45:18
 * @LastEditTime: 2022-11-16 15:43:39
 * @LastEditors: Hunter
 * @Description:
 * @FilePath: \plugin-web-settlement-checking\src\settlement-department\SettlementContent.tsx
 * 可以输入预定的版权声明、个性签名、空行等
 */

import React, { Component } from 'react';
import styles from '../settlement-checking/SettlementCheckingDetail.module.less';
import CheckingBillOverview from '../settlement-checking/components/CheckingBillOverview';
import { inject, provider } from '@ekuaibao/react-ioc';
import FeeTypeStatistics from '../settlement-checking/components/FeeTypeStatistics';
import { EnhanceConnect } from '@ekuaibao/store'
import FeeDetailWrapper from '../settlement-checking/components/FeeDetailWrapper';
import { FeeDetailVm } from '../settlement-checking/vms/FeeDetail.vm';
import SettlementSearch from './SettlementSearch';
import { getDepartementCheckConfig } from "../setttlement-checkin-action"
interface State {
  [key: string]: any;
}
interface Props {
  [key: string]: any;
}
const hasCheckConfig = (config: any) => config?.type === 'BILL_FIELD'
@provider([FeeDetailVm.NAME, FeeDetailVm])
@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].userinfo,
}))
export default class SettlementContent extends Component<State, Props> {
  @inject(FeeDetailVm.NAME) vm: FeeDetailVm;
  constructor(props: Props) {
    super(props);
    this.state = {
      departementCheckConfig: {
        type: 'ORGANIZATION'
      }
    }
  }
  componentDidMount() {
    this.vm.isDep = true
    this.getDepartementCheckConfig()
    this.props.bus.on('@refresh-data', this.refreshData);
  }
  getDepartementCheckConfig = async () => {
    const { corporation = {} } = this.props.userInfo
    const { value } = await getDepartementCheckConfig(corporation.id)
    this.setState({ departementCheckConfig: value })
  }
  componentWillUnmount() {
    this.props.bus.un('@refresh-data', this.refreshData);
  }
  refreshData = () => {
    const { value, settlementVm } = this.props;
    const { departementCheckConfig = {} } = this.state
    this.vm.clean();
    this.vm.init(value, true);
    if (hasCheckConfig(departementCheckConfig)) {
      settlementVm.clean();
      settlementVm.init(value);
      settlementVm.getFeeTypeStatistics('', '', { departCheck: 'depart', expenseDepartment: 'expenseDepartment' });
    }

  };
  render() {
    const { value } = this.props;
    const { departementCheckConfig = {} } = this.state
    return (
      <div className={styles['settlement-checking-detail-wrapper']}>
        <SettlementSearch {...this.props} />
        <div className="content-wrapper">
          {hasCheckConfig(departementCheckConfig) ? <FeeTypeStatistics searchApportions={false} value={value} /> : <CheckingBillOverview value={value} />}
          <FeeDetailWrapper isApportions={false} value={value} />
        </div>
      </div>
    );
  }
}
