/*
 * @Author: <PERSON>
 * @Date: 2022-01-06 10:06:46
 * @LastEditTime: 2023-02-22 17:41:56
 * @LastEditors: Hunter
 * @Description:
 * @FilePath: \plugin-web-settlement-checking\src\settlement-department\SettlementTitle.tsx
 * 可以输入预定的版权声明、个性签名、空行等
 */
import React, { Component } from 'react';
import { Button } from 'antd';
interface Props {
  isCheck?: boolean;
  [key: string]: any;
}
interface State {
  [key: string]: any;
}
export default class SettlementTitle extends Component<State, Props> {
  render() {
    const { bus, isCheck } = this.props;
    return (
      <div className="settlement-header">
        <div className="title">{this.props?.title}</div>
        <div className="right_view">
          <Button className='mr-10' disabled={isCheck} type="primary" onClick={() => bus.emit('@checking-sync')}>
            {i18n.get('重新同步账单')}
          </Button>
          <Button type="primary" disabled={isCheck} onClick={() => bus.emit('@checking-success')}>
            {i18n.get('本期对账完成')}
          </Button>
          <Button style={{ marginLeft: '10px' }} disabled={!isCheck} onClick={() => bus.emit('@checking-cancel')}>
            {i18n.get('撤回')}
          </Button>
        </div>
      </div>
    );
  }
}
