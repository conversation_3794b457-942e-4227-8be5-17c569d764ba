/*
 * @Author: <PERSON>
 * @Date: 2022-01-05 10:57:49
 * @LastEditTime: 2022-11-15 17:54:00
 * @LastEditors: Hunter
 * @Description:
 * @FilePath: \plugin-web-settlement-checking\src\settlement-department\index.tsx
 * 可以输入预定的版权声明、个性签名、空行等
 */
import React, { Component } from 'react';
import SettlementTitle from './SettlementTitle';
import SettlementContent from './SettlementContent';
import MessageCenter from '@ekuaibao/messagecenter';
import { showMessage } from '@ekuaibao/show-util';
import { app } from '@ekuaibao/whispered';
import {
  getCheckingBillList,
  settlementSuccess,
  getFeeDetailAmount,
  getCheckingBill,
  settlementCancel,
  depSettlement,
  depSyncSettlement,
} from '../setttlement-checkin-action';
import { QuerySelect } from 'ekbc-query-builder';
import './index.module.less';
import { EnhanceConnect } from '@ekuaibao/store';
import { cantEnter } from '../settlement-checking/utils/checking-columns';
import { inject, provider } from '@ekuaibao/react-ioc';
import { SettlementCheckingDetailVm } from '../settlement-checking/vms/settlement-checking-detail.vm';
interface State {
  [key: string]: any;
}
interface Props {
  [key: string]: any;
}
@provider([SettlementCheckingDetailVm.NAME, SettlementCheckingDetailVm])
@EnhanceConnect((state: any) => ({
  userInfo: state['@common'].userinfo,
}))
export default class SettlementDepartment extends Component<State, Props> {
  bus = new MessageCenter();
  @inject(SettlementCheckingDetailVm.NAME) settlementVm: SettlementCheckingDetailVm;
  settlementSearchForm: any;
  constructor(props: Props) {
    super(props);
    this.state = {
      value: {},
      isCheck: false,
    };
  }
  componentDidMount() {
    this.bus.on('@checking-success', this.settlementSuccess);
    this.bus.on('@checking-cancel', this.settlementCancel);
    this.bus.on('@checking-sync', this.settlementSync);
    this.bus.on('@search-settlement', this.searchSettlement);
    this.bus.on('@reset-search-settlement', this.resetSearchSettlement);
    this.bus.on('@sync-history-settlement', this.syncHistorySettlement);
  }
  componentWillUnmount() {
    this.bus.un('@checking-success', this.settlementSuccess);
    this.bus.un('@checking-cancel', this.settlementCancel);
    this.bus.un('@checking-sync', this.settlementSync);
    this.bus.un('@search-settlement', this.searchSettlement);
    this.bus.un('@reset-search-settlement', this.resetSearchSettlement);
    this.bus.un('@sync-history-settlement', this.syncHistorySettlement);
  }
  syncHistorySettlement = async () => {
    const checkingBillId = await this.checkFields();
    app.open('@settlement-checkin:SyncSettlementHistoryDrawer', {
      checkingBillId,
    });
  };
  settlementSync = async () => {
    const { value = {} } = this.state;
    const { settlementVm } = this;
    const checkingBillId = await this.checkFields();
    app
      .open('@settlement-checkin:ReSyncSettlement', {
        footer: null,
        value,
        settlementVm,
      })
      .then((res: any) => {
        depSyncSettlement({ ...res, checkingBillId });
      });
  };
  checkFields = () => {
    const { value } = this.state;
    const vmForm = this.settlementSearchForm.vmForm();
    return new Promise<void>((resolve, reject) => {
      vmForm.validateFields(async (err: any, val: any) => {
        if (!err) {
          if (!value?.id) {
            return showMessage.error('对账详情为空');
          }
          return resolve(value.id);
        }
      });
    });
  };
  settlementSuccess = async () => {
    try {
      const id = await this.checkFields();
      await settlementSuccess({ checkingBillId: id });
      this.getDepartmentCheck({ id });
    } catch (error) {
      showMessage.error(error?.errorMessage ?? error?.message);
    }
  };
  settlementCancel = async () => {
    try {
      const id = await this.checkFields();
      await settlementCancel({ checkingBillId: id });
      this.getDepartmentCheck({ id });
    } catch (error) {
      showMessage.error(error?.errorMessage ?? error?.message);
    }
  };
  searchSettlement = async (params: any) => {
    const q = new QuerySelect().select(
      'supplierAccountId(id,name,settlementType,balanceAmount,categoryIds(id,name)),supplierArchiveId(id,name),billPeriod(`...`),`...`',
    );
    q.desc('createTime');
    if (params?.supplierAccountId) {
      q.filterBy(`supplierAccountId=="${params?.supplierAccountId}"`);
    }
    if (params?.supplierArchiveId) {
      q.filterBy(`supplierArchiveId=="${params?.supplierArchiveId}"`);
    }
    if (params?.billPeriod) {
      q.filterBy(`billPeriod.id=="${params?.billPeriod}"`);
    }
    try {
      const res = await getCheckingBillList({ ...q.value() });
      if (res?.errorCode) {
        showMessage.error(res?.message);
      } else {
        const val = res.items?.[0] ?? {};
        if (await cantEnter(val, true)) return;
        if (res?.items?.length === 0) {
          this.resetSearchSettlement();
          return;
        }
        this.getFeeDetailAmount(val);
        this.getDepartmentCheck(val);
      }
    } catch (err) {
      showMessage.error(err?.errorMessage ?? err?.message);
    }
  };
  getDepartmentCheck = async (params: any) => {
    try {
      let isFinishAll: boolean = true;
      const userId = this.props.userInfo?.staff?.id ?? '';
      const { value } = await getCheckingBill(params?.id ?? '');
      const { items } = await depSettlement(params?.id ?? '');
      const depList = items.filter((item: any) => item.checkerId?.includes(userId));
      depList.forEach((item: any) => item.state === 'INCOMPLETE' && (isFinishAll = false));
      this.setState({
        isCheck: value?.checkingState === 'CHECKING_TODO' && depList.length > 0 && isFinishAll,
      });
    } catch (err) {
      showMessage.error(err?.errorMessage ?? err?.message);
    }
  };
  getFeeDetailAmount = async (value: any) => {
    try {
      const { feeDetailAmount } = await getFeeDetailAmount(value?.id);
      this.setState(
        { value: { ...value, feeTypeAmount: { ...value.feeTypeAmount, standard: feeDetailAmount } } },
        () => {
          this.bus.emit('@refresh-data');
        },
      );
    } catch (err) {
      showMessage.error(err?.errorMessage ?? err?.message);
    }
  };
  resetSearchSettlement = () => {
    this.setState({ value: {} }, () => {
      this.bus.emit('@refresh-data');
    });
  };
  render() {
    const { value, isCheck } = this.state;
    const { bus, settlementVm } = this;
    return (
      <div className="settlement-department">
        <SettlementTitle bus={bus} isCheck={isCheck} />
        <SettlementContent
          bus={bus}
          settlementVm={settlementVm}
          wrappedComponentRef={(e: any) => {
            this.settlementSearchForm = e;
          }}
          value={value}
        />
      </div>
    );
  }
}
