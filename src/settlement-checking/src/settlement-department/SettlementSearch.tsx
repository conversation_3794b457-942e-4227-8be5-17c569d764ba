/*
 * @Author: <PERSON>
 * @Date: 2022-01-06 10:06:46
 * @LastEditTime: 2022-11-17 10:53:35
 * @LastEditors: Hunter
 * @Description:
 * @FilePath: \plugin-web-settlement-checking\src\settlement-department\SettlementSearch.tsx
 * 可以输入预定的版权声明、个性签名、空行等
 */
import { app } from '@ekuaibao/whispered';
import React, { Component } from 'react';
import { Form, Select, Button, Tooltip } from 'antd';
import { get } from 'lodash';
import { getSupplierArchiveList, getBillingPeriodList, getSupplierAccountList } from '../setttlement-checkin-action';
import { QuerySelect } from 'ekbc-query-builder';
import { showMessage } from '@ekuaibao/show-util';
import './index.module.less';
const EKBIcon = app.require<any>('@elements/ekbIcon');
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');

const FormItem = Form.Item;
const { Option } = Select;
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
interface Props {
  [key: string]: any;
}
interface State {
  [key: string]: any;
}
@EnhanceFormCreate()
export default class SettlementSearch extends Component<State, Props> {
  constructor(props: Props) {
    super(props);
    this.state = {
      billPeriod: [],
      archiveList: [],
      accountList: [],
    };
    this.getSupplierArchiveList();
    this.getSupplierAccountList();
    this.getBillingPeriod();
  }
  handleSearch() {
    const {
      form: { validateFieldsAndScroll },
      bus,
    } = this.props;
    validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }
      bus.emit('@search-settlement', values);
    });
  }
  handleReset() {
    const {
      form: { resetFields },
      bus,
    } = this.props;
    resetFields();
    bus.emit('@reset-search-settlement');
  }
  handleHistory() {
    const { bus } = this.props;
    bus.emit('@sync-history-settlement');
  }

  getSupplierArchiveList = async () => {
    try {
      const res = await getSupplierArchiveList({
        select: 'id,name,supplierArchiveType,active',
        orderBy: [{ value: 'name', order: 'DESC' }],
        filterBy: '(active==true)',
      });
      this.setState({ archiveList: get(res, 'items', []) });
    } catch (error) {
      showMessage.error(error?.message ?? error?.errMessage);
    }
  };
  getSupplierAccountList = async () => {
    try {
      const query = new QuerySelect();
      query.select('id,name,active');
      query.filterBy('active==true');
      const res = await getSupplierAccountList(query.value());
      this.setState({ accountList: get(res, 'items', []) });
    } catch (error) {
      showMessage.error(error?.message ?? error?.errMessage);
    }
  };
  async getBillingPeriod() {
    try {
      const res = await getBillingPeriodList({});
      this.setState({ billPeriod: get(res, 'items', []) });
    } catch (error) {
      showMessage.error(error?.errorMessage ?? error?.message);
    }
  }
  vmForm() {
    return this.props.form;
  }
  render() {
    const { billPeriod, accountList, archiveList } = this.state;
    const { getFieldDecorator } = this.props.form;
    return (
      <div className="settlement-search">
        <Form layout="inline" className={'row'}>
          <FormItem {...formItemLayout} className={'form_item'} label="供应商">
            {getFieldDecorator('supplierArchiveId', {
              rules: [{ required: true, message: i18n.get('请选择供应商') }],
            })(
              <Select allowClear placeholder={i18n.get('请选择供应商')}>
                {archiveList?.map((it) => {
                  return (
                    <Option key={it.id} value={it.id}>
                      <Tooltip placement="bottom" title={it.name}>
                        {it.name}
                      </Tooltip>
                    </Option>
                  );
                })}
              </Select>,
            )}
          </FormItem>
          <FormItem {...formItemLayout} className={'form_item'} label="账户">
            {getFieldDecorator('supplierAccountId', {
              rules: [{ required: true, message: i18n.get('请选择供应商账户') }],
            })(
              <Select allowClear placeholder={i18n.get('请选择供应商账户')}>
                {accountList?.map((it) => {
                  return (
                    <Option key={it.id} value={it.id}>
                      {it.name}
                    </Option>
                  );
                })}
              </Select>,
            )}
          </FormItem>
          <FormItem {...formItemLayout} className={'form_item'} label="账期">
            {getFieldDecorator('billPeriod', {
              rules: [{ required: true, message: i18n.get('请选择账期') }],
            })(
              <Select allowClear placeholder={i18n.get('请选择账期')}>
                {billPeriod?.map((it) => {
                  return (
                    <Option key={it?.id} value={it?.id}>
                      {it?.name}
                    </Option>
                  );
                })}
              </Select>,
            )}
          </FormItem>
          <FormItem className={'form_item'}>
            <Button type="primary" style={{ marginLeft: 32 }} onClick={this.handleSearch.bind(this)}>
              {i18n.get('查询')}
            </Button>
            <Button className='ml-8' onClick={this.handleReset.bind(this)}>
              {i18n.get('重置')}
            </Button>

            <Button className='ml-8' onClick={this.handleHistory.bind(this)}>
              <EKBIcon name="#EDico-record1" />
            </Button>
          </FormItem>
        </Form>
      </div>
    );
  }
}
