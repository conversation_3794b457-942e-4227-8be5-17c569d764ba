@import '~@ekuaibao/eui-styles/less/token';

.supplier-manager-wrapper {
  background-color: #f7f7f7;
  flex: 1;
  display: flex;
  flex-direction: column;
  :global {
    .content-header {
      background: white;
      height: 52px;
      width: 100%;
      display: flex;
      padding: 0 24px;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 20px;
        font-weight: 500;
        max-width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .search {
      flex: 1;
    }

    .header-search {
      .font-size-2;
      color: @color-black-1;
      padding: @space-5;
      background-color: @color-white-1;

      label {
        margin-right: @space-4;
        margin-left: @space-4;
      }
    }

    .error-content {
      padding: 0 24px;
      .supplier-error {
        margin-bottom: 16px;
        .download-error-excel {
          color: var(--brand-base);
          cursor: pointer;
          margin-left: 8px;
        }
      }
    }

    .header-operation {
      padding: @space-7;
      .ant-radio-group {
        .font-size-2;
        .font-weight-3;

        .ant-radio-button-wrapper {
          color: @color-black-1;
          border-color: rgba(29, 43, 61, 0.15);
        }

        .ant-radio-button-wrapper-checked {
          box-shadow: none;
        }

        .ant-radio-button-wrapper-disabled {
          color: rgba(29, 43, 61, 0.15);
          background: @color-white-1;
        }
      }
    }
    .supplier-list {
      display: flex;
      flex: 1;
      flex-direction: column;
      padding: 0 @space-7 @space-7 @space-7;
    }
  }
}
