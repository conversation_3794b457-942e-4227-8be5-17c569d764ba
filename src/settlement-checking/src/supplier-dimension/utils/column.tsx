import React from 'react';
import Action from '../elements/Action';
import { taxpayerTypeLabel } from './form';
import { ISupplier } from '@ekuaibao/ekuaibao_types';
import { SupplierManagerVm } from '../vms';
import { Tooltip } from '@hose/eui';
export const columns = (vm: SupplierManagerVm) => {
  return [
    {
      title: i18n.get('供应商名称'),
      dataIndex: 'name',
      width: 200,
      render: (value: string) => (
        <Tooltip placement="bottom" title={value}>
          {value.length >= 30 ? value.slice(0, 20) + '...' : value}
        </Tooltip>
      ),
    },
    {
      title: i18n.get('供应商编码'),
      dataIndex: 'code',
      width: 200,
    },
    {
      title: i18n.get('纳税人类型'),
      dataIndex: 'taxpayerType',
      width: 180,
      render: (value: 'GENERAL' | 'SMALL') => {
        if (!value) return '-';
        return taxpayerTypeLabel()[value];
      },
    },
    {
      title: i18n.get('纳税人识别号'),
      dataIndex: 'taxpayerIdNum',
      width: 200,
    },
    {
      title: i18n.get('状态'),
      dataIndex: 'active',
      width: 100,
      render: (value: boolean) => {
        return (
          <span style={value ? { color: 'var(--brand-base)' } : { color: '#ff4d4f' }}>
            {value ? i18n.get('已启用') : i18n.get('已停用')}
          </span>
        );
      },
    },
    {
      title: i18n.get('电话'),
      dataIndex: 'telephone',
      width: 170,
    },
    {
      title: i18n.get('创建人'),
      dataIndex: 'creator',
      width: 170,
      render: (value: any) => {
        return value?.name;
      },
    },
    {
      title: i18n.get('地址'),
      dataIndex: 'address',
      width: 220,
    },
    {
      title: i18n.get('操作'),
      dataIndex: 'operation',
      fixed: 'right',
      width: 180,
      render: (value: any, record: ISupplier) => {
        return <Action vm={vm} record={record} />;
      },
    },
  ];
};
