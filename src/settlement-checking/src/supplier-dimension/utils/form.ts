import { ETaxpayerType } from '@ekuaibao/ekuaibao_types';

enum ESupplierArchiveType {
  'HOSE_TRIP' = 'HOSE_TRIP',
  'DIDI_NEW' = 'DIDI_NEW',
  'DIDI' = 'DIDI',
  'TRAVEL_ONE' = 'TRAVEL_ONE',
  'ALI_TRIP' = 'ALI_TRIP',
  'DT_TRIP' = 'DT_TRIP',
  'XC_TRIP' = 'XC_TRIP',
  'GEELY_TRIP' = 'GEELY_TRIP',
  'TC_TRIP' = 'TC_TRIP',
  'OTHER' = 'OTHER',
}
enum ESettlementType {
  'CREDIT' = 'CREDIT',
  'PRIECHARGE' = 'PRIECHARGE',
}

export const supplierCode = (editable = true) => ({
  name: 'code',
  type: 'text',
  optional: false,
  maxLength: 20,
  editable,
  validator: (value: string) => {
    const reg = /^[0-9a-zA-Z_-]+$/;
    if (!reg.test(value)) {
      return i18n.get('供应商编码只支持数字、字母、下划线、中划线');
    }
    return '';
  },
  placeholder: i18n.get('请输入供应商编码'),
  label: i18n.get('供应商编码'),
});
export const supplierArchiveType = (editable = true) => ({
  name: 'supplierArchiveType',
  type: 'select',
  editable,
  optional: false,
  placeholder: i18n.get('请选择供应商类型'),
  label: i18n.get('供应商类型'),
  tags: [
    {
      value: ESupplierArchiveType.HOSE_TRIP,
      label: supplierArchiveTypeLabel()[ESupplierArchiveType.HOSE_TRIP],
      disabled: true,
    },
    { value: ESupplierArchiveType.DIDI, label: supplierArchiveTypeLabel()[ESupplierArchiveType.DIDI] },
    { value: ESupplierArchiveType.DIDI_NEW, label: supplierArchiveTypeLabel()[ESupplierArchiveType.DIDI_NEW] },
    { value: ESupplierArchiveType.TRAVEL_ONE, label: supplierArchiveTypeLabel()[ESupplierArchiveType.TRAVEL_ONE] },
    { value: ESupplierArchiveType.ALI_TRIP, label: supplierArchiveTypeLabel()[ESupplierArchiveType.ALI_TRIP] },
    { value: ESupplierArchiveType.DT_TRIP, label: supplierArchiveTypeLabel()[ESupplierArchiveType.DT_TRIP] },
    { value: ESupplierArchiveType.XC_TRIP, label: supplierArchiveTypeLabel()[ESupplierArchiveType.XC_TRIP] },
    { value: ESupplierArchiveType.GEELY_TRIP, label: supplierArchiveTypeLabel()[ESupplierArchiveType.GEELY_TRIP] },
    { value: ESupplierArchiveType.TC_TRIP, label: supplierArchiveTypeLabel()[ESupplierArchiveType.TC_TRIP] },
    { value: ESupplierArchiveType.OTHER, label: supplierArchiveTypeLabel()[ESupplierArchiveType.OTHER] },
  ],
});
export const settlementTypes = (editable = true) => ({
  name: 'settlementTypes',
  type: 'select',
  editable,
  mode: 'multiple',
  optional: true,
  placeholder: i18n.get('请选择账户类型'),
  label: i18n.get('账户类型'),
  tags: [
    { value: ESettlementType.CREDIT, label: settlementTypesLabel()[ESettlementType.CREDIT] },
    { value: ESettlementType.PRIECHARGE, label: settlementTypesLabel()[ESettlementType.PRIECHARGE] },
  ],
});
export const supplierName = (editable = true) => ({
  name: 'name',
  type: 'text',
  maxLength: 100,
  editable,
  optional: false,
  placeholder: i18n.get('请输入供应商名称'),
  label: i18n.get('供应商名称'),
});
export const taxpayerType = () => ({
  name: 'taxpayerType',
  type: 'select',
  label: i18n.get('纳税人类型'),
  optional: true,
  placeholder: i18n.get('请选择纳税人类型'),
  tags: [
    { value: ETaxpayerType.GENERAL, label: taxpayerTypeLabel()[ETaxpayerType.GENERAL] },
    { value: ETaxpayerType.SMALL, label: taxpayerTypeLabel()[ETaxpayerType.SMALL] },
  ],
});
export const taxpayerNumber = () => ({
  name: 'taxpayerIdNum',
  type: 'text',
  maxLength: 200,
  label: i18n.get('纳税人识别号'),
  placeholder: i18n.get('请输入纳税人识别号'),
  optional: true,
  validator: (value: string) => {
    const reg = /^[0-9a-zA-Z]+$/;
    if (!reg.test(value)) {
      return i18n.get('纳税人识别号只支持数字、字母');
    }
    return '';
  },
});
export const address = () => ({
  name: 'address',
  type: 'text',
  maxLength: 200,
  label: i18n.get('地址'),
  placeholder: i18n.get('请输入地址'),
  optional: true,
});
export const phone = () => ({
  name: 'telephone',
  type: 'text',
  maxLength: 200,
  placeholder: i18n.get('请输入电话'),
  label: i18n.get('电话'),
  validator: (value: string) => {
    const reg = /^[0-9]+$/;
    if (!reg.test(value)) {
      return i18n.get('电话只支持数字');
    }
    return '';
  },
  optional: true,
});

export const noteValidator = (value: string = '') => {
  if (value?.length > 200) {
    return i18n.get('备注不能超过200个字');
  }
  return '';
};

export const taxpayerTypeLabel = () => ({
  GENERAL: i18n.get('一般纳税人'),
  SMALL: i18n.get('小规模纳税人'),
});

export const supplierArchiveTypeLabel = () => ({
  HOSE_TRIP: i18n.get('合思商城'),
  DIDI: i18n.get('滴滴'),
  DIDI_NEW: i18n.get('新滴滴'),
  TRAVEL_ONE: i18n.get('差旅壹号'),
  ALI_TRIP: i18n.get('阿里商旅'),
  DT_TRIP: i18n.get('大唐商旅'),
  XC_TRIP: i18n.get('携程商旅'),
  GEELY_TRIP: i18n.get('吉利商旅'),
  TC_TRIP: i18n.get('同程商旅'),
  OTHER: i18n.get('其他'),
});
export const settlementTypesLabel = () => ({
  CREDIT: i18n.get('授信'),
  PRIECHARGE: i18n.get('预存'),
});

export const orderConfigName = (editable: boolean = true) => ({
  name: 'name',
  type: 'text',
  maxLength: 10,
  editable,
  optional: false,
  placeholder: i18n.get('请输入配置名称'),
  label: i18n.get('配置名称'),
});
export const orderConfigDescription = (editable: boolean = true) => ({
  name: 'description',
  type: 'text',
  maxLength: 50,
  editable,
  optional: true,
  placeholder: i18n.get('请输入配置描述'),
  label: i18n.get('配置描述'),
});
export const orderConfigSupplierType = (editable: boolean = true, whiteList: string[] = []) => {
  const supplierType = supplierArchiveType(editable);
  if (whiteList?.length === 0) {
    return supplierType;
  }
  supplierType.tags = supplierType?.tags?.filter((i) => whiteList.includes(i.value));
  return supplierType;
};
export const orderConfigSelect = (editable: boolean = true) => ({
  name: 'mappings',
  type: 'order-config-select',
  editable,
  optional: false,
  placeholder: i18n.get('请选择匹配字段'),
  label: i18n.get('匹配字段'),
});

export const orderConfigRules = (editable: boolean = true) => ({
  name: 'rules',
  type: 'order-config-rules',
  editable,
  optional: false,
  placeholder: i18n.get('请选择匹配规则'),
  label: i18n.get('匹配规则'),
});

export const orderSelectTime = (editable: boolean = true) => ({
  name: 'time',
  type: 'order-select-time',
  editable,
  optional: false,
  placeholder: i18n.get('请选择订单行程日期范围'),
  label: i18n.get('订单行程日期范围'),
});
