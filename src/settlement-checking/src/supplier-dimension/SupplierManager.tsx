/*
 * @Description:
 * @Creator: chencan<PERSON><PERSON>
 * @Date: 2021-06-25 10:59:21
 */
import React, { PureComponent } from 'react';
import styles from './supplier-dimension.module.less';
import { provider, inject } from '@ekuaibao/react-ioc';
import { SupplierManagerVm } from './vms';
import Top from '../components/Top';
import SupplierList from './elements/SupplierList';

@provider([SupplierManagerVm.NAME, SupplierManagerVm])
export default class Index extends PureComponent {
  @inject(SupplierManagerVm.NAME) vm: SupplierManagerVm;

  async componentDidMount() {
    this.vm.setKeel(this.props?.keel);
    await this.vm.init();
  }

  componentWillUnmount() {
    this.vm.clean();
  }

  render() {
    return (
      <div className={styles['supplier-manager-wrapper']}>
        <div className="content-header">
          <div className="title">{this.props?.title}</div>
        </div>
        <Top />
        <SupplierList keel={this.props?.keel} />
      </div>
    );
  }
}
