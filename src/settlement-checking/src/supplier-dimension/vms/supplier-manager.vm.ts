import { action, computed, IReactionPublic, observable, reaction } from 'mobx';
import { ICategory } from '../../types/interface';
import { ECategory } from '../../types/enums';
import { ISupplier } from '@ekuaibao/ekuaibao_types';
import { TableVm } from '@ekuaibao/collection-definition';
import { Column } from '@ekuaibao/datagrid/lib/types/column';
import { columns } from '../utils/column';
import {
  batchDisable,
  batchEnable,
  checkSupplierImportError,
  deleteSupplierErrorExcel,
  disable,
  enable,
  exportSupplierExcel,
  getSupplierArchiveList,
} from '../../setttlement-checkin-action';
import { QuerySelect } from 'ekbc-query-builder';

export const Category: ICategory[] = [
  {
    value: ECategory.ALL,
    label: i18n.get('全部'),
  },
  {
    value: ECategory.ENABLE,
    label: i18n.get('已启用'),
  },
  {
    value: ECategory.DISABLE,
    label: i18n.get('已停用'),
  },
];

export class SupplierManagerVm extends TableVm<ISupplier> {
  static NAME = Symbol.for('SUPPLIER_MANAGER_VM');
  @observable keel: any;
  @observable category: ECategory = ECategory.ALL;
  @observable columns: Column[] = columns(this) as unknown as Column[];
  @observable selectedRowKeys: string[] = [];
  @observable selectedRowData: { [key: string]: ISupplier } = {};
  @observable searchName = '';
  @observable searchCode = '';
  @observable errorImportExcel: string = '';

  reaction?: IReactionPublic;

  @action setSearchName = (value: string) => {
    this.searchName = value;
  };
  @action setKeel = (value: any) => {
    this.keel = value;
  };
  @action setSearchCode = (value: string) => {
    this.searchCode = value;
  };

  @action setSelectedRowKeys = (selectedRowKeys: string[]) => {
    this.selectedRowKeys = selectedRowKeys;
  };

  @action setSelectedData = (data: { [key: string]: ISupplier } = {}) => {
    this.selectedRowData = data;
  };

  @computed get batchEnableDisabled() {
    return !Object.values(this.selectedRowData).filter((item) => !item.active).length || !this.selectedRowKeys.length;
  }
  @computed get batchDisableDisabled() {
    return !Object.values(this.selectedRowData).filter((item) => item.active).length || !this.selectedRowKeys.length;
  }

  @computed get enabledExport(): boolean {
    return !this.selectedRowKeys.length;
  }

  @computed get enabledBatchExport(): boolean {
    return !this.dataSource.length;
  }

  @action setCategory = (value: ECategory) => {
    this.category = value;
    this.selectedRowKeys = [];
    this.selectedRowData = {};
  };

  getSupplier = async () => {
    const params = this.getParams();
    const result = await getSupplierArchiveList(params);
    this.dataSource = result?.items ?? [];
    this.dataTotal = result?.total ?? 0;
  };

  init = async () => {
    reaction(
      () => [this.category],
      (data, reaction) => {
        this.reaction = reaction;
        this.getSupplier();
      },
      {
        fireImmediately: true,
      },
    );
  };

  getParams = () => {
    const query = new QuerySelect();
    if (this.category !== ECategory.ALL) {
      query.filterBy(`active==${this.category === ECategory.ENABLE}`);
    }
    query.filterBy(`name.contains("${this.searchName}")`);
    if (!!this.searchCode) {
      query.filterBy(`code=="${this.searchCode}"`);
    }
    query.select('creator(id, name),`...`');
    return query.value();
  };

  batchDisable = async () => {
    return batchDisable(this.selectedRowKeys);
  };

  batchEnable = async () => {
    return batchEnable(this.selectedRowKeys);
  };

  disable = async (id: string = '') => {
    return disable({ id });
  };
  enable = async (id: string = '') => {
    return enable({ id });
  };

  @action resetData = () => {
    this.searchName = '';
    this.searchCode = '';
    this.category = ECategory.ALL;
    this.selectedRowKeys = [];
    this.selectedRowData = {};
  };

  exportExcel = async () => {
    const params = this.getParams();
    const query = new QuerySelect(params);
    query.desc('createTime');
    const queryStr = query.value();
    console.log('=====start--log=====');
    console.log(queryStr);
    console.log('=====end--log=====');
    exportSupplierExcel({ ids: this.selectedRowKeys });
    this.selectedRowKeys = [];
    this.selectedRowData = {};
  };

  exportAll = async () => {
    const params = this.getParams();
    const query = new QuerySelect(params);
    query.desc('createTime');
    const queryStr = query.value();
    console.log('=====start--log=====');
    console.log(queryStr);
    console.log('=====end--log=====');
    // const { taskName } = await app.open('@layout:AsyncExportModal');
    exportSupplierExcel({ isAll: true, ids: [] });
  };

  checkImportError = async () => {
    const result = await checkSupplierImportError();
    this.errorImportExcel = result.value;
  };

  deleteSupplierErrorExcel = () => {
    return deleteSupplierErrorExcel();
  };

  clean = () => {
    this.reaction?.dispose();
  };
}
