import { action, observable } from 'mobx';
import { ITemplate } from '@ekuaibao/template/src/Template';
import {
  address,
  noteValidator,
  phone,
  supplierCode,
  supplierName,
  supplierArchiveType,
  taxpayerNumber,
  taxpayerType,
  settlementTypes,
} from '../utils/form';
import { QuerySelect } from 'ekbc-query-builder';
import { Bus } from '@ekuaibao/template';
import { ISupplier, AttachmentIF, PayeeInfoIF } from '@ekuaibao/ekuaibao_types';
import {
  postSupplierAccount,
  putSupplierAccount,
  createSupplier,
  getSupplierInfo,
  modifySupplier,
  getSupplierAccountList,
  disableSupplierAccount,
  restoreSupplierAccount,
} from '../../setttlement-checkin-action';
import { IExtendBus } from '@ekuaibao/template/types/Bus';
import { showMessage, showModal } from '@ekuaibao/show-util';

export class SupplierEditVm {
  static NAME = Symbol('SUPPLIER_EDIT_VM');
  bus: IExtendBus = Bus();
  originValue?: ISupplier;
  @observable templateValue = {};
  @observable description?: string;
  @observable showAccount?: boolean;
  @observable attachmentObj?: AttachmentIF[];
  @observable paymentAccountObj?: PayeeInfoIF[];
  @observable accountList = [];
  @observable defaultPaymentAccountId?: string;
  @observable template: ITemplate = [];

  init = async (supplier?: ISupplier) => {
    let editable = true;
    if (!!supplier) {
      editable = false;
      this.template = [
        supplierCode(editable),
        supplierName(editable),
        supplierArchiveType(editable),
        taxpayerType(),
        taxpayerNumber(),
        address(),
        phone(),
      ];
      this.showAccount = true;
      const value = await this.getSupplierDetail(supplier?.id);
      this.originValue = value;
      this.templateValue = value ?? {};
      this.description = value?.description;
      this.paymentAccountObj = value?.paymentAccountObj;
      this.defaultPaymentAccountId = value?.defaultPaymentAccountId;
      this.attachmentObj = value?.attachmentObj?.map((item: AttachmentIF) => {
        const f = value?.attachments?.find((ele: AttachmentIF) => item.id === ele.id);
        return {
          ...f,
          ...item,
        };
      });
      const accountList = await this.getSupplierAccount(supplier?.id);
      this.accountList = accountList;
    } else {
      this.showAccount = false;
      this.template = [
        supplierCode(editable),
        supplierName(editable),
        supplierArchiveType(editable),
        settlementTypes(editable),
        taxpayerType(),
        taxpayerNumber(),
        address(),
        phone(),
      ];
    }
  };

  @action setDescription = (value?: string) => {
    this.description = value;
  };
  @action setAttachmentObj = (value?: AttachmentIF[]) => {
    this.attachmentObj = value;
  };
  @action setPaymentAccountObj = (value?: PayeeInfoIF[]) => {
    this.paymentAccountObj = value;
    if (value?.length === 1) {
      this.defaultPaymentAccountId = value[0].id;
    }
    if (!this.paymentAccountObj?.some((item) => item.id === this.defaultPaymentAccountId)) {
      this.defaultPaymentAccountId = '';
    }
  };
  @action setDefaultPaymentAccountId = (value?: string) => {
    this.defaultPaymentAccountId = value;
  };
  @action getSupplierAccountAction = async (it) => {
    const { id, active, supplierArchiveType } = it;
    if (supplierArchiveType !== 'HOSE_TRIP') {
      showModal.confirm({
        title: i18n.get('提示'),
        content: i18n.get(`确定${active ? '停用账户' : '启用账户'}？`),
        onOk: async () => {
          if (active) {
            disableSupplierAccount({ id })
              .then(async (res) => {
                showMessage.success(i18n.get('停用账户成功'));
                const accountList = await this.getSupplierAccount(it?.supplierArchiveId?.id);
                this.accountList = accountList;
              })
              .catch((err) => {
                showMessage.error(err?.message || err?.errMessage);
              });
          } else {
            restoreSupplierAccount({ id })
              .then(async (res) => {
                showMessage.success(i18n.get('启用账户成功'));
                const accountList = await this.getSupplierAccount(it?.supplierArchiveId?.id);
                this.accountList = accountList;
              })
              .catch((err) => {
                showMessage.error(err?.message || err?.errMessage);
              });
          }
        },
      });
    } else {
      showMessage.info('账户状态不可修改');
    }
  };
  @action putSupplierAccountAction = async (res, it) => {
    putSupplierAccount({ ...res, id: it?.id, supplierArchiveType: it?.supplierArchiveType })
      .then(async (re) => {
        showMessage.success('修改供应商账户成功');
        const accountList = await this.getSupplierAccount(it?.supplierArchiveId?.id);
        this.accountList = accountList;
      })
      .catch((err) => {
        showMessage.error(err?.message || err?.errMessage);
      });
  };
  @action postSupplierAccountAction = async (res) => {
    postSupplierAccount(res)
      .then(async (re) => {
        showMessage.success('编辑供应商账户成功');
        const accountList = await this.getSupplierAccount(this?.originValue?.id);
        this.accountList = accountList;
      })
      .catch((err) => {
        showMessage.error(err?.message || err?.errMessage);
      });
  };
  @action setDefaultPaymentAccountId222 = (value?: string) => {
    this.defaultPaymentAccountId = value;
  };

  getSupplierAccount = async (id: string = '') => {
    const q = new QuerySelect().select('supplierArchiveId(id,name),...');
    q.filterBy(`supplierArchiveId=="${id}"`);
    const result = await getSupplierAccountList({ ...q.value() });
    return result?.items;
  };

  getSupplierDetail = async (id: string = '') => {
    const result = await getSupplierInfo({ id });
    return result?.value;
  };

  getSupplierValue = async () => {
    const baseData = await this.bus.getValueWithValidate(0);
    return {
      ...baseData,
      paymentAccountIds: this.paymentAccountObj?.map((item) => item.id),
      description: this.description,
      attachments: this.attachmentObj?.map((item) => ({
        id: item.id,
        fileName: item.fileName,
        key: item.key,
      })),
      defaultPaymentAccountId: this.defaultPaymentAccountId,
    };
  };

  save = async () => {
    const value = await this.getSupplierValue();
    if (!!noteValidator(this.description)) {
      return Promise.reject();
    }
    if (!!this.originValue) {
      return this.modify();
    }
    return createSupplier(value);
  };

  modify = async () => {
    const value = await this.getSupplierValue();
    return modifySupplier({ ...value, id: this.originValue?.id });
  };
}
