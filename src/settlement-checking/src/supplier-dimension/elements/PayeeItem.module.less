@import "~@ekuaibao/eui-styles/less/token";
.item {
  position: relative;
  background: #f5f5f5;
  border-radius: @radius-2;
  padding: @space-6;
  height: 110px;

  :global {
    display: flex;

    .icon {
      width: 40px;
      height: 40px;
      img {
        width: 100%;
        height: 100%;
      }
    }

    .info_view {
      margin-left: 16px;

      font-size: 14px;
      color: #333333;

      .bank_number {
        margin-top: 8px;
        font-size: 16px;
        font-weight: 600;
      }
      .company {
        margin-top: 8px;
        font-size: 12px;
      }
    }
    .default-span {
      background-color: @color-white-1;
      padding: @space-1 @space-3;
      color: @brand-color;
    }
    .mark-position {
      position: absolute;
      top: 0;
      right: 0;
    }
    .default {
      .mark-position;
      .default-span;
    }
    .action {
      .mark-position;
      visibility: hidden;
      cursor: pointer;
      >:last-child {
        border-radius: 0 @space-2 0 0;
      }
      span {
        .default-span;
      }
      .delete{
        color: @color-error-1;
        background-color: @color-error-6;
      }
    }
  }
  &:hover {
    :global {
      .action {
        visibility: visible;
      }
    }
  }
}
