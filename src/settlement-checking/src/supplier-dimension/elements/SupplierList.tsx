import React from 'react'
import {useInstance} from '@ekuaibao/react-ioc'
import {SupplierManagerVm} from '../vms'
import {useObserver} from 'mobx-react-lite'
import TableWrapper from '../../components/TableWrapper'
import {
  PageMode,
  PaginationConfig
} from '@ekuaibao/datagrid/esm/types/pagination'
import {ISupplier} from '@ekuaibao/ekuaibao_types'

interface TopProps {
}

export const Top: React.FC<TopProps> = (props) => {
  const vm = useInstance<SupplierManagerVm>(SupplierManagerVm.NAME)
  vm.setKeel(props?.keel)
  const handleTableRowClick = () => {
    console.log('handleTableRowClick')
  }
  const handleSelectedChange = (selectedRowKeys: string[], data: { [key: string]: ISupplier }) => {
    vm.setSelectedRowKeys(selectedRowKeys)
    vm.setSelectedData(data)
  }
  const handlePageChange = (pagination: PaginationConfig, pageMode: PageMode) => {
    vm.currentPage = pagination.current
    vm.pageSize = pagination.size
    vm.pageMode = pageMode
  }
  return useObserver(() => {
    return (
      <div className="supplier-list">
        <TableWrapper
          tableProps={{
            dataSource: vm.dataSource,
            columns: vm.columns,
            isMultiSelect: true,
            filters: vm.filters,
            scrolling: vm.scrolling,
            pageSize: vm.pageSize,
            selectedRowKeys: vm.selectedRowKeys,
            onRowClick: handleTableRowClick,
            onSelectedChange: handleSelectedChange,
            fixedSelect: true,
          }}
          paginationProps={{
            totalLength: vm.dataTotal,
            pagination: {
              current: vm.currentPage,
              size: vm.pageSize,
            },
            onChange: handlePageChange,
            pageMode: vm.pageMode,
          }}
        />
      </div>
    )
  })
}

export default Top
