import React from 'react';
import styles from './action.module.less';
import { T } from '@ekuaibao/i18n';
import { app } from '@ekuaibao/whispered';
import { showMessage } from '@ekuaibao/show-util';
import { ISupplier } from '@ekuaibao/ekuaibao_types';
import { SupplierManagerVm } from '../vms';

interface ActionProps {
  record?: ISupplier;
  vm?: SupplierManagerVm;
}

export const Action: React.FC<ActionProps> = ({ vm, record }) => {
  const handleDisable = async () => {
    try {
      await vm?.disable(record?.id);
      await vm?.getSupplier();
    } catch (e) {
      showMessage.error(e.message);
    }
  };

  const handleAble = async () => {
    try {
      await vm?.enable(record?.id);
      await vm?.getSupplier();
    } catch (e) {
      showMessage.error(e.message);
    }
  };

  const handleEdit = async () => {
    await app.open('@settlement-checkin:SupplierEdit', {
      supplier: record,
      keel: vm?.keel,
    });
    await vm?.getSupplier();
  };

  return (
    <div className={styles['actions']}>
      {record?.active ? (
        <span className="col-btn-danger" onClick={handleDisable}>
          <T name="停用" />
        </span>
      ) : (
        <span className="col-btn" onClick={handleAble}>
          <T name="启用" />
        </span>
      )}
      <span className="col-btn" onClick={handleEdit}>
        <T name="编辑" />
      </span>
    </div>
  );
};

export default Action;
