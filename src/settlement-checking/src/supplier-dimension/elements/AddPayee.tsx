import React from 'react';
import { T } from '@ekuaibao/i18n';
import { app } from '@ekuaibao/whispered';
import styles from './AddPayee.module.less';
const EKBIcon = app.require('@elements/ekbIcon');

interface AddPayeeProps {
  title?: string;
  onClick: () => void;
}

export const AddPayee: React.FC<AddPayeeProps> = ({ title, onClick }) => {
         return (
           <div className={styles['add-wrapper']} onClick={onClick}>
             <div className="content">
               <div className="icon-wrapper">
                 <EKBIcon name="#EDico-plus-default" />
               </div>
               <T name={title ? title : '新增收款信息'} />
             </div>
           </div>
         );
       };

export default AddPayee;
