import { app } from '@ekuaibao/whispered';
import React, { Component } from 'react';
import { Keel, registerComponentsCellar } from '@ekuaibao/keel';
const KeelSingleViewHeader = app.require<any>('@elements/puppet/KeelSingleViewHeader');
const KeelViewBody = app.require<any>('@elements/puppet/KeelViewBody');

@registerComponentsCellar([
  {
    key: 'SupplierDetailView',
    getComponent: () => import('./../supplier-account/SupplierDetailView'),
  },
  {
    key: 'SupplierManagerView',
    getComponent: () => import('./SupplierManager'),
    title: i18n.get('供应商档案'),
  },
])
export default class ContactsView extends Component {
  render() {
    return (
      <Keel>
        <KeelSingleViewHeader viewKey="SupplierManagerView" showHeader={false} />
        <KeelViewBody classNameKey="content-main" />
      </Keel>
    );
  }
}
