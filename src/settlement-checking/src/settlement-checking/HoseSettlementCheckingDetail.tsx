import React from 'react';
import HoseFeeDetailWrapper from './components/HoseFeeDetailWrapper';
import { inject, provider } from '@ekuaibao/react-ioc';
import { FeeDetailVm } from './vms/FeeDetail.vm';
import { HoseFeeDetailVm } from './vms/HoseFeeDetail.vm';
import { SettlementCheckingDetailVm } from './vms/settlement-checking-detail.vm';
import { DepSettlementVm } from './vms/DepSettlement.vm';
import { ICheckingBill } from '@ekuaibao/ekuaibao_types';
// @ts-ignore
import styles from './HoseSettlementCheckingDetail.module.less';
import HoseFeeTypeStatistics from './components/HoseFeeTypeStatistics';
import HoseBillStatistics from './components/HoseBillStatistics';
import HoseCheckingBillTop from './components/HoseCheckingBillTop';
import { Alert } from 'antd';
import { EnhanceConnect } from '@ekuaibao/store';
import { getConfigSettlementList, getDepartementCheckConfig } from '../setttlement-checkin-action';
import { get } from 'lodash';
import FeeDetailWrapper from "./components/FeeDetailWrapper";
import useLayoutDriver from '@ekuaibao/lib/lib/layoutDriver';
import { SettlementGuideStep } from '../types/enums'
import MessageCenter from '@ekuaibao/messagecenter';
interface SettlementCheckingDetailProps {
  [key: string]: any;
  value: ICheckingBill;
}
interface SettlementCheckingDetailState { }
const hasCheckConfig = (config: any) => config?.type === 'BILL_FIELD'
@provider(
  [FeeDetailVm.NAME, FeeDetailVm],
  [HoseFeeDetailVm.NAME, HoseFeeDetailVm],
  [SettlementCheckingDetailVm.NAME, SettlementCheckingDetailVm],
  [DepSettlementVm.NAME, DepSettlementVm],
)

@EnhanceConnect((state: any) => ({
  KA_ZY_Reconciliation_Settlement: state['@common'].powers.KA_ZY_Reconciliation_Settlement,
  userInfo: state['@common'].userinfo,
}))
export default class SettlementCheckingDetail extends React.Component<
  SettlementCheckingDetailProps,
  SettlementCheckingDetailState
> {
  @inject(SettlementCheckingDetailVm.NAME) detailVm: SettlementCheckingDetailVm;
  state = {
    isClose: false,
    isApportions: false, // 结算单配置法人实体维度是否设置分摊
    depParams: {},// 是否是部门对账，是否在拓展中心对账结算配置部门字段
  };

  bus = new MessageCenter()
  async componentWillMount() {
    // this.props?._id无值为从结算概览穿透过来的，这种场景下，如果该供应商账户的结算单设置结算维度中法人实体包含明细分摊，则异常对账单详情卡片
    if (!this.props?._id) {
      let isApportions = false;
      const supplierAccountId = get(this.props, 'value.supplierAccountId.id');
      const settlementList = await getConfigSettlementList();
      if (settlementList?.items?.length && supplierAccountId) {
        const settlementItem = settlementList?.items.find((el: any) => el.supplierAccountId === supplierAccountId);
        if (
          settlementItem &&
          settlementItem?.settlementDimension?.type === 'legalEntity' &&
          settlementItem?.settlementDimension?.apportions === true
        ) {
          isApportions = true;
        }
      }
      this.setState({ isApportions });
    }
  }
  getDepartementCheckConfig = async () => {
    const { corporation = {} } = this.props.userInfo
    const { value } = await getDepartementCheckConfig(corporation.id)
    let depParams: any = {}
    if (hasCheckConfig(value)) {
      depParams.expenseDepartment = 'expenseDepartment'
    }
    this.setState({ depParams })
  }
  componentDidMount() {
    this.detailVm.init(this.props.value);
    const { tppReason, tppState } = this.props.value;
    this.setState({ isClose: !!(tppState !== 'agree' && tppReason) });
    this.getDepartementCheckConfig()
    this.handleShowGuide()
  }

  handleClose = () => {
    this.setState({
      isClose: false
    })
  }

  handleShowGuide = async () => {
    const step: string = localStorage.getItem('settlement_guide_step') || SettlementGuideStep.FIRST
    if (step === SettlementGuideStep.CLOSE) {
      return
    }
    const guideFirst = document.getElementById('statement-details-guide-first')
    const guideSecond = document.getElementById('statement-details-guide-second')
    const guideThird = document.getElementById('statement-details-guide-third')
    const guideFourth = document.getElementById('statement-details-guide-fourth')
    const guideFirstChildNode = document.querySelector('#statement-details-guide-first > .hose-bill-statistics')
    const idsMap = {
      '0': guideFirst,
      '1': guideSecond,
      '2': guideThird,
      '3': guideFourth,
    }
    if (guideFirst && guideSecond && guideThird && guideFourth) {
      const onPrevious = (currentStep: string) => {
        localStorage.setItem('settlement_guide_step', currentStep)
        const stepNode = get(idsMap, currentStep)
        stepNode.style.zIndex = 'auto'
        stepNode.style.pointerEvents = 'auto'
        if (guideFirstChildNode) {
          guideFirstChildNode.style.overflow = 'auto'
        }
        driver.reset()
      }
      const params = [
        {
          dom: guideFirst,
          title: i18n.get('概览移到这里了'),
          description: i18n.get('增加了数据图标，查看概览更加一目了然'),
          prevBtnText: i18n.get('稍后再说'),
          nextBtnText: i18n.get('下一步'),
          position: 'right',
          onPrevious: () => onPrevious(SettlementGuideStep.FIRST),
          onNext: () => {
            // 解决跳转下一步元素层级高问题, 恢复上一步的样式写入
            guideFirst.style.zIndex = 'auto'
            guideFirst.style.pointerEvents = 'auto'
            guideSecond.style.zIndex = '100004'
            guideSecond.style.pointerEvents = 'none'
          },
        },
        {
          dom: guideSecond,
          title: i18n.get('点击这里可以收起/展示边栏'),
          description: i18n.get('收起后右侧数据会有更大的展示空间'),
          prevBtnText: i18n.get('稍后再说'),
          doneBtnText: i18n.get('下一步'),
          position: 'right',
          onPrevious: () => onPrevious(SettlementGuideStep.SECOND),
          onNext: () => {
            // 解决跳转下一步元素层级高问题, 引导时不显示hover效果，引导结束回复hover效果
            guideSecond.style.zIndex = 'auto'
            guideSecond.style.pointerEvents = 'auto'
            guideThird.style.zIndex = '100004'
            guideThird.style.pointerEvents = 'none'
          },
        },
        {
          dom: guideThird,
          title: i18n.get('费用概览表也可以折叠/展开'),
          description: i18n.get('折叠后查看明细更方便'),
          prevBtnText: i18n.get('稍后再说'),
          doneBtnText: i18n.get('下一步'),
          position: 'left',
          onPrevious: () => onPrevious(SettlementGuideStep.THIRD),
          onNext: () => {
            guideThird.style.zIndex = 'auto'
            guideThird.style.pointerEvents = 'auto'
            guideFourth.style.zIndex = '100004'
            guideFourth.style.pointerEvents = 'none'
          },
        },
        {
          dom: guideFourth,
          title: i18n.get('新增了表格全屏能力'),
          description: i18n.get('沉浸式查看数据，杜绝一切视觉干扰'),
          prevBtnText: i18n.get('稍后再说'),
          doneBtnText: i18n.get('关闭'),
          position: 'left',
          onNext: () => {
            localStorage.setItem('settlement_guide_step', SettlementGuideStep.CLOSE)
            guideFourth.style.zIndex = 'auto'
            guideFourth.style.pointerEvents = 'auto'
            if (guideFirstChildNode) {
              guideFirstChildNode.style.overflow = 'auto'
            }
          },
          onPrevious: () => onPrevious(SettlementGuideStep.FOURTH),
        },
      ]
      const driver = useLayoutDriver(params)
      // 解决第一步进入时可折叠和查看详情操作，点过稍后再说，再次进入需要直接到该节点的状态
      const stepNode = get(idsMap, step)
      stepNode.style.zIndex = '100004'
      stepNode.style.pointerEvents = 'none'
      if (guideFirstChildNode) {
        guideFirstChildNode.style.overflow = 'hidden'
      }
      driver.start(Number(step))
    }
  }

  render() {
    const { stackerManager } = this.props;
    const { isApportions, depParams, isClose } = this.state;
    return (
      <div className={styles['hose-settlement-checking-detail-wrapper']}>
        <div className="hose-content-wrapper">
          {isClose && (
            <Alert message={this.props.value.tppReason} type="warning" showIcon closable style={{ marginBottom: 4 }} onClose={this.handleClose} />
          )}
          {<HoseCheckingBillTop value={this.props.value} bus={this.bus} />}
          <div className={`${isClose ? 'detail-settlement-reason-warp' : 'detail-settlement-warp'}`}>
            {(this.props.value as any)?.hideButton &&
              (this.props.value as any)?.hiddenCheckBill &&
              (this.props.value as any)?.hiddenInvoice &&
              (this.props.value as any)?.hiddenSettle ? null : (
              <HoseBillStatistics
                hideButton={(this.props.value as any)?.hideButton}
                hiddenCheckBill={isApportions ? isApportions : (this.props.value as any)?.hiddenCheckBill}
                hiddenInvoice={(this.props.value as any)?.hiddenInvoice}
                hiddenSettle={(this.props.value as any)?.hiddenSettle}
                value={this.props.value}
                stackerManager={stackerManager}
                bus={this.bus}
              />
            )}
            <div className={styles['hose-feetype-statistics-right']}>
              <div className='feetype-statistics'>
                <HoseFeeTypeStatistics value={this.props.value} searchApportions={isApportions} depParams={depParams} />
                {
                  this.props.KA_ZY_Reconciliation_Settlement ?
                    <FeeDetailWrapper
                      settlementManager={this.props.KA_ZY_Reconciliation_Settlement}
                      value={this.props.value}
                      isApportions={isApportions}
                      sourceType={'SettlementCheckingDetail'}
                    /> :
                    <HoseFeeDetailWrapper
                      settlementManager={false}
                      value={this.props.value}
                      isApportions={isApportions}
                      sourceType={'SettlementCheckingDetail'}
                    />
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
