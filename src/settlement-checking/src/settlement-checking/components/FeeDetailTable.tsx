/**
 *  Created by pw on 2021/6/8 下午5:18.
 */
import React, { useEffect, useState, forwardRef } from 'react';
import TableWrapper from '../../components/TableWrapper';
import { useInstance } from '@ekuaibao/react-ioc';
import { FeeDetailVm } from '../vms/FeeDetail.vm';
import { PageMode, PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination';
import { useObserver } from 'mobx-react-lite';
import './FeeDetailTable.less';
import { app } from '@ekuaibao/whispered';
import { CheckingDetailTab, FeeDetailFilterType } from '../../types/enums';
import { Map } from '@ekuaibao/datagrid/lib/types/utils';
import { Data } from '@ekuaibao/datagrid/lib/types/dataSource';
import { showMessage } from '@ekuaibao/show-util';
import { ECheckingState } from '@ekuaibao/ekuaibao_types';
import { openFeeDetailAfter, openFeeDetailPre } from '../utils/open-detail-utils';
import { T } from '@ekuaibao/i18n';
const { fnParseDetails } = app.require('@bills/util/parse');
import { Resource } from '@ekuaibao/fetch';
import { EnhanceConnect } from '@ekuaibao/store';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import { Select } from 'antd';
import { CheckingDetailBottom } from './CheckingDetailBottom';
import { searchOptionDepartment } from '../utils/data-map';
interface Props {
  type: CheckingDetailTab;
  footerAction: React.ReactNode;
  value?: any;
  settlementManager?: boolean;
  [key: string]: any;
  isApportions: boolean;
  legalEntityName: string;
  sourceType?: string;
}
let _instance: any;
let activeScene: any = 'all';
const receiptResource = new Resource('/api/flow/v2/filter')

export const FeeDetailTable: React.FC<Props> = ({
  type,
  footerAction,
  value,
  settlementManager = false,
  KA_ZY_Reconciliation_Settlement,
  isApportions,
  legalEntityName,
  sourceType,
}) => {
  const vm = useInstance<FeeDetailVm>(FeeDetailVm.NAME);
  const [isVisibleSaveDiff, setIsVisibleSaveDiff] = useState(false);
  const [scenesInfo, setScenesInfo] = useState<any[]>([{ text: '全部', scene: 'all', sceneIndex: 'all'}]);
  const [summary, setSummary] = useState<any[]>();

  useEffect(() => {
    vm.settlementCharge = KA_ZY_Reconciliation_Settlement;
    // 获取场景列表
    vm.updateTabColumn = updateTabColumn;
    _instance && _instance.resize();
    return () => {};
  }, [vm.tabType]);

  const updateTabColumn = async () => {
    if (!KA_ZY_Reconciliation_Settlement) return;
    activeScene = 'all';
    vm.activeScene = 'all';
    let scenesRes = await receiptResource.GET('/$type', { type: settlementManager ? 'FEE_DETAIL_STATISTICS' : 'STATEMENTDEPART' })
    let data = scenesRes;
    let sceneInfo = data?.value?.filter.map((v) => JSON.parse(v));
    if (sceneInfo?.length > 0) {
      setScenesInfo(sceneInfo)
      let defaultScene = [];
      for (let k in sceneInfo) {
        if (sceneInfo[k]?.ruleType === 'default') defaultScene.push(sceneInfo[k].sceneIndex);
      }
      vm.defaultScene = defaultScene;
      //激活activeScene
      let targetScene = sceneInfo.find((v) => v.sceneIndex === activeScene) || {};
      setSummary([
        {
          column: targetScene?.statisticRule?.key, //统计金款金额
          showInGroupFooter: false, //不展示在组的底部
          alignByColumn: true, //展示在组的标题上
          displayFormat: '合计¥: {0}', //默认统计文本
          summaryType: targetScene?.statisticRule?.algorithm, //取所有相加的合计
          showInColumn: targetScene?.statisticRule?.key,
          valueFormat: (value: number) => {
            return value.toFixed(2);
          },
        },
      ]);
      vm.updateColumns(targetScene?.defaultColumns, targetScene?.groupDimension);
      handleFilterChange({});
    }
  };

  const handleSearch = (value: string) => {
    vm.search = value;
  };

  const handleClear = () => {
    vm.search = '';
  };

  const handleColumnChooseChange = (visibleColumns: string[]) => {
    vm.updateColumns(visibleColumns);
  };

  const handleColumnReset = () => {
    vm.resetColumns();
  };
  const getInstance = (instance: any) => {
    _instance = instance;
  };
  const handleSorterChange = (sorter: Map<'ascend' | 'descend'>) => {
    vm.sorters = sorter;
  };

  const handleFilterChange = (filter: Map<any>) => {
    vm.filters = filter;
  };

  const handleSelectedChange = (selectedKeys: string[], selectedRowsData: Map<Data>) => {
    vm.selectedKeys = selectedKeys;
  };

  const handlePageChange = (pagination: PaginationConfig, pageMode: PageMode) => {
    vm.pageSize = pagination.size;
    vm.currentPage = pagination.current;
    vm.pageMode = pageMode;
  };

  const refreshState = (obj: any) => {
    setIsVisibleSaveDiff(obj);
    if (obj.hasOwnProperty('isVisibleSaveDiff')) {
      setIsVisibleSaveDiff(obj['isVisibleSaveDiff']);
    }
  };

  const handleTableRowClick = async (data) => {
    try {
      const [feeTypes, userinfo, feeDetail, applicationListDetails] = await openFeeDetailPre(data, vm);
      const result: any[] = await app.open('@bills:BillStackerModal', {
        viewKey: 'FeeDetailView',
        viewTitle: i18n.get('编辑消费详情'),
        isRejectCancel: true,
        applicationListDetails,
        dataSource: { showAllFeeType: true, ...feeDetail?.value?.form },
        billData: { details: [], submitterId: userinfo.staff },
        isEdit: data.checkingState === ECheckingState.TODO,
        billSpecification: {
          components: [],
          type: 'expense',
          configs: [],
        },
        submitterId: userinfo.staff,
        visibleFeeTypes: feeTypes,
        disableFields: ['amount', 'system_statement'],
        isSettlement: true,
      });
      const baseDataProperties = await app.getState('@common.globalFields.data');
      const arr = fnParseDetails(result, baseDataProperties, true);
      const saveData = {
        id: data.id,
        form: arr[0],
      };
      openFeeDetailAfter();
      await vm.modifyFeeDetail(saveData);
      showMessage.success(i18n.get('保存成功'));
      await vm.fetch();
    } catch (e) {
      showMessage.error(e?.message ?? i18n.get('发生错误'));
    }
  };

  const handleClick = (record: any, dataSource: any[]) => {
    vm.fetchApportionColumn(record.id).then((res) => {
      const { columns } = res;
      app.open('@settlement-checkin:ApportionsModal', { dataSource, columns });
    });
  };

  const rowClassName = (index) => {
    const record = vm.dataSource?.[index];
    return record?.flowId?.state === 'rejected' ? 'table-row-error' : '';
  };

  const onSaveDiffScenes = () => {
    if (_instance) {
      let visibleColumns = _instance && _instance.getVisibleColumns ? _instance.getVisibleColumns() : [];
      visibleColumns =
        visibleColumns.length > 0 && visibleColumns.filter((d: any) => d.dataField).map((d: any) => d.dataField);
      let filt = false
      let targetScene
      let promise: Promise<any>;
      for(let i of scenesInfo) {
        if (i.sceneIndex === activeScene) {
          targetScene = i
          if (i?.groupDimension?.length > 0 && i?.hasOwnProperty('statisticRule')) {
            let arr = cloneDeep(i.groupDimension) ?? []//分组维度
            arr.push(i?.statisticRule?.key)//统计值
            for(const item of arr) {
              if(!!!visibleColumns.includes(item)) {
                filt = true
                break;
              }
            }
          }
        }
      }
      if (!!filt) return showMessage.error(i18n.get('变更的字段里包含分组维度中正在使用的字段，无法操作'))
      if (targetScene) {
        targetScene.defaultColumns = visibleColumns
      }

      const filter = scenesInfo.map((scene: any) => JSON.stringify(scene));
      promise = receiptResource.POST('/$type', {
        type: settlementManager ? 'FEE_DETAIL_STATISTICS' : 'STATEMENTDEPART',
        filter,
      })
      promise
        .then(() => {
          showMessage.success(i18n.get('保存成功'));
          setIsVisibleSaveDiff(false);
        })
        .catch((err) => {
          showMessage.error(err.message);
        });
    }
  };

  const onChangeScene = (scene: any) => {
    activeScene = scene;
    const sceneTab = scenesInfo.filter((i) => i.sceneIndex === scene)[0] ?? {};
    vm.activeScene = scene;
    //获取统计维度数据
    setSummary([
      {
        column: sceneTab?.statisticRule?.key, //统计金款金额
        showInGroupFooter: false, //不展示在组的底部
        alignByColumn: true, //展示在组的标题上
        displayFormat: '合计¥: {0}', //默认统计文本
        summaryType: sceneTab?.statisticRule?.algorithm, //取所有相加的合计
        showInColumn: sceneTab?.statisticRule?.key,
        valueFormat: (value: number) => {
          return value.toFixed(2);
        },
      },
    ]);
    setIsVisibleSaveDiff(false)
    vm.updateColumns(sceneTab?.defaultColumns, sceneTab?.groupDimension);
    handleFilterChange(sceneTab?.filters?.[0] ?? {});
  };

  const onEditScenes = async () => {
    let switcherDataIndexes = _instance && _instance.getVisibleColumns ? _instance.getVisibleColumns() : [];
    switcherDataIndexes =
      switcherDataIndexes.length > 0 &&
      switcherDataIndexes.filter((d: any) => d.dataField).map((d: any) => d.dataField);
    const stackerOptions = {
      viewKey: 'EditScenesView',
      scenes: scenesInfo, //当前scenes
      columns: vm.allColumns,
      prefixColumns: { state: 'feeTypeForm', '*': 'form.feeTypeForm' },
      switcherDataIndexes, //当前column
      noType: true,
      isGroup: false,
      scenesType: settlementManager ? 'STATEMENT' : 'STATEMENTDEPART',
    };
    app.open('@layout:ScenesStackerModal', stackerOptions).then((data: any) => {
      const filter = data.scenes.map((s: any) => JSON.stringify(s));
      return receiptResource.POST('/$type', {
        type: settlementManager ? 'FEE_DETAIL_STATISTICS' : 'STATEMENTDEPART',
        filter,
      }).then((scenesRes) => {
        let scene = scenesRes?.value?.filter.map((v) => JSON.parse(v));
        setScenesInfo(scene);
        if (scene && scene.length) {
          let defaultScene = scene.find((v) => v.sceneIndex === activeScene) || {};
          setSummary([
            {
              column: defaultScene?.statisticRule?.key, //统计金款金额
              showInGroupFooter: false, //不展示在组的底部
              alignByColumn: true, //展示在组的标题上
              displayFormat: '合计¥: {0}', //默认统计文本
              summaryType: defaultScene?.statisticRule?.algorithm, //取所有相加的合计
              showInColumn: defaultScene?.statisticRule?.key,
              valueFormat: (value: number) => {
                return value.toFixed(2);
              },
            },
          ]);
          vm.updateColumns(defaultScene?.defaultColumns, defaultScene?.groupDimension);
          handleFilterChange(defaultScene?.filters?.[0] ?? {});
        }
      });
    });
  };

  const initDate = (groupDimensions: Array<any>) => {
    const propertySet = app.getState('@common.globalFields.data') as any;
    for (let j = 0; j < groupDimensions.length; j++) {
      groupDimensions[j] = groupDimensions[j].split('.')[groupDimensions[j].split('.').length - 1];
    }
    for (const it of vm.dataSource) {
      let feeTypeForm = it?.form?.feeTypeForm || {};
      for (let k in feeTypeForm) {
        if (groupDimensions.includes(k)) {
          let elm = propertySet.find((item) => {
            return item.name === k;
          });
          if (elm.dataType.type === 'date') {
            feeTypeForm[k] = Number(moment(feeTypeForm[k]).startOf('day'));
          }
        }
      }
    }
  };

  const action = {
    title: i18n.get('操作'),
    dataIndex: 'operation',
    fixed: 'right',
    width: 150,
    render: (value: any, record: any) => {
      let dataSource = record?.form?.feeTypeForm?.apportions?.slice() || [];
      return (
        <div
          onClick={(e) => {
            e.persist();
            e.nativeEvent.stopImmediatePropagation();
            e.stopPropagation();
            e.preventDefault();
            return false;
          }}
        >
          {dataSource.length > 0 && (
            <a className="action" onClick={() => handleClick(record, dataSource)}>
              <T name="查看分摊明细" />
            </a>
          )}
        </div>
      );
    },
  };

  return useObserver(() => {
    let activeSceneInfo = scenesInfo?.find((v) => {
      return v?.sceneIndex === activeScene;
    });
    let groupDimensions = cloneDeep(activeSceneInfo?.groupDimension) || [];
    if (groupDimensions?.length > 0) {
      //当选择按日期分组时，里面的日期格式需要统一转成时分秒为0，以确保同一天时分秒不同的话可以统计在同一天
      initDate(groupDimensions);
    }

    const columnChooserProps = {
      defaultVisibleColumns: vm.defaultVisibleColumns,
      columns: vm.allColumns,
      onChange: handleColumnChooseChange,
      onReset: handleColumnReset,
      refreshState: refreshState.bind(this),
    };

    let columns = vm.columns.slice();
    columns.push(action);
    if (isApportions && legalEntityName) {
      columns = columns.filter((item: any) => !JSON.stringify(item).includes('form.feeTypeForm.amount'));
    }

    let tableProps = {
      dataSource: vm.dataSource,
      columns: columns,
      filters: vm.filters,
      scrolling: vm.scrolling,
      pageSize: vm.pageSize,
      groupPanel: { visible: true, allowColumnDragging: false },
      selectedRowKeys: vm.selectedKeys,
      onSelectedChange: handleSelectedChange,
      onSorterChange: handleSorterChange,
      onFilterChange: handleFilterChange,
      onRowClick: handleTableRowClick,
      disabledScroll: true,
      fixedSelect: true,
      rowClassName: rowClassName,
      getInstance: getInstance,
      allowColumnReordering: false,
      allowColumnResizing:true
    };

    if (KA_ZY_Reconciliation_Settlement && summary?.length > 0) {
      tableProps.summary = summary;
      tableProps.key = JSON.stringify(summary);
    }
    let scenesAvailable = false;
    if (KA_ZY_Reconciliation_Settlement) {
      scenesAvailable = true;
      if (!settlementManager && !vm?.checkingBill?.feeTypeAmount?.standard) {
        scenesAvailable = false;
      }
    }
    const handleNewSearch = (value) => {
      vm.searchOptionText = value;
    }

    return (
      <div className="checking-fee-detail-table-wrapper">
        <TableWrapper
          tableProps={tableProps}
          scenesProps={scenesAvailable ? { scenes: scenesInfo, onChangeScene, activeScene, onEditScenes } : null}
          // searchProps={{ placeholder: '请搜索费用承担部门', onSearch: handleSearch, onClear: handleClear }}
          searchProps={{ newSearch: true, searchOptions: [searchOptionDepartment()], onNewSearch: handleNewSearch}}
          exportProps={{
            className: `fee-detail-table-export-btn ${
              value?.hideButton ? 'fee-detail-table-export-btn-right' : ''
            } `,
            onClick: () => vm.onExport(legalEntityName),
          }}
          columnChooserProps={columnChooserProps}
          footerAction={footerAction}
          paginationProps={
            !KA_ZY_Reconciliation_Settlement || !(vm.groupDimension && vm.groupDimension?.length > 0)
              ? {
                  totalLength: vm.dataTotal,
                  pagination: {
                    current: vm.currentPage,
                    size: vm.pageSize,
                  },
                  onChange: handlePageChange,
                  pageMode: vm.pageMode,
                }
              : null
          }
          searchClass={`fee-detail-table-search-wrapper ${
            value?.hideButton ? 'fee-detail-table-search-wrapper-right' : ''
          }`}
          columnChooserClassName={`fee-detail-table-column-chooser ${
            value?.hideButton ? 'fee-detail-table-column-chooser-right' : ''
          }`}
          tableLeftAction={
            sourceType === 'SettlementCheckingDetail' ? (
              <div className="fee-detail-table-left-action">
                <Select
                  className={'fee-detail-table-left-action-select'}
                  size={'large'}
                  defaultValue={FeeDetailFilterType.ALL}
                  onSelect={(value: any) => (vm.filterFeeDetailByType = value)}
                >
                  <Select.Option value={FeeDetailFilterType.ALL}>{i18n.get('全部费用')}</Select.Option>
                  <Select.Option value={FeeDetailFilterType.DisabledFee}>
                    {i18n.get('引用了停用数据的费用')}
                  </Select.Option>
                  <Select.Option value={FeeDetailFilterType.IncompleteFee}>
                    {i18n.get('填写不完整的费用')}
                  </Select.Option>
                </Select>
              </div>
            ) : null
          }
          tableRightAction={
            !value?.hideButton && (
              <div className="fee-detail-table-right-action">
                <CheckingDetailBottom />
              </div>
            )
          }
          bodyStyle={{ 'border-top': 'unset' }}
        />

        {isVisibleSaveDiff && (
          <div
            style={{
              position: 'absolute',
              top: sourceType === 'SettlementCheckingDetail' ? '45px' : '8px',
              right: '465px',
              fontSize: '14px',
              color: 'var(--brand-base)',
              cursor: 'pointer'
            }}
            onClick={() => {
              onSaveDiffScenes();
            }}
          >
            {i18n.get('保存变更')}
          </div>
        )}
      </div>
    );
  });
};

export default EnhanceConnect((state: any) => ({
  KA_ZY_Reconciliation_Settlement: state['@common'].powers.KA_ZY_Reconciliation_Settlement,
}))(forwardRef(FeeDetailTable as any));
