import React, { useEffect, useState } from 'react';
import { useInstance } from '@ekuaibao/react-ioc';
import { HoseCheckingBillStatisticsDetailVm } from '../vms/hose-checking-bill-statistics-detail.vm';
import HoseTableWrapper from '../../components/HoseTableWrapper';
import { useObserver } from 'mobx-react-lite';
import { Map } from '@ekuaibao/datagrid/lib/types/utils';
import { Data } from '@ekuaibao/datagrid/lib/types/dataSource';
import { showMessage } from '@ekuaibao/show-util';
import { app } from '@ekuaibao/whispered';
import HoseCheckingBillStatisticsDetailBottom
  from "./HoseCheckingBillStatisticsDetailBottom";
import {Button, Tooltip} from "@hose/eui";
import {OutlinedDirectionRefresh} from "@hose/eui-icons";
import {checkingSaveBill} from "../../setttlement-checkin-action";
interface CheckingBillStatisticsListProps {
  hideButton?: boolean
}

export const HoseCheckingBillStatisticsList: React.FC<CheckingBillStatisticsListProps> = (props) => {
  const [isFull, setIsFull] = useState(false);
  const vm = useInstance<HoseCheckingBillStatisticsDetailVm>(HoseCheckingBillStatisticsDetailVm.NAME);
  const [isVisibleSaveDiff, setIsVisibleSaveDiff] = useState(false);
  const userInfo = app.getState('@common.userinfo')
  const handleSorterChange = (sorter: Map<'ascend' | 'descend'>) => {
    vm.sorters = sorter;
  };
  const handleFullScreen = () => {
    setIsFull(!isFull)
  }
  const handleFilterChange = (filter: Map<any>) => {
    vm.filters = filter;
  };

  const handleSelectedChange = (selectedKeys: string[], selectedRowsData: Map<Data>) => {
    vm.selectedKeys = selectedKeys;
  };
  const handleColumnChooseChange = (visibleColumns: string[]) => {
    vm.updateColumns(visibleColumns);
  };
  const handleColumnReset = () => {
    vm.resetColumns();
  };

  useEffect(() => {
    vm.bus.watch('get:column:checked:value', getColumns);
    vm.getScene();
    return () => {
      vm.bus.un('get:column:checked:value', getColumns);
    };
  }, []);

  function getColumns() {
    return vm.columns.map((v: any) => v.dataIndex).filter((v) => v);
  }

  const handleSearch = (value: string) => {
    vm.search = value;
  };

  const handleClear = () => {
    vm.search = '';
  };

  const handlePageChange = (pageNum: number, pageSize: number) => {
    vm.pageSize = pageSize;
    localStorage.setItem(`${userInfo.staff?.id}CheckingOverview`, pageSize?.toString())
    vm.currentPage = pageNum;
  };

  const refreshState = (obj: any) => {
    setIsVisibleSaveDiff(obj);
    if (obj.hasOwnProperty('isVisibleSaveDiff')) {
      setIsVisibleSaveDiff(obj['isVisibleSaveDiff']);
    }
  };

  const handleRefresh = async () => {
    const type = 'CHECKING_SUBMIT'
    let res: any = await checkingSaveBill({ type, checkingBillId: vm.checkingBillInfo.id })
    if (!res.value || !['SUCCESS', 'CONFIRMED'].includes(res?.value?.state)) {
      let result = ''
      let failure = []
      if(!res.value) {
        showMessage.success(i18n.get('刷新成功'))
        await vm.refreshData();
        return ''
      }
      if (res?.value?.state === 'PENDING') {
        result = 'FAILURE'
        failure = ['对账单创建中,请稍后再试']
      } else {
        result = res?.value?.state
        failure = res?.value?.failureReasons
      }
      await app.open('@settlement-checkin:SubmitCheckingModal', { result, failureReasons: failure, type, value: res.value })
    }
    await vm.refreshData();
    return '';
  };

  const onSaveDiffScenes = () => {
    let visibleColumns: any = vm.columns.length ? vm.columns : [];
    visibleColumns = visibleColumns.filter((d: any) => d?.key).map((d: any) => d?.key);
    vm.saveScene(visibleColumns)
      .then(() => {
        showMessage.success(i18n.get('保存成功'));
        setIsVisibleSaveDiff(false);
      })
      .catch((err: any) => {
        showMessage.error(err.message);
      });
  };
  console.log(vm.dataSource, 'dataSourcedataSource222')
  return useObserver(() => {
    return (
      <div className={`hose-checking-detail-list ${isFull ? 'hose-screen' : ''}`}>
        <HoseTableWrapper
          footerAction={<HoseCheckingBillStatisticsDetailBottom hideButton={props.hideButton} exportProps={{onClick: vm.onExport}}/>}
          tableProps={{
            dataSource: vm.dataSource,
            columns: vm.columns,
            filters: vm.filters,
            sorters: vm.sorters,
            pageSize: vm.dataSource.length,
            selectedRowKeys: vm.selectedKeys,
            onSorterChange: handleSorterChange,
            onFilterChange: handleFilterChange,
            isFull,
            handleFullScreen,
            rowSelection: {
              type: 'checkbox',
              onChange: handleSelectedChange
            },
            scroll: { x: vm.columns?.length * 200, y: isFull ? 'calc(100vh - 200px)' : 'calc(100vh - 450px)' }
          }}
          columnChooserProps={{
            defaultVisibleColumns: vm.columns.map((item) => item.dataIndex).filter((item) => !!item),
            columns: vm.allColumns,
            onChange: handleColumnChooseChange,
            onReset: handleColumnReset,
            refreshState: (params: any) => refreshState(params),
          }}
          searchProps={{ placeholder: '请搜索单据名称或单号', onSearch: handleSearch, onClear: handleClear }}
          columnChooserClassName={'checking-detail-list-column-chooser'}
          searchClass={'checking-detail-list-search'}
          paginationProps={{
            total: vm.total,
            current: vm.currentPage,
            pageSize: localStorage.getItem(`${userInfo.staff?.id}CheckingOverview`) ? Number(localStorage.getItem(`${userInfo.staff?.id}CheckingOverview`)) : vm.pageSize,
            onChange: handlePageChange,
            showTotal: (total: number) => i18n.get(`共{__k0}条`, { __k0: total })
          }}
          tableRightAction={
            <Tooltip placement={isFull ? 'left' : 'top'} title={i18n.get('刷新')} >
              <Button onClick={handleRefresh} category="secondary" className='operate-icon' ><OutlinedDirectionRefresh fontSize={16} /></Button>
            </Tooltip>
          }
          visibleSaveDiff={isVisibleSaveDiff && (
            <div
              style={{
                color: 'var(--eui-primary-pri-500)',
                cursor: 'pointer',
                marginRight: '8px'
              }}
              onClick={() => {
                onSaveDiffScenes();
              }}
            >
              {i18n.get('保存变更')}
            </div>
          )}
          bodyStyle={{ 'border-top': 'unset' }}
        />
      </div>
    );
  });
};

export default HoseCheckingBillStatisticsList;
