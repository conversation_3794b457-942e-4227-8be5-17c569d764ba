.overview-progress-wrapper {
  background-color: var(--eui-static-white);
  display: flex;

  :global {
    .header-wrapper {
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;

      .title {
        font: var(--eui-font-body-b1);
        color: var(--eui-text-title);
      }

      .more {
        font: var(--eui-font-body-r1);
        color: var(--eui-text-caption);
        cursor: pointer;
      }
    }

    .COR_SETTLE_PAID {
      background-color: var(--eui-function-success-400);
    }

    .COR_SETTLE_PAYING {
      background-color: var(--eui-function-warning-400);
    }

    .COR_SETTLE_PENDING {
      background-color: var(--eui-function-warning-400);
    }

    .COR_SETTLE_DRAFT {
      background-color: var(--eui-function-info-400);
    }
    .COR_SETTLE_WGH {
      background-color: var(--eui-decorative-neu-200);
    }

    .progress-wrapper {
      display: flex;
      flex-direction: column;
      width: 200px;
      .money {
        margin-top: 24px;
        font: var(--eui-num-display-b2);
        color: var(--eui-text-title);
      }

      .subtitle {
        margin-top: 3px;
        font: var(--eui-text-body-r1);
        color: var(--eui-text-placeholder);
      }
      .progress-money {
        font: var(--eui-num-head-b2);
        color: var(--eui-text-title);
        margin-bottom: 4px;
      }
      .progress-title {
        font: var(--eui-font-note-r2);
        color: var(--eui-text-caption);
        margin-bottom: 4px;
      }
      .progress-view {
        height: 4px;
        width: 100%;
        display: flex;
        background-color: var(--eui-decorative-neu-200);
        .box {
          &:first-child {
            border-left: unset;
          }
          border-left: 1px solid var(--eui-static-white);
          display: flex;
          height: 4px;
        }
      }
    }

    .content-wrapper {
      margin-left: 42px;
      .content-border-bottom {
        border-bottom: 1px dashed var(--eui-line-divider-default);
        padding-bottom: 6px;
      }
      .content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        .content-status {
          display: inline-block;
        }
        .content-th-font {
          font: var(--eui-font-body-r1);
          color: var(--eui-text-caption);
        }
        .content-money {
          width: 49px;
          display: inline-block;
          text-align: right;
        }
        .content-money-font {
          font: var(--eui-num-body-b1);
          color: #0A0A0A;
        }
        .money-padding {
          padding-right: 4px;
        }
        .td-warp {
          display: flex;
        }
        .left-v {
          display: flex;
          align-items: center;
          margin-right: 8px;
          .dot {
            width: 6px;
            height: 6px;
            border-radius: 6px;
          }

          .status {
            margin-left: 6px;
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            color: #404040;
          }
        }

        .money {
          font: var(--eui-num-body-b1);
          color: var(--eui-text-title);
          width: 100px;
          display: flex;
          justify-content: right;
        }
      }

      .content-left {
        justify-content: unset;
      }
    }
  }
}

.receive-progress-wrapper-row {
  flex-direction: row;
  align-items: flex-end;
}
.tooltips {

  :global {
    .ant-tooltip-inner {
      padding: 4px 11px;
      font: var(--eui-font-note-r2);
      background: var(--eui-text-title);
      box-shadow: var(--eui-shadow-down-1);
      border-radius: 4px;
    }
  }
}
