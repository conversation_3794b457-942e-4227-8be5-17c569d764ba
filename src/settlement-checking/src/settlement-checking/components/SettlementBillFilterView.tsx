import React from 'react';
import { T } from '@ekuaibao/i18n';
import { Button, Select, Tooltip, Space } from '@hose/eui';
import { useObserver } from 'mobx-react-lite';
import { useInstance } from '@ekuaibao/react-ioc';
import {
  checkingState,
  invoiceState,
  SettlementCheckingListVm,
  settlementState,
} from '../vms/settlement-checking-list.vm';

interface SettlementBillFilterViewProps { }

export const SettlementBillFilterView: React.FC<SettlementBillFilterViewProps> = () => {
  const vm = useInstance<SettlementCheckingListVm>(SettlementCheckingListVm.NAME);
  const supplierChange = (value) => {
    vm.setSupplier(value);
  };

  const periodChange = (value) => {
    vm.setBillPeriod(value);
  };

  const settlementStateChange = (value) => {
    vm.setSettlementValue(value);
  };

  const invoiceStateChange = (value) => {
    vm.setInvoiceValue(value);
  };

  const checkingStateChange = (value) => {
    vm.setCheckingState(value);
  };

  const handleSearch = async () => {
    await vm.getData();
  };

  const handleReset = async () => {
    vm.reset();
    await vm.getData();
  };
  return useObserver(() => {
    return (
      <div className="filter-wrapper">
        <Space size={[16, 16]} wrap>
          <ItemWrapper name={<T name="供应商" />}>
            <Select
              allowClear={true}
              value={vm.supplier}
              style={{ width: 368 }}
              placeholder={<T name="请选择供应商" />}
              onChange={supplierChange}
            >
              {vm.supplierList.map((item) => (
                <Select.Option key={item.id} value={item.id}>
                  <Tooltip placement="bottom" title={item.name}>
                    {item.name}
                  </Tooltip>
                </Select.Option>
              ))}
            </Select>
          </ItemWrapper>
          <ItemWrapper name={<T name="账期" />}>
            <Select
              allowClear={true}
              value={vm.billPeriod}
              placeholder={<T name="请选择供账期" />}
              style={{ width: 368 }}
              onChange={periodChange}
            >
              {vm.billPeriodList.map((item) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </ItemWrapper>
          <ItemWrapper name={<T name="对账状态" />}>
            <Select
              allowClear={true}
              value={vm.checkingValue}
              placeholder={<T name="请选择对账状态" />}
              style={{ width: 368 }}
              onChange={checkingStateChange}
            >
              {checkingState.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </ItemWrapper>
          <br />
          <ItemWrapper name={<T name="开票状态" />}>
            <Select
              allowClear={true}
              value={vm.invoiceValue}
              placeholder={<T name="请选择开票状态" />}
              style={{ width: 368 }}
              onChange={invoiceStateChange}
            >
              {invoiceState.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </ItemWrapper>
          <ItemWrapper name={<T name="结算状态" />}>
            <Select
              allowClear={true}
              value={vm.settlementValue}
              placeholder={<T name="请选择结算状态" />}
              style={{ width: 368 }}
              onChange={settlementStateChange}
            >
              {settlementState.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </ItemWrapper>
          <ItemWrapper>
            <Button className="mr-8" category="primary" onClick={handleSearch}>
              {i18n.get('查询')}
            </Button>
            <Button category="secondary" onClick={handleReset}>{i18n.get('重置')}</Button>
          </ItemWrapper>
        </Space>
      </div >
    );
  });
};

interface ItemWrapperProps {
  name?: React.ReactNode;
  className?: string;
}
const ItemWrapper: React.FC<ItemWrapperProps> = ({ name, className, children }) => {
  return (
    <div className={className}>
      <div style={{ height: 22, marginBottom: 8, color: 'var(--eui-text-title)', font: 'var(--eui-font-body-r1)' }}>{name}</div>
      {children}
    </div>
  );
};

export default SettlementBillFilterView;
