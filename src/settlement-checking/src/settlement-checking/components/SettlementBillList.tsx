import React, { useEffect } from 'react';
import { useInstance } from '@ekuaibao/react-ioc';
import { SettlementCheckingListVm } from '../vms/settlement-checking-list.vm';
import { useObserver } from 'mobx-react-lite';
import { Map } from '@ekuaibao/datagrid/lib/types/utils';
import { EnhanceConnect } from '@ekuaibao/store';
import { Table } from '@hose/eui'

// @ts-ignore
import type { ColumnsType } from '@hose/eui/es/table';
interface SettlementBillListProps {
  KA_ZY_Reconciliation_Settlement?: boolean
}

interface SettlementBillListProps { }

export const SettlementBillList: React.FC<SettlementBillListProps> = (props: any) => {
  const { KA_ZY_Reconciliation_Settlement } = props
  const vm = useInstance<SettlementCheckingListVm>(SettlementCheckingListVm.NAME);

  useEffect(() => {
    vm.initData();
  }, []);


  vm.KA_ZY_Reconciliation_Settlement = KA_ZY_Reconciliation_Settlement
  const handlePageChange = (page, pageSize) => {
    vm.currentPage = page;
    vm.pageSize = pageSize;
  };


  const onTableChange = (page, filter, orgSorter, extra) => {
    if (extra.action === 'sort') {
      const key = Array.isArray(orgSorter.field) ? orgSorter.field.join('.') : orgSorter.field;
      const sorter: Map<'ascend' | 'descend'> = {
        [key]: orgSorter.order
      };
      vm.sorters = sorter;
    }

  }

  return useObserver(() => {
    return (
      <div className="list-wrapper">
        <Table
          loading={vm.loading}
          locale={{

            triggerDesc: '点击降序',
            triggerAsc: '点击升序',
            cancelSort: '取消排序',
          }}
          dataSource={vm.dataSource}
          columns={vm.columns}
          onChange={onTableChange}
          pagination={{
            pageSize: vm.pageSize,
            total: vm.dataTotal,
            onChange: handlePageChange,
          }}
          scroll={{ y: 'calc(100vh - 422px)' }}
        />
      </div>
    );
  });
};
export default EnhanceConnect((state: any) => ({
  KA_ZY_Reconciliation_Settlement: state['@common'].powers.KA_ZY_Reconciliation_Settlement,
}))(SettlementBillList);
