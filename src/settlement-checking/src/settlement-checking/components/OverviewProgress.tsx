import React, { FC } from 'react';
// @ts-ignore
import styles from './OverviewProgress.module.less';
import { Tooltip } from '@hose/eui';
import { app } from '@ekuaibao/whispered';
import { get } from 'lodash';
const Money = app.require<any>('@elements/puppet/Money');
import { IStatisticsValue } from '../../types/interface';

type IProps = {
  name?: string;
  data?: IStatisticsValue[];
  total: any;
  type: string
};

type PProps = {
  it: IStatisticsValue;
  total: number;
  statusName: string;
  num: number | string;
};

const ProcessSection: React.FC<PProps> = (props) => {
  const { it, total, statusName, num } = props;
  const ratio = `${Math.round(((num as number) / total) * 100)}%`;
  const style = { width: ratio };
  let statusColor = 'COR_SETTLE_DRAFT';
  if (it.type === 'wait') {
    statusColor = 'COR_SETTLE_DRAFT';
  } else if (it.type === 'waiting') {
    statusColor = 'COR_SETTLE_PENDING';
  } else if (it.type === 'waitEnd') {
    statusColor = 'COR_SETTLE_PAID';
  }
  return (
    <Tooltip
      placement="top"
      arrowPointAtCenter
      autoAdjustOverflow
      overlayClassName={styles['tooltips']}
      title={`${statusName}(${ratio}): ${num}`}
    >
      <div className={`box ${statusColor}`} style={style} />
    </Tooltip>
  );
};

const SettlementProgress: FC<IProps> = (props) => {
  const { data, name, total, type } = props;
  if (!data?.length) {
    return null;
  }

  return (
    <div className={styles['overview-progress-wrapper']}>
      <div className="progress-wrapper">
        {
          type === 'amount' ?
            <Money className="progress-money" value={total} showSymbol={false} /> :
            <div className="progress-money">{total}</div>
        }

        <div className="progress-title">{name}</div>
        <div className="progress-view">
          <>
            {data?.map((it, index) => (
              <ProcessSection
                it={it}
                key={it?.id}
                total={type === 'amount' ? (total?.standard) : total}
                statusName={get(it, 'state')}
                num={type === 'amount' ? Number(get(it, 'num.standard', 0)).toFixed(2) : get(it, 'num', 0)}
              />
            ))}
          </>
        </div>
      </div>
      <div className="content-wrapper">
        {data.map((item: any) => {
          let statusColor = 'COR_SETTLE_DRAFT';
          if (item.type === 'wait') {
            statusColor = 'COR_SETTLE_DRAFT';
          } else if (item.type === 'waiting') {
            statusColor = 'COR_SETTLE_PENDING';
          } else if (item.type === 'waitEnd') {
            statusColor = 'COR_SETTLE_PAID';
          }
          return (
            <div className="content">
              <div className="td-warp">
                <div className="left-v content-status">
                  <div className={`dot ${statusColor}`}></div>
                  <div className="status">{i18n.get(item?.state)}</div>
                </div>
                {
                  type === 'amount' ?
                    <Money className="money" value={item?.num} showSymbol={false} /> :
                    <div className="content-money content-money-font">{item?.num}</div>
                }
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default SettlementProgress;
