@import '~@ekuaibao/eui-styles/less/token';

.hose-checking-fee-detail-table-wrapper {
  flex: 1;
  flex-direction: column;
  display: flex;
  position: relative;
  height: 100%;
  .table-row-error {
    background: #ffebeb;
  }
  .hose-table-toolbar-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    .fee-detail-table-left {
      .fee-detail-table-left-action {
        &-select {
          width: 180px;
        }
      }
    }
    .fee-detail-table-right {
      display: flex;
      align-items: center;
    }
  }
  .operate-icon {
    width: 32px;
    min-width: 32px !important;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--eui-line-border-component);
    border-radius: 6px;
    margin-left: 8px;
    cursor: pointer;
  }
  .dx-widget.ekb-table-inner .dx-datagrid .dx-datagrid-header-panel {
    display: none;
  }
  .action {
    color: rgb(34, 178, 204);
    cursor: pointer;
    margin-right: @space-4;
    text-decoration: none;
  }
}
.hose-fee-detail-table-left-action-select {
  width: 190px;
}

.hose-checking-fee-detail-table-screen{
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 999;
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 20px;
  background: #fff;
  overflow-y: auto;
}
