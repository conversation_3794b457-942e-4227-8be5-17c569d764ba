import React, { useEffect } from 'react';
import { T } from '@ekuaibao/i18n';
import { app } from '@ekuaibao/whispered';
import { KEELVM, IKeel } from '@ekuaibao/keel';
import { useInstance as runInstance } from '@ekuaibao/react-ioc';
import { SettlementCheckingDetailVm } from '../vms/settlement-checking-detail.vm';
import { useObserver } from 'mobx-react-lite';
import { IStatisticsValue } from '../../types/interface';
const Money = app.require<any>('@elements/puppet/Money');

type Props = {
  hideButton?: boolean;
  value: any;
};

export const SettlementStatistics: React.FC<Props> = (props) => {
  const keel: any = !props.hideButton && runInstance<IKeel>(KEELVM);
  const vm = runInstance<SettlementCheckingDetailVm>(SettlementCheckingDetailVm.NAME);
  useEffect(() => {
    if (props?.value?.splitType === 'MACH') {
      vm.getSettlementStatistics(props?.value?.legalEntityId);
    } else {
      vm.getSettlementStatistics();
    }
  }, []);
  const handleDetail = () => {
    keel.open('SettlementStatisticsDetail', { checkingBillId: vm.checkingBill.id });
  };

  const renderItem = (item: IStatisticsValue) => {
    const cl = item.type === 'total' ? 'item-wrapper bc fw-500' : 'item-wrapper item-hover';
    return (
      <div className={cl}>
        <div className="f-1 ta-l">{item.name}</div>
        <div className="dp-f f-1 jc-e">
          <Money value={item.value} />
        </div>
      </div>
    );
  };

  return useObserver(() => (
    <div className="settlement-wrapper">
      <div className="item-wrapper jc-b">
        <div className="fs-16 fw-500">
          <T name="结算概览" />
        </div>
        {!props.hideButton && (
          <div className="check-detail" onClick={handleDetail}>
            <T name="查看详情" />
          </div>
        )}
      </div>
      {vm?.settlementStatisticsData?.map((item: IStatisticsValue) => {
        return renderItem(item);
      })}
    </div>
  ));
};

export default SettlementStatistics;
