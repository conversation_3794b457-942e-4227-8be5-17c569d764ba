import React from 'react';
import { useObserver } from 'mobx-react-lite';
import { Button } from 'antd';
import { T } from '@ekuaibao/i18n';
import { useInstance } from '@ekuaibao/react-ioc';
import { CheckingBillStatisticsDetailVm } from '../vms/checking-bill-statistics-detail.vm';
import { showMessage } from '@ekuaibao/show-util';
import { checkingSaveBill } from '../../setttlement-checkin-action'
import { app } from '@ekuaibao/whispered';
interface CheckingBillStatisticsDetailBottomProps {
  hideButton: boolean
}

export const CheckingBillStatisticsDetailBottom: React.FC<CheckingBillStatisticsDetailBottomProps> = (props) => {

  const vm = useInstance<CheckingBillStatisticsDetailVm>(CheckingBillStatisticsDetailVm.NAME);

  const handleLaunchChecking = async () => {
    try {
      await vm.submitChecking();
      await app.open('@settlement-checkin:SubmitCheckingModal', {
        result: 'PENDING', failureReasons: [], type: 'CHECKING_SUBMIT', checkingBillId: vm.checkingBillInfo.id, action: checkingSaveBill
      })
      vm.cleanSelectedKeys();
      await vm.refreshData();
    } catch (e) {
      showMessage.error(e?.message);
    }
  };

  const handleDelete = async () => {
    try {
      await vm.deleteChecking();
      vm.cleanSelectedKeys();
      showMessage.success(i18n.get('删除成功'));
      await vm.refreshData();
    } catch (e) {
      showMessage.error(e?.message);
    }
  };

  const handleRefresh = async () => {
    const type = 'CHECKING_SUBMIT'
    let res: any = await checkingSaveBill({ type, checkingBillId: vm.checkingBillInfo.id })
    if (!res.value || !['SUCCESS', 'CONFIRMED'].includes(res?.value?.state)) {
      let result = ''
      let failure = []
      if(!res.value) {
        showMessage.success(i18n.get('刷新成功'))
        await vm.refreshData();
        return ''
      }
      if (res?.value?.state === 'PENDING') {
        result = 'FAILURE'
        failure = ['对账单创建中,请稍后再试']
      } else {
        result = res?.value?.state
        failure = res?.value?.failureReasons
      }
      await app.open('@settlement-checkin:SubmitCheckingModal', { result, failureReasons: failure, type, value: res.value })
    }
    await vm.refreshData();
    return '';
  };

  const handleRemind = () => {
    const selectedKeys = vm.isSelectAll ? vm.allFlowIds : vm.selectedKeys
    const result = vm.dataSource.filter(item => selectedKeys.includes(item.id) && item.state === 'approving')
    if(result.length) {
      vm.handleAllRemind(result, selectedKeys)
    }else {
      showMessage.warning(i18n.get('已选中单据当前状态不支持催办'))
    }
  }

  const handleSelectAll = async () => {
    vm.isSelectAll = true
    await app.open('@settlement-checkin:ExportSelectAllModal', {
      vm,
      onDelete: handleDelete,
      onLaunchChecking: handleLaunchChecking,
      onRemind: handleRemind,
    })
  }

  return useObserver(() => (
    <div className="checking-detail-info-bottom-wrapper">
      {!props.hideButton && <Button type="primary" disabled={!vm.selectedKeys.length} onClick={handleLaunchChecking}>
        <T name="发起对账" />
      </Button>}
      {!props.hideButton && <Button disabled={!vm.selectedKeys.length} onClick={handleDelete}>
        <T name="删除" />
      </Button>}
      {!props.hideButton && <Button onClick={handleRefresh}>
        <T name="刷新数据" />
      </Button>}
      {!props.hideButton && <Button disabled={!vm.selectedKeys.length} onClick={handleRemind}>
        <T name="催办" />
      </Button>}
      {
        vm.total ?  <span className={'action color-primary'} onClick={handleSelectAll}>
        {i18n.get('选择全部')}
      </span> : null
      }
    </div >
  ));
};

export default CheckingBillStatisticsDetailBottom;
