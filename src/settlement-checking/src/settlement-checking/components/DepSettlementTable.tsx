/*
 * @Author: <PERSON>
 * @Date: 2022-01-07 11:06:53
 * @LastEditTime: 2023-03-06 15:53:48
 * @LastEditors: Hunter
 * @Description:
 * @FilePath: \plugin-web-settlement-checking\src\settlement-checking\components\DepSettlementTable.tsx
 * 可以输入预定的版权声明、个性签名、空行等
 */

import React, { useEffect, FC } from 'react';
import { useInstance } from '@ekuaibao/react-ioc';
import TableWrapper from '../../components/TableWrapper';
import { PageMode, PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination';
import { useObserver } from 'mobx-react-lite';
import { DepSettlementVm } from '../vms/DepSettlement.vm';
import './DepSettlementTable.less';
import { Button } from 'antd';
import { Map } from '@ekuaibao/datagrid/lib/types/utils';

interface Props {
  [key: string]: any;
}

const DepSettlementTable: FC<Props> = (props) => {
  const vm = useInstance<DepSettlementVm>(DepSettlementVm.NAME);

  useEffect(() => {
    vm.init(props.value);
  }, []);

  const handleSearch = (value: string) => {
    vm.search = value;
    vm.searchValue();
  };

  const handleClear = () => {
    vm.search = '';
    vm.clearPageData();
  };

  const handlePageChange = (pagination: PaginationConfig, pageMode: PageMode) => {
    vm.pageSize = pagination.size;
    vm.currentPage = pagination.current;
    vm.pageMode = pageMode;
    vm.handlePage();
  };

  const refreshData = () => {
    vm.refrshStatus(props.value);
  };
  // 中文排序
  const sorterByZH = (a: string, b: string) => {
    return a.localeCompare(b, 'zh-Hans-CN')
  }
  // 排序
  const onSorterChange = (sorter: Map<'ascend' | 'descend'>) => {
    const key = Reflect.ownKeys(sorter)[0]
    if (key !== 'stateName') return
    const val = sorter[key]
    vm.dataSource = vm.dataSource.sort((a, b) => {
      const _a = a.stateName
      const _b = b.stateName
      return val === 'ascend' ? sorterByZH(_a, _b) : sorterByZH(_b, _a)
    })
    vm.handlePage();
  }
  return useObserver(() => {
    return (
      <div className="checking-fee-detail-table-wrapper">
        <TableWrapper
          tableProps={{
            columns: vm.defaultColumns,
            dataSource: vm.pageData,
            isMultiSelect: false,
            groupPanel: { visible: true, allowColumnDragging: false },
            scrolling: vm.scrolling,
            pageSize: vm.pageSize,
            disabledScroll: true,
            fixedSelect: true,
            sorters: vm.sorters,
            onSorterChange,
            allowColumnResizing:true
          }}
          searchProps={{ placeholder: '请输入部门名称', onSearch: handleSearch, onClear: handleClear }}
          paginationProps={{
            totalLength: vm.total,
            pagination: {
              current: vm.currentPage,
              size: vm.pageSize,
            },
            onChange: handlePageChange,
            pageMode: vm.pageMode,
          }}
        >
          <Button onClick={refreshData} style={{ width: '150px' }}>
            刷新
          </Button>
        </TableWrapper>
      </div>
    );
  });
};

export default DepSettlementTable;
