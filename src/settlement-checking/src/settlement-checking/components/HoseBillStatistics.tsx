import React, { useState } from 'react';
// @ts-ignore
import styles from '../HoseSettlementCheckingDetail.module.less';
import HoseCheckingBillStatistics from './HoseCheckingBillStatistics';
import HoseInvoiceStatistics from './HoseInvoiceStatistics';
import HoseSettlementStatistics from './HoseSettlementStatistics';
import { OutlinedDirectionPullRight } from '@hose/eui-icons'
import { Tooltip } from '@hose/eui'
import classnames from 'classnames'

type Props = {
  hideButton?: boolean;
  hiddenCheckBill: boolean;
  hiddenInvoice: boolean;
  hiddenSettle: boolean;
  value: any
}
export const BillStatistics: React.FC<Props> = (props) => {
  const [statistics, setStatistics] = useState(true)
  const onStatistics = () => {
    setStatistics(!statistics)
  }
  const clsBill = classnames(styles['hose-bill-statistics-wrapper'], {
    [styles['hose-bill-statistics-none']]: !statistics,
  })
  return (
    <div className={clsBill} id="statement-details-guide-first">
      <div className={`hose-bill-statistics ${!statistics ? 'display-none' : ''}`}>
        <HoseCheckingBillStatistics {...props} onStatistics={onStatistics} />
        <HoseInvoiceStatistics {...props} />
        <HoseSettlementStatistics {...props} />
      </div>
      <Tooltip placement="top" title={i18n.get('展开边栏')}>
        <div className={`${statistics ? 'display-none' : 'pull-lose-right'}`} onClick={onStatistics}>
          <OutlinedDirectionPullRight />
        </div>
      </Tooltip>
    </div>
  );
};

export default BillStatistics;
