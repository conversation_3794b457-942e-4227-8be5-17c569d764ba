import React, {useState, useRef} from 'react';
import { T } from '@ekuaibao/i18n';
import { app } from '@ekuaibao/whispered';
import { ICheckingBillStatistics } from '../../types/interface';
import { useObserver } from 'mobx-react-lite';
import { useInstance } from '@ekuaibao/react-ioc';
import { HoseCheckingBillStatisticsDetailVm } from '../vms/hose-checking-bill-statistics-detail.vm';
const ToolBar = app.require<any>('@elements/HoseTable/toolBar');
import OverviewProgress from "./OverviewProgress";
const Money = app.require<any>('@elements/puppet/Money');

interface CheckingBillStatisticsDetailOverviewProps {}
type IDataProps = {
  data: any
  name: string
  total: any
  type: string
}

export const CheckingBillStatisticsDetailOverview: React.FC<CheckingBillStatisticsDetailOverviewProps> = () => {
  const [direction, setDirection] = useState(false)
  const vm = useInstance<HoseCheckingBillStatisticsDetailVm>(HoseCheckingBillStatisticsDetailVm.NAME);
  const actionRef = useRef<HTMLInputElement | null>(null)
  const itemsAmount = parseStatisticsAmountData(vm.statisticsInfo);
  const itemsNum = parseStatisticsNumData(vm.statisticsInfo);

  const RenderTitle = () => {
    return (
      <div className="detail-msg-warp">
          <span className="company-name">{i18n.get('对账概览')}</span>
          <span className="line"></span>
          <div className="info-wrapper">
            <T name="结算金额：" />
            <Money
              className="company-money"
              showSymbol={false}
              value={vm.statisticsInfo?.all?.amount}
            />
          </div>
          <span className="line"></span>
          <div className="info-wrapper">
            <T name="结算数量：" />
            <span className="company-code" >
              {vm.statisticsInfo?.all?.total}
            </span>
          </div>
        </div>
    )
  }
  const RenderData: React.FC<IDataProps> = (props) =>
    <OverviewProgress data={props.data} name={props.name} total={props.total} type={props.type}/>

  return useObserver(() => {
    return (
      <div className="hose-detail-overview">
        <div className="hose-detail-overview-top">
          <ToolBar
            toolBarLeft={{
              title: direction ? <RenderTitle /> : i18n.get('对账概览'),
              onChange: setDirection
            }}
            isLine={true}
            isDirection={true}
            isFullScreen={true}
            actionRef={actionRef}
          />
        </div>
        <div className="hose-detail-overview-wrap" ref={actionRef}>
          <div className="progress-left">
            <RenderData
              name={i18n.get('结算金额')}
              total={vm.statisticsInfo?.all?.amount}
              data={itemsAmount}
              type='amount'
            /></div>
          <div className="line" />
          <div className="progress-right">
            <RenderData
              name={i18n.get('结算数量')}
              total={vm.statisticsInfo?.all?.total}
              data={itemsNum}
              type='num'
            /></div>
        </div>
      </div>
    );
  });
};

const parseStatisticsAmountData= (data?: ICheckingBillStatistics) => {
  return [
    { num: data?.todo?.amount ?? 0, state: i18n.get('待对账'), type: 'wait' },
    { num: data?.doing?.amount ?? 0, state: i18n.get('对账中'), type: 'waiting' },
    { num: data?.done?.amount ?? 0, state: i18n.get('已对账'), type: 'waitEnd' },
  ];
};

const parseStatisticsNumData = (data?: ICheckingBillStatistics) => {
  return [
    { num: data?.todo?.total ?? 0, state: i18n.get('待对账'), type: 'wait' },
    { num: data?.doing?.total ?? 0, state: i18n.get('对账中'), type: 'waiting' },
    { num: data?.done?.total ?? 0, state: i18n.get('已对账'), type: 'waitEnd' },
  ];
};

export default CheckingBillStatisticsDetailOverview;
