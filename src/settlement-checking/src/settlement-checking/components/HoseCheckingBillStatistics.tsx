import React, { useEffect } from 'react';
import { app } from '@ekuaibao/whispered';
import { T } from '@ekuaibao/i18n';
import { useInstance as runInstance} from '@ekuaibao/react-ioc';
import { SettlementCheckingDetailVm } from '../vms/settlement-checking-detail.vm';
import { useObserver } from 'mobx-react-lite';
import { KEELVM } from '@ekuaibao/keel';
import { checkingSaveBill } from '../../setttlement-checkin-action'
import { OutlinedDirectionPullLeft, OutlinedDirectionDetails } from '@hose/eui-icons'
import { Tooltip } from '@hose/eui'

import SettlementProgress from './SettlementProgress'

type Props = {
  hideButton?: boolean;
  value: any
  onStatistics: () => void
}

export const HoseCheckingBillStatistics: React.FC<Props> = (props) => {
  const vm: any = runInstance(SettlementCheckingDetailVm.NAME);
  const keel: any = !props.hideButton && runInstance(KEELVM);
  const { stackerManager } = props
  useEffect(() => {
    vm.getCheckingStatistics(props?.value?.legalEntityId);
  }, []);

  const checkDetail = async () => {
    const type = 'CHECKING_SAVE'
    let res: any = await checkingSaveBill({ type, checkingBillId: vm.checkingBill.id })
    if (res.value && res.value.state === 'FAILURE') {
      await app.open('@settlement-checkin:SubmitCheckingModal', { result: res.value.state, failureReasons: res.value.failureReasons, type: 'CHECKING_SAVE', value: res.value })
    }
    if (stackerManager) {
      stackerManager.push('CheckingBillStatisticsDetail', {
        value: vm.checkingStatisticsOriginData,
        checkingBill: vm.checkingBill,
        hideButton: props.hideButton
      })
    } else {
      keel.open('CheckingBillStatisticsDetail', {
        value: vm.checkingStatisticsOriginData,
        checkingBill: vm.checkingBill,
      });
    }
  }
  return useObserver(() => {
    return (
      <div className="checking-bill-wrapper">
        <div className="item-wrapper jc-b">
          <div className="item-name">
            <T name="对账概览" />
          </div>
          <div className='check-detail-wrap'>
            <span className='check-detail' onClick={checkDetail}>
              <T name="详情" />
              <OutlinedDirectionDetails className='detail-icon' />
            </span>
            <Tooltip placement="top" title={i18n.get('收起边栏')}>
              <div className='pull-lose-left' onClick={props.onStatistics} id="statement-details-guide-second">
                <OutlinedDirectionPullLeft />
              </div>
            </Tooltip>
          </div>
        </div>
        <SettlementProgress data={vm.checkingStatisticsData} name={i18n.get('对账金额')}/>
      </div>
    );
  });
};

export default HoseCheckingBillStatistics;
