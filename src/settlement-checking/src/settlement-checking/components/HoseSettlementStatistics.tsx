import React, { useEffect } from 'react';
import { T } from '@ekuaibao/i18n';
import { KEELVM, IKeel } from '@ekuaibao/keel';
import { useInstance as runInstance } from '@ekuaibao/react-ioc';
import { SettlementCheckingDetailVm } from '../vms/settlement-checking-detail.vm';
import { useObserver } from 'mobx-react-lite';
import SettlementProgress from './SettlementProgress';
import { OutlinedDirectionDetails } from '@hose/eui-icons';
// import detailIcon from "../../images/detail-icon.svg";

type Props = {
  hideButton?: boolean;
  value: any;
};

export const HoseSettlementStatistics: React.FC<Props> = (props) => {
  const keel: any = !props.hideButton && runInstance<IKeel>(KEELVM);
  const vm = runInstance<SettlementCheckingDetailVm>(SettlementCheckingDetailVm.NAME);
  useEffect(() => {
    if (props?.value?.splitType === 'MACH') {
      vm.getSettlementStatistics(props?.value?.legalEntityId);
    } else {
      vm.getSettlementStatistics();
    }
    props.bus.on('checkingBilled:confirmed', onRefresh);
    return () => {
      props.bus.un('checkingBilled:confirmed', onRefresh);
    };
  }, []);

  const onRefresh = () => {
    vm.getSettlementStatistics(props?.value?.legalEntityId);
  };

  const handleDetail = () => {
    keel.open('SettlementStatisticsDetail', { checkingBillId: vm.checkingBill.id });
  };

  return useObserver(() => (
    <div className="checking-bill-wrapper">
      <div className="item-wrapper jc-b">
        <div className="item-name">
          <T name="结算概览" />
        </div>
        {!props.hideButton && (
          <div className="check-detail detail-margin" onClick={handleDetail}>
            <T name="详情" />
            <OutlinedDirectionDetails className="detail-icon" />
          </div>
        )}
      </div>
      <SettlementProgress data={vm.settlementStatisticsData} name={i18n.get('结算金额')} />
    </div>
  ));
};

export default HoseSettlementStatistics;
