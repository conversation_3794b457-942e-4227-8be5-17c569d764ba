import React, { <PERSON> } from 'react';
// @ts-ignore
import styles from './InvoiceProgress.module.less';
import { VictoryPie, VictoryTooltip } from 'victory';
import { app } from '@ekuaibao/whispered';
import { sumBy } from 'lodash';
const Money = app.require<any>('@elements/puppet/Money');

enum ITypes {
  INVOICE_DONE = 'INVOICE_DONE',
  INVOICE_DOING = 'INVOICE_DOING',
  INVOICE_TODO = 'INVOICE_TODO',
}

type IProps = {
  columns?: { key: string }[];
  dataSource?: ({ key: string } | undefined)[];
  table?: { key: string }[];
};

type ChartType = {
  x: string;
  y: number;
  z: number;
  ratio: string;
  type: string;
};

const InvoiceProgress: FC<IProps> = (props) => {
  const { columns, dataSource = [], table } = props;

  const dataChart: ChartType[] = [];
  const data = dataSource?.map((d: any) => {
    const moneyObj = table?.[d?.id] || 0;
    d.money = moneyObj;
    d.totalMoney = getTotalMoney(moneyObj);
    return d;
  });
  const total = sumBy(data, function (o: { totalMoney: number }) {
    return o?.totalMoney;
  });
  data.forEach((item: any) => {
    dataChart.push({
      x: item.name,
      y: total === 0 ? (item.id === ITypes.INVOICE_TODO ? 100 : 0) : item.totalMoney / total,
      z: item.totalMoney,
      ratio: `${Math.round((item.totalMoney / total) * 100)}%`,
      type: item.id,
    });
  });
  return (
    <div className={styles['invoice-progress-wrapper']}>
      <div className="progress-wrapper">
        <div className="progress-view">
          <div className="total-money">
            <Money className="money-total" showSymbol={false} value={total} />
            <div className="subtitle">{i18n.get('开票总金额')}</div>
          </div>
          <VictoryPie
            innerRadius={190}
            radius={165}
            data={dataChart}
            events={[
              {
                target: 'data',
                eventHandlers: {
                  onMouseEnter: (q) => {
                    return [
                      {
                        mutation: (props) => ({ innerRadius: 190, radius: 165 }),
                      },
                    ];
                  },
                  onMouseLeave: (q) => {
                    return [
                      {
                        mutation: (props) => ({ innerRadius: 190, radius: 165 }),
                      },
                    ];
                  },
                },
              },
            ]}
            animate={{
              duration: 2000,
            }}
            labels={({ datum }) =>
              total === 0 ? `开票总金额(0%): 0.00` : `${datum.x}(${datum.ratio}): ${Number(datum?.z)?.toFixed(2)}`
            }
            labelComponent={
              <VictoryTooltip
                constrainToVisibleArea
                cornerRadius={8}
                style={{ fill: 'var(--eui-text-white)', font: 'var(--eui-font-body-r1)', fontSize: 32 }}
                flyoutStyle={{
                  stroke: 'var(--eui-text-title)',
                  fill: 'var(--eui-text-title)',
                  strokeWidth: 1,
                }}
                flyoutPadding={{ top: 15, bottom: 15, left: 50, right: 50 }}
              />
            }
            style={{
              data: {
                fill: (item) => {
                  let color = 'var(--eui-bg-base)';
                  if (item?.datum?.type === ITypes.INVOICE_TODO) {
                    color = 'var(--eui-function-info-400)';
                  } else if (item?.datum?.type === ITypes.INVOICE_DOING) {
                    color = 'var(--eui-function-warning-400)';
                  } else if (item?.datum?.type === ITypes.INVOICE_DONE) {
                    color = 'var(--eui-function-success-400)';
                  }
                  if (total === 0) {
                    color = 'var(--eui-bg-base)';
                  }
                  return color;
                },
              },
            }}
          />
        </div>
      </div>
      <div className="content-wrapper">
        <tr className="content content-border-bottom">
          <th className="content-status content-th-font">{i18n.get('状态')}</th>
          <th className="content-th-font">{i18n.get('金额')}</th>
        </tr>
        {data?.map((item: any) => {
          let statusColor = 'DRAFT';
          if (item.id === ITypes.INVOICE_TODO) {
            statusColor = 'DRAFT';
          } else if (item.id === ITypes.INVOICE_DOING) {
            statusColor = 'PAYING';
          } else if (item.id === ITypes.INVOICE_DONE) {
            statusColor = 'PAID';
          }
          return (
            <>
              <tr
                className={`content ${
                  item.id === ITypes.INVOICE_DOING || item.id === ITypes.INVOICE_DONE ? 'content-m-t' : ''
                }`}
              >
                <td className="td-warp">
                  <div className="left-v content-status">
                    <div className={`dot ${statusColor}`}></div>
                    <div className="status">{item.name}</div>
                  </div>
                </td>
                <td>
                  <Money className="money" value={item.totalMoney} showSymbol={false} />
                </td>
              </tr>
              {columns?.map((c: any) => {
                return (
                  <tr className="content">
                    <td className="td-warp">
                      <div className="left-v">
                        <div className={`dot WHITE`}></div>
                        <div className="status-sub">{c.name}</div>
                      </div>
                    </td>
                    <td>
                      <Money
                        className="money"
                        value={item?.money ? item?.money?.[c?.id] : item?.money}
                        showSymbol={false}
                      />
                    </td>
                  </tr>
                );
              })}
            </>
          );
        })}
      </div>
    </div>
  );
};

function getTotalMoney<T>(moneyObj: T): number {
  if (moneyObj && typeof moneyObj === 'object') {
    const moneyArr = Object.values(moneyObj as T);
    const sumprice = moneyArr.reduce((total: number, currentValue: number) => {
      return total + Number(currentValue);
    }, 0);
    return sumprice;
  }
  return 0;
}

export default InvoiceProgress;
