@import '~@ekuaibao/eui-styles/less/token.less';

.checking-detail-tab-wrapper {
  display: flex;
  //flex: 1;
  min-height: 700px;
  flex-direction: column;
  background-color: #f7f7f7;
  overflow-y: auto;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
  border-radius: @radius-2;
  .checking-detail-tab {
    background-color: @color-white-1;
    .ant-tabs-bar {
      width: 100%;
      padding-left: @space-7;
      .ant-tabs-nav-scroll {
        justify-content: flex-start;
      }
    }
  }
}

.checking-detail-tab-wrapper-single {
  padding: 16px 24px 8px 24px;
  background-color: @color-white-1;
  .checking-detail-tab-wrapper-title {
    .font-size-2;
    margin-bottom: @space-6;
    display: flex;
    justify-items: center;
    font-weight: 500;
  }
  .checking-fee-detail-table-wrapper {
    margin: 0;
  }
}
