/*
 * @Author: <PERSON>
 * @Date: 2022-01-07 11:06:53
 * @LastEditTime: 2023-03-06 15:53:48
 * @LastEditors: Hunter
 * @Description:
 * @FilePath: \plugin-web-settlement-checking\src\settlement-checking\components\DepSettlementTable.tsx
 * 可以输入预定的版权声明、个性签名、空行等
 */

import React, {useEffect, FC, useState} from 'react';
import { useInstance } from '@ekuaibao/react-ioc';
import HoseTableWrapper from '../../components/HoseTableWrapper';
import { useObserver } from 'mobx-react-lite';
import { DepSettlementVm } from '../vms/DepSettlement.vm';
import './DepSettlementTable.less';
import { Button, Tooltip } from '@hose/eui';
import { Map } from '@ekuaibao/datagrid/lib/types/utils';
import {OutlinedDirectionRefresh} from "@hose/eui-icons";

interface Props {
  [key: string]: any;
}

const HoseDepSettlementTable: FC<Props> = (props) => {
  const [isFull, setIsFull] = useState(false);
  const vm = useInstance<DepSettlementVm>(DepSettlementVm.NAME);

  useEffect(() => {
    vm.init(props.value);
  }, []);

  const handleFullScreen = () => {
    setIsFull(!isFull)
  }

  const handleSearch = (value: string) => {
    vm.search = value;
    vm.searchValue();
  };

  const handleClear = () => {
    vm.search = '';
    vm.clearPageData();
  };

  const handlePageChange = (page: number, pageSize: number) => {
    vm.pageSize = pageSize;
    vm.currentPage = page;
    vm.handlePage();
  };

  const refreshData = () => {
    vm.refrshStatus(props.value);
  };
  // 中文排序
  const sorterByZH = (a: string, b: string) => {
    return a.localeCompare(b, 'zh-Hans-CN')
  }
  // 排序
  const onSorterChange = (sorter: Map<'ascend' | 'descend'>) => {
    const key = Reflect.ownKeys(sorter)[0]
    if (key !== 'stateName') return
    const val = sorter[key]
    vm.dataSource = vm.dataSource.sort((a, b) => {
      const _a = a.stateName
      const _b = b.stateName
      return val === 'ascend' ? sorterByZH(_a, _b) : sorterByZH(_b, _a)
    })
    vm.handlePage();
  }
  const onTableChange = (pagination, filters, sorter) => {
    const sorterStr = JSON.stringify(sorter)
    const vmSorterStr = JSON.stringify(vm.sorters)
    if(JSON.stringify(sorter) !== '{}' && sorterStr !== vmSorterStr) {
      let key: string = sorter['columnKey']
      onSorterChange({[key]: sorter?.order})
    }
  }
  return useObserver(() => {
    return (
      <div className={`hose-checking-fee-detail-table-wrapper ${isFull ? 'hose-checking-fee-detail-table-screen' : ''}`}>
        <HoseTableWrapper
          tableProps={{
            columns: vm.defaultColumns,
            dataSource: vm.pageData,
            isMultiSelect: false,
            groupPanel: { visible: true, allowColumnDragging: false },
            scrolling: vm.scrolling,
            pageSize: vm.pageSize,
            disabledScroll: true,
            fixedSelect: true,
            sorters: vm.sorters,
            onChange: onTableChange,
            isFull,
            handleFullScreen
          }}
          searchProps={{ placeholder: '请输入部门名称', onSearch: handleSearch, onClear: handleClear }}
          paginationProps={{
            total: vm.total,
            current: vm.currentPage,
            pageSize: vm.pageSize,
            onChange: handlePageChange,
            showTotal: (total: number) => {i18n.get(`共{__k0}条`, { __k0: total })}
          }}
          tableRightAction={
            <Tooltip placement={isFull ? 'left' : 'top'} title={i18n.get('刷新')} >
              <Button onClick={refreshData} category="secondary" className='operate-icon' ><OutlinedDirectionRefresh fontSize={16} /></Button>
            </Tooltip>
          }
        >
        </HoseTableWrapper>
      </div>
    );
  });
};

export default HoseDepSettlementTable;
