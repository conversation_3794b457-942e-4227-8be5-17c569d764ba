import React, { FC } from 'react';
// @ts-ignore
import styles from './SettlementProgress.module.less';
import { Tooltip } from '@hose/eui';
import { app } from '@ekuaibao/whispered';
import { get } from 'lodash';
const Money = app.require<any>('@elements/puppet/Money');
import { IStatisticsValue } from '../../types/interface';

type IProps = {
  name?: string;
  data?: IStatisticsValue[];
};

type PProps = {
  it: IStatisticsValue;
  total: number;
  statusName: string;
  num: number | string;
};

const ProcessSection: React.FC<PProps> = (props) => {
  const { it, total, statusName, num } = props;
  const ratio = `${Math.round(((num as number) / total) * 100)}%`;
  const style = { width: ratio };
  let statusColor = 'COR_SETTLE_DRAFT';
  if (it.type === 'wait') {
    statusColor = 'COR_SETTLE_DRAFT';
  } else if (it.type === 'waiting') {
    statusColor = 'COR_SETTLE_PENDING';
  } else if (it.type === 'waitEnd') {
    statusColor = 'COR_SETTLE_PAID';
  }
  return (
    <Tooltip
      placement="top"
      arrowPointAtCenter
      autoAdjustOverflow
      overlayClassName={styles['tooltips']}
      title={`${statusName}(${ratio}): ${num}`}
    >
      <div className={`box ${statusColor}`} style={style} />
    </Tooltip>
  );
};

const SettlementProgress: FC<IProps> = (props) => {
  const { data, name } = props;
  if (!data?.length) {
    return null;
  }
  const [firstData, ...otherData] = data as IStatisticsValue[];
  return (
    <div className={styles['receive-progress-wrapper']}>
      <div className="progress-wrapper">
        <Money className="progress-money" value={firstData?.value} showSymbol={false} />
        <div className="progress-title">{name}</div>
        <div className="progress-view">
          <>
            {otherData?.map((it, index) => (
              <ProcessSection
                it={it}
                key={it?.id}
                total={get(firstData, 'value.standard', 0)}
                statusName={get(it, 'state')}
                num={Number(get(it, 'value.standard', 0)).toFixed(2)}
              />
            ))}
          </>
        </div>
      </div>
      <div className="progress-wrapper">
        <div className="progress-money">{firstData?.num}</div>
        <div className="progress-title">{firstData?.state}</div>
        <div className="progress-view">
          <>
            {otherData?.map((it, index) => (
              <ProcessSection
                it={it}
                key={it?.id}
                total={firstData?.num}
                statusName={get(it, 'state')}
                num={get(it, 'num', 0)}
              />
            ))}
          </>
        </div>
      </div>
      <div className="content-wrapper">
        <tr className="content content-border-bottom">
          <th>
            <span className="content-status content-th-font">{i18n.get('状态')}</span>
            <span className="content-money content-th-font">{i18n.get('数量')}</span>
          </th>
          <th className="content-th-font">{i18n.get('金额')}</th>
        </tr>
        {otherData.map((item: any) => {
          let statusColor = 'COR_SETTLE_DRAFT';
          if (item.type === 'wait') {
            statusColor = 'COR_SETTLE_DRAFT';
          } else if (item.type === 'waiting') {
            statusColor = 'COR_SETTLE_PENDING';
          } else if (item.type === 'waitEnd') {
            statusColor = 'COR_SETTLE_PAID';
          }
          return (
            <tr className="content">
              <td className="td-warp">
                <div className="left-v content-status">
                  <div className={`dot ${statusColor}`}></div>
                  <div className="status">{i18n.get(item?.state)}</div>
                </div>
                <span className="content-money content-money-font">{item?.num}</span>
              </td>
              <td>
                <Money className="money" value={item?.value} showSymbol={false} />
              </td>
            </tr>
          );
        })}
      </div>
    </div>
  );
};

export default SettlementProgress;
