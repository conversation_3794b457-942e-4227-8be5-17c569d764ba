@import '~@ekuaibao/eui-styles/less/token';

.checking-fee-detail-table-wrapper {
  //width: 100%;
  margin: @space-6;
  flex: 1;
  flex-direction: column;
  display: flex;
  position: relative;
  .table-row-error {
    background: #ffebeb;
  }
  .dx-widget.ekb-table-inner .dx-datagrid .dx-datagrid-header-panel .dx-toolbar .dx-toolbar-items-container {
    position: relative;
    left: 260px;
    border: none;
  }
  .action {
    color: rgb(34, 178, 204);
    cursor: pointer;
    margin-right: @space-4;
    text-decoration: none;
  }
  .fee-detail-table-search-wrapper {
    right: 216px !important;
    top: -8px !important;
  }
  .fee-detail-table-search-wrapper-right {
    right: 115px !important;
  }
  .fee-detail-table-export-btn {
    position: absolute;
    top: 2px;
    background: #f7f7f7;
    border-radius: 3px;
    opacity: 0.76;
    font-size: 14px;
    color: #142234;
    right: 145px;
    width: 60px;
  }
  .fee-detail-table-export-btn-right {
    right: 45px;
  }
  .fee-detail-table-column-chooser {
    position: absolute;
    width: 32px;
    height: 32px;
    top: 2px;
    right: 102px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #f7f7f7;
    border-radius: @space-2;
    > div {
      margin-top: 2px;
    }
  }
  .fee-detail-table-left-action {
    position: absolute;
    left: 0;
    display: flex;
    top: 3px;
    .fee-detail-table-left-action-select {
      width: 180px;
    }
  }
  .fee-detail-table-right-action {
    position: absolute;
    width: 90px;
    height: 32px;
    top: 2px;
    right: 0px;
    display: flex;
    .right-action {
      margin-right: @space-4;
    }
  }
  .fee-detail-table-column-chooser-right {
    right: 0px;
  }
}
