import React, { useEffect, useState } from 'react';
import styles from '../HoseSettlementCheckingDetail.module.less';
import { useInstance } from '@ekuaibao/react-ioc';
import { app } from '@ekuaibao/whispered';
const HoseTable = app.require<any>('@elements/HoseTable');
import { SettlementCheckingDetailVm } from '../vms/settlement-checking-detail.vm';
import { useObserver } from 'mobx-react-lite';
import { ICheckingBill } from '@ekuaibao/ekuaibao_types';
import classnames from "classnames";
interface FeeTypeStatisticsProps {
  value: ICheckingBill;
  searchApportions: boolean;
  depParams?: any
}
export const FeeTypeStatistics: React.FC<FeeTypeStatisticsProps> = ({ value, searchApportions, depParams }) => {
  const vm = useInstance(SettlementCheckingDetailVm.NAME);
  const [isFull, setIsFull] = useState(false)
  useEffect(() => {
    const apportionsLegalEntityId = value?.legalEntityObj?.id || '';
    // 如果有 法人实体 id 传 id
    vm.getFeeTypeStatistics(value?.legalEntityId, apportionsLegalEntityId, depParams);
  }, [depParams]);
  const handleFullScreen = () => {
    setIsFull(!isFull)
  }
  const cls = classnames(styles['hose-feetype-statistics-wrapper'], {
    [styles['hose-feetype-statistics-screen']]: isFull,
  })
  return useObserver(() => {
    return (
      <div className={cls}>
        <HoseTable
          toolBarLeft={{
            title: i18n.get('费用概览')
          }}
          scroll={{ y: isFull ? 'calc(100vh - 200px)' : 200 }}
          isFull={isFull}
          handleFullScreen={handleFullScreen}
          isDirection={true}
          bordered
          size="middle"
          columns={vm.feeTypeStatisticsColumns}
          pagination={false}
          dataSource={vm.feeTypeStatisticsDatasource}
        />
      </div>
    );
  });
};

export default FeeTypeStatistics;
