import React, { <PERSON> } from 'react';
// @ts-ignore
import styles from './HoseCheckingCommonHeader.module.less';
import { useInstance } from '@ekuaibao/react-ioc';
import { KEELVM } from '@ekuaibao/keel';
import { Breadcrumb } from '@hose/eui';

const CheckingCommonHeader: FC<any> = () => {
  const keel: any = useInstance(KEELVM);
  const dataset = keel.dataset.values()
  return (
    <Breadcrumb>
      {dataset.map((item: any, index: number) => {
        return (
          <Breadcrumb.Item
            onClick={() => {
              keel?.closeTo(index);
            }}
          >
            {item.title}
          </Breadcrumb.Item>
        );
      })}
    </Breadcrumb>
  );
};

export default CheckingCommonHeader;
