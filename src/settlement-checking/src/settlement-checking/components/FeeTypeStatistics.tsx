import React, { useEffect } from 'react';
import styles from '../SettlementCheckingDetail.module.less';
import { useInstance } from '@ekuaibao/react-ioc';
import { Table } from 'antd';
import { SettlementCheckingDetailVm } from '../vms/settlement-checking-detail.vm';
import { useObserver } from 'mobx-react-lite';
import { ICheckingBill } from '@ekuaibao/ekuaibao_types';
interface FeeTypeStatisticsProps {
  value: ICheckingBill;
  searchApportions: boolean;
  depParams?: any
}
export const FeeTypeStatistics: React.FC<FeeTypeStatisticsProps> = ({ value, searchApportions, depParams }) => {
  const vm = useInstance(SettlementCheckingDetailVm.NAME);
  useEffect(() => {
    const apportionsLegalEntityId = value?.legalEntityObj?.id || '';
    // 如果有 法人实体 id 传 id
    vm.getFeeTypeStatistics(value?.legalEntityId, apportionsLegalEntityId,depParams);
  }, [depParams]);
  const rowClassName = (record, index) => {
    return index % 2 === 0 ? 'bg-white' : 'bg-gray';
  };
  const components = {
    header: {
      cell: TableHeader,
    },
  };
  return useObserver(() => {
    const height = vm.feeTypeStatisticsDatasource.length ? (vm.feeTypeStatisticsDatasource.length + 1) * 40 : 80;
    return (
      <div className={styles['feetype-statistics-wrapper']} style={{ minHeight: height }}>
        <Table
          bordered
          size="middle"
          components={components}
          columns={vm.feeTypeStatisticsColumns}
          pagination={false}
          dataSource={vm.feeTypeStatisticsDatasource}
          rowClassName={rowClassName}
        />
      </div>
    );
  });
};

const TableHeader = (props) => {
  const { className, ...others } = props;
  return <th {...others} className={`${className} bg-gray column-title`} />;
};

export default FeeTypeStatistics;
