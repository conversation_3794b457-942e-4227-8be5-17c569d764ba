/**
 *  Created by pw on 2021/6/9 下午1:00.
 */
import React, { useEffect } from 'react';
import './HoseFeeDetailWrapper.less';
import { app } from '@ekuaibao/whispered';
import HoseFeeDetailTable from './HoseFeeDetailTable';
import HoseDepSettlementTable from './HoseDepSettlementTable';
import { CheckingDetailTab } from '../../types/enums';
import { ICheckingBill } from '@ekuaibao/ekuaibao_types';
import { useInstance } from '@ekuaibao/react-ioc';
import { HoseFeeDetailVm } from '../vms/HoseFeeDetail.vm';
import {HoseCheckingDetailBottom, HoseUnCheckingDetailBottom} from "./HoseCheckingDetailBottom";
const ETabs = app.require<any>('@elements/ETabs');

interface Props {
  value: ICheckingBill;
  settlementManager?: boolean;
  isApportions: boolean;
  sourceType?: string;
}

const HoseFeeDetailWrapper: React.FC<Props> = ({ value, settlementManager, isApportions, sourceType }) => {
  const vm = useInstance<HoseFeeDetailVm>(HoseFeeDetailVm.NAME);
  useEffect(() => {
    vm.init(value, false, sourceType);
  }, []);
  const isAPI = value?.supplierArchiveType === 'HOSE_TRIP' && value?.importMethod === 'api';
  const renderTabs = () => {
    const legalEntityName = value?.legalEntityObj?.name || '';
    let tabs = settlementManager
      ? [
          {
            key: CheckingDetailTab.DEP_CHECKING_DETAIL,
            tab: i18n.get('部门对账概览'),
            children: <HoseDepSettlementTable type={CheckingDetailTab.DEP_CHECKING_DETAIL} value={value} />,
          },
        ]
      : [];

    tabs.push({
      key: CheckingDetailTab.FEETDETAIL,
      tab: i18n.get('费用明细'),
      children: (
        <HoseFeeDetailTable
          type={CheckingDetailTab.FEETDETAIL}
          footerAction={!(value as any)?.hideButton && <HoseCheckingDetailBottom />}
          settlementManager={settlementManager}
          value={{ ...value }}
          isApportions={isApportions}
          legalEntityName={legalEntityName}
          sourceType={sourceType}
        />
      ),
    });
    if (value?.settlementType !== 'CREDIT') {
      tabs.push({
        key: CheckingDetailTab.NO_CHECKING_DETAIL,
        tab: i18n.get('本期不对账'),
        children: (
          <HoseFeeDetailTable
            type={CheckingDetailTab.NO_CHECKING_DETAIL}
            footerAction={!(value as any)?.hideButton && !isAPI && <HoseUnCheckingDetailBottom />}
            settlementManager={settlementManager}
            value={value}
            isApportions={isApportions}
            legalEntityName={legalEntityName}
          />
        ),
      });
    }
    return tabs;
  };
  const handleTabChange = (key) => {
    vm.selectedKeys = [];
    vm.dataSource = [];
    vm.tabType = key;
  };
  const dataSource = renderTabs();
  if (dataSource.length === 1) {
    const tab = dataSource[0];
    return (
      <div className="hose-checking-detail-tab-wrapper hose-checking-detail-tab-wrapper-single">
        <div className="hose-checking-detail-tab-wrapper-title">{tab.tab}</div>
        {tab.children}
      </div>
    );
  }
  return (
    <div className="hose-checking-detail-tab-wrapper hose-checking-detail-tab-wrapper-single">
      <ETabs
        isHoseEUI={true}
        type="card"
        size="middle"
        className={'checking-detail-tab'}
        defaultActiveKey={CheckingDetailTab.FEETDETAIL}
        onChange={handleTabChange}
        dataSource={dataSource}
      />
    </div>
  );
};

export default HoseFeeDetailWrapper;
