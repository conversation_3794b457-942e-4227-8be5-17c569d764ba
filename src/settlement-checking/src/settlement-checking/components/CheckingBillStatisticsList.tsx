import React, { useEffect, useState, useRef } from 'react';
import { useInstance } from '@ekuaibao/react-ioc';
import { CheckingBillStatisticsDetailVm } from '../vms/checking-bill-statistics-detail.vm';
import TableWrapper from '../../components/TableWrapper';
import { useObserver } from 'mobx-react-lite';
import { Map } from '@ekuaibao/datagrid/lib/types/utils';
import { Data } from '@ekuaibao/datagrid/lib/types/dataSource';
import { PageMode, PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination';
import classNames from 'classnames';
import { showMessage } from '@ekuaibao/show-util';
import { app } from '@ekuaibao/whispered';
interface CheckingBillStatisticsListProps {}

export const CheckingBillStatisticsList: React.FC<CheckingBillStatisticsListProps> = () => {
  const vm = useInstance<CheckingBillStatisticsDetailVm>(CheckingBillStatisticsDetailVm.NAME);
  const [isVisibleSaveDiff, setIsVisibleSaveDiff] = useState(false);
  const userInfo = app.getState('@common.userinfo')
  const _instance = useRef(null)
  const handleSorterChange = (sorter: Map<'ascend' | 'descend'>) => {
    vm.sorters = sorter;
  };

  const handleFilterChange = (filter: Map<any>) => {
    vm.filters = filter;
  };

  const handleSelectedChange = (selectedKeys: string[], selectedRowsData: Map<Data>) => {
    vm.selectedKeys = selectedKeys;
  };
  const handleColumnChooseChange = (visibleColumns: string[]) => {
    vm.updateColumns(visibleColumns);
  };
  const handleColumnReset = () => {
    vm.resetColumns();
  };

  useEffect(() => {
    vm.bus.watch('get:column:checked:value', getColumns);
    vm.getScene();
    return () => {
      vm.bus.un('get:column:checked:value', getColumns);
    };
  }, []);

  function getColumns() {
    return vm.columns.map((v: any) => v.dataIndex).filter((v) => v);
  }

  const handleSearch = (value: string) => {
    vm.search = value;
  };

  const handleClear = () => {
    vm.search = '';
  };

  const handlePageChange = (pagination: PaginationConfig, pageMode: PageMode) => {
    vm.pageSize = pagination.size;

    localStorage.setItem(`${userInfo.staff?.id}CheckingOverview`, pagination.size)
    vm.currentPage = pagination.current;
    vm.pageMode = pageMode;
  };

  const refreshState = (obj: any) => {
    setIsVisibleSaveDiff(obj);
    if (obj.hasOwnProperty('isVisibleSaveDiff')) {
      setIsVisibleSaveDiff(obj['isVisibleSaveDiff']);
    }
  };

  const getInstance = (instance: any) => {
    _instance.current = instance;
  };

  const onSaveDiffScenes = () => {
    if (_instance) {
      let visibleColumns = _instance?.current?.getVisibleColumns() ?? [];
      visibleColumns =
        visibleColumns.length > 0 && visibleColumns.filter((d: any) => d.dataField).map((d: any) => d.dataField);
      vm.saveScene(visibleColumns)
        .then(() => {
          showMessage.success(i18n.get('保存成功'));
          setIsVisibleSaveDiff(false);
        })
        .catch((err: any) => {
          showMessage.error(err.message);
        });
    }
  };
  console.log(vm.dataSource, 'dataSourcedataSource222')
  return useObserver(() => {
    return (
      <div className="checking-detail-list">
        <TableWrapper
          exportProps={{
            onClick: vm.onExport,
          }}
          tableProps={{
            dataSource: vm.dataSource,
            columns: vm.columns,
            filters: vm.filters,
            scrolling: vm.scrolling,
            pageSize: vm.dataSource.length,
            groupPanel: { visible: true, allowColumnDragging: false },
            selectedRowKeys: vm.selectedKeys,
            onSelectedChange: handleSelectedChange,
            onSorterChange: handleSorterChange,
            onFilterChange: handleFilterChange,
            disabledScroll: true,
            fixedSelect: true,
            getInstance: getInstance,
          }}
          columnChooserProps={{
            defaultVisibleColumns: vm.columns.map((item) => item.dataIndex).filter((item) => !!item),
            columns: vm.allColumns,
            onChange: handleColumnChooseChange,
            onReset: handleColumnReset,
            refreshState: (params: any) => refreshState(params),
          }}
          searchProps={{ placeholder: '请搜索单据名称或单号', onSearch: handleSearch, onClear: handleClear }}
          columnChooserClassName={'checking-detail-list-column-chooser'}
          searchClass={'checking-detail-list-search'}
          paginationProps={{
            totalLength: vm.total,
            pagination: {
              current: vm.currentPage,
              size: localStorage.getItem(`${userInfo.staff?.id}CheckingOverview`) ? Number(localStorage.getItem(`${userInfo.staff?.id}CheckingOverview`)) : vm.pageSize,
            },
            onChange: handlePageChange,
            pageMode: vm.pageMode,
          }}
          sceneClassName={'checking-detail-list-scene'}
          containerClassName={classNames('checking-detail-list-table-container')}
          bodyStyle={{ 'border-top': 'unset' }}
        />
        {isVisibleSaveDiff && (
          <div
            style={{
              position: 'absolute',
              top: '6px',
              right: '390px',
              height: '52px',
              fontSize: '14px',
              color: 'var(--brand-base)',
              cursor: 'pointer',
              lineHeight: '52px',
            }}
            onClick={() => {
              onSaveDiffScenes();
            }}
          >
            {i18n.get('保存变更')}
          </div>
        )}
      </div>
    );
  });
};

export default CheckingBillStatisticsList;
