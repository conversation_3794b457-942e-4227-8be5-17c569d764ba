import React, { FC } from 'react';
// @ts-ignore
import styles from './CheckingCommonHeader.module.less';
import { app } from '@ekuaibao/whispered';
import { T } from '@ekuaibao/i18n';
const EKBIcon = app.require<any>('@elements/ekbIcon');
import { useInstance } from '@ekuaibao/react-ioc';
import { KEELVM } from '@ekuaibao/keel';
const CheckingCommonHeader: FC<any> = (props) => {
  const { actionButton, title } = props;
  const keel: any = useInstance(KEELVM);
  return (
    <div className={styles['checking-common-header']}>
      <div className="title">
        <EKBIcon
          name="#EDico-APP-back"
          className="icc"
          style={{ marginRight: 4 }}
          onClick={() => {
            keel?.closeTo(keel.dataset.length - keel.current);
          }}
        />
        <T name={title} />
      </div>
      <div className="action">{actionButton}</div>
    </div>
  );
};

export default CheckingCommonHeader;
