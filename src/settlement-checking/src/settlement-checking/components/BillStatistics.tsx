import React from 'react';
// @ts-ignore
import styles from '../SettlementCheckingDetail.module.less';
import { Col, Row } from 'antd';
import CheckingBillStatistics from './CheckingBillStatistics';
import InvoiceStatistics from './InvoiceStatistics';
import SettlementStatistics from './SettlementStatistics';

type Props = {
  hideButton?: boolean;
  hiddenCheckBill: boolean;
  hiddenInvoice: boolean;
  hiddenSettle: boolean;
  value: any
}
export const BillStatistics: React.FC<Props> = (props) => {
  const { hideButton,
    hiddenCheckBill, // 对账概览
    hiddenInvoice, // 发票概览
    hiddenSettle, // 结算概览
  } = props

  const getSpan = (type: string) => {
    const hiddenItemMap = {
      hiddenCheckBill,
      hiddenInvoice,
      hiddenSettle
    }

    const hiddenItemLength = Object.values(hiddenItemMap).filter(item => !item).length

    if (type === 'checkBill' || type === 'settle') {
      return hiddenItemLength === 1 ? 24 : 6 * (4 - hiddenItemLength)
    }

    if (type === 'invoice') {
      return hiddenItemLength === 1 ? 24 : 12
    }
  }

  return (
    <div className={styles['bill-statistics-wrapper']}>
      {hideButton ? <Row gutter={{ xs: 16, sm: 16, md: 16 }} style={{ minWidth: 870 }}>
        {!hiddenCheckBill && <Col span={getSpan('checkBill')}>
          <CheckingBillStatistics {...props} />
        </Col>}
        {!hiddenInvoice && <Col span={getSpan('invoice')}>
          <InvoiceStatistics {...props} />
        </Col>}
        {!hiddenSettle && <Col span={getSpan('settle')}>
          <SettlementStatistics {...props} />
        </Col>}
      </Row> :
        <Row gutter={{ xs: 16, sm: 16, md: 16 }} style={{ minWidth: 870 }}>
          <Col span={6}>
            <CheckingBillStatistics {...props} />
          </Col>
          <Col span={12}>
            <InvoiceStatistics {...props} />
          </Col>
          <Col span={6}>
            <SettlementStatistics {...props} />
          </Col>
        </Row>}
    </div>
  );
};

export default BillStatistics;
