import React from 'react';
import { useObserver } from 'mobx-react-lite';
import { <PERSON><PERSON>, Modal } from '@hose/eui';
import { T } from '@ekuaibao/i18n';
import { useInstance } from '@ekuaibao/react-ioc';
import { HoseCheckingBillStatisticsDetailVm } from '../vms/hose-checking-bill-statistics-detail.vm';
import { showMessage } from '@ekuaibao/show-util';
import { checkingSaveBill } from '../../setttlement-checkin-action'
import { OutlinedGeneralBell } from "@hose/eui-icons";
import { app } from '@ekuaibao/whispered';
interface HoseCheckingBillStatisticsDetailBottomProps {
  hideButton?: boolean
  exportProps: {
    onClick: () => void
  }
}

export const HoseCheckingBillStatisticsDetailBottom: React.FC<HoseCheckingBillStatisticsDetailBottomProps> = (props) => {

  const vm = useInstance<HoseCheckingBillStatisticsDetailVm>(HoseCheckingBillStatisticsDetailVm.NAME);

  const handleLaunchChecking = async () => {
    try {
      await vm.submitChecking();
      await app.open('@settlement-checkin:SubmitCheckingModal', {
        result: 'PENDING', failureReasons: [], type: 'CHECKING_SUBMIT', checkingBillId: vm.checkingBillInfo.id, action: checkingSaveBill
      })
      vm.cleanSelectedKeys();
      await vm.refreshData();
    } catch (e) {
      showMessage.error(e?.message);
    }
  };

  const handleDelete = async () => {
    try {
      const result = await vm.deleteChecking();
      if(result.items.length == 0){
        showMessage.success('删除成功');
      }else{
        showToast(
          `仅支持删除处于「待提交」或「驳回」状态的对账单。${
            (vm.selectedKeys.length || vm.total) - result.items.length
          }条删除成功，${result.items.length}条删除失败。删除失败单号: ${result.items.join('、')}`,
        );
      }
      vm.cleanSelectedKeys();
      await vm.refreshData();
    } catch (e) {
      showMessage.error(e?.message);
    }
  };

  const showToast = (message: string) => {
    Modal.error({
      title: '提示',
      content: message,
      centered: true,
      onOk() { },
    });
  };

  const handleRemind = () => {
    const selectedKeys = vm.isSelectAll ? vm.allFlowIds : vm.selectedKeys
    const result = vm.dataSource.filter(item => selectedKeys.includes(item.id) && item.state === 'approving')
    if(result.length) {
      vm.handleAllRemind(result, selectedKeys)
    }else {
      showMessage.warning(i18n.get('已选中单据当前状态不支持催办'))
    }
  }

  const handleSelectAll = async () => {
    vm.isSelectAll = true
    await app.open('@settlement-checkin:ExportSelectAllModal', {
      vm,
      onDelete: handleDelete,
      onLaunchChecking: handleLaunchChecking,
      onRemind: handleRemind,
    })
  }

  return useObserver(() => (
    <div className="hose-checking-detail-info-bottom-wrapper">
      {!props.hideButton && <Button category="primary" disabled={!vm.selectedKeys.length} size="small" onClick={handleLaunchChecking}>
        <T name="发起对账" />
      </Button>}
      {
        props.exportProps && <Button category="secondary" theme="highlight" size="small" onClick={props.exportProps?.onClick}>
          <T name="导出" />
        </Button>
      }
      {!props.hideButton && <Button category="secondary" theme="highlight" size="small" icon={<OutlinedGeneralBell />} disabled={!vm.selectedKeys.length} onClick={handleRemind}>
        <T name="催办" />
      </Button>}
      {!props.hideButton && <Button category="secondary" theme="danger" size="small" disabled={!vm.selectedKeys.length} onClick={handleDelete}>
        <T name="删除" />
      </Button>}
      {
        vm.total ?  <span className={'action color-primary'} onClick={handleSelectAll}>
        {i18n.get('选择全部')}
      </span> : null
      }
    </div >
  ));
};

export default HoseCheckingBillStatisticsDetailBottom;
