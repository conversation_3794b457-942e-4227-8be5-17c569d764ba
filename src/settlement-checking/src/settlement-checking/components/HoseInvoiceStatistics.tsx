import React, { useEffect } from 'react';
import { T } from '@ekuaibao/i18n';
import { app } from '@ekuaibao/whispered';
import { useInstance } from '@ekuaibao/react-ioc';
import { SettlementCheckingDetailVm } from '../vms/settlement-checking-detail.vm';
import { useObserver } from 'mobx-react-lite';
import { toJS } from 'mobx';
import { showModal } from '@ekuaibao/show-util';
import { OutlinedDirectionDetails } from '@hose/eui-icons'
import InvoiceProgress from "./InvoiceProgress";

type Props = {
  hideButton?: boolean;
  value: any;
};

export const HoseInvoiceStatistics: React.FC<Props> = (props) => {
  const vm = useInstance(SettlementCheckingDetailVm.NAME);
  useEffect(() => {
    vm.getInvoiceStatistics(props?.value?.legalEntityId);
    props.bus.on('checkingBilled:confirmed', onRefresh)
    return () => {
      props.bus.un('checkingBilled:confirmed', onRefresh)
    }
  }, []);
  const onRefresh = () => {
    vm.getInvoiceStatistics(props?.value?.legalEntityId);
  }
  const handleClick = () => {
    // 弹窗打开
    if (props?.hideButton) {
      const table = toJS(vm.invoiceStatisticsTable);
      if (Object.keys(table)?.length > 0) {
        if (table?.INVOICE_TODO) {
          showModal.confirm({
            tittle: i18n.get('提示'),
            content: i18n.get('商城账单尚未开票！'),
          });
          // showMessage.info(i18n.get('商城账单尚未开票！'))
        } else {
          app.open('@invoice-manage:UniteInvoiceMange', { fromLayer: true, value: props?.value });
        }
      }
    } else {
      const invoicingDetailsProps = toJS(vm.invoiceStatisticsDataSource);
      const params = {
        fromLayer: false,
        value: {
          supplierAccountId: props.value.supplierAccountId,
        },
        invoicingDetailsProps
      };
      app.store.dispatch('@layout5/activeMenu')('unifiedOpening', JSON.stringify(params))
    }
  };

  return useObserver(() => {
    return (
      <div className="checking-bill-wrapper">
        <div className="item-wrapper jc-b">
          <div className="item-name">
            <T name="开票概览" />
          </div>
          <div className="check-detail detail-margin" onClick={handleClick}>
            <T name="详情" />
            <OutlinedDirectionDetails className='detail-icon' />
          </div>
        </div>
        <InvoiceProgress columns={vm.hoseInvoiceStatisticsColumns} dataSource={vm.invoiceStatisticsDataSource} table={vm.invoiceStatisticsTable} />
      </div>
    );
  });
};

export default HoseInvoiceStatistics;
