import React from 'react';
import { Button } from 'antd';
import { T } from '@ekuaibao/i18n';
import { useInstance } from '@ekuaibao/react-ioc';
import { SettlementCheckingDetailVm } from '../vms/settlement-checking-detail.vm';
import { showMessage, showModal } from '@ekuaibao/show-util';
import { useObserver } from 'mobx-react-lite';
import { KEELVM } from '@ekuaibao/keel';
import { app } from '@ekuaibao/whispered';
import { checkingCheckingBillProgress, getBillSwitchConfig } from '../../setttlement-checkin-action';
const EKBIcon = app.require<any>('@elements/ekbIcon');
export const CheckingBillTop: React.FC = () => {
  const vm: any = useInstance(SettlementCheckingDetailVm.NAME);
  const keel: any = useInstance(KEELVM);
  const prevConfirm = () => {
    // 获取商旅对账单同步配置
    getBillSwitchConfig({billPlatform: 'TRAVEL_ONE', type: 'BillConfirmConfig'})
    .then((result: any) => {
      const { value } = result
      const { apiSyncSwitch = false } = value || {}
      // 校验是否开启了【对账单审批通过后，与差旅壹号确认】
      if (!!apiSyncSwitch) {
        showModal.confirm({
          title: i18n.get('确定对账单'),
          content:i18n.get('确认后，本期对账单将无法修改，请确认本期对账单数据已全部核对无误'),
          okText: i18n.get('确定'),
          cancelText: i18n.get('取消'),
          onOk: () => {
            confirm()
          },
          onCancel: () => {}
        })
      } else {
        confirm()
      }
    })
    .catch((e: any) => {})
  }
  const confirm = async () => {
    try {
      await vm.confirmCheckingBill();
      const text = vm?.checkingBill?.importMethod === 'api' ? '提交商城审核中' : '本期对账完成';
      showMessage.success(text);
    } catch (e) {
      showMessage.error(e.message);
    }
  };

  const launchChecking = async () => {
    try {
      await vm.launchChecking();
      await app.open('@settlement-checkin:SubmitCheckingModal', {
        result: 'PENDING',
        failureReasons: [],
        type: 'CHECKING_PROGTESS',
        progressMode: 'progress',
        checkingBillId: vm.checkingBill.id,
        action: checkingCheckingBillProgress,
      });
      keel.open('CheckingBillStatisticsDetail', {
        value: vm.checkingStatisticsOriginData,
        checkingBill: vm.checkingBill,
      });
      await vm.refreshData();
    } catch (e) {
      showMessage.error(e?.message);
    }
  };
  return useObserver(() => {
    return (
      <div className="detail-top">
        <div className="title">
          <EKBIcon
            name="#EDico-APP-back"
            className="icc"
            style={{ marginRight: 4 }}
            onClick={() => {
              keel?.closeTo(0);
            }}
          />
          <T name="对账单详情" />
        </div>
        <div className="action">
          <Button type="primary" onClick={prevConfirm}>
            <T name="确认本期账单" />
          </Button>
          <Button type="primary" onClick={launchChecking}>
            <T name="生成子对账单" />
          </Button>
        </div>
      </div>
    );
  });
};

export default CheckingBillTop;
