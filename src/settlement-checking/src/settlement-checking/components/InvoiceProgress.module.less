.invoice-progress-wrapper {
  width: 100%;
  background-color: var(--eui-static-white);
  display: flex;
  flex-direction: column;

  :global {
    .PAID {
      background-color: var(--eui-function-success-400);
    }

    .PAYING {
      background-color: var(--eui-function-warning-400);
    }

    .DRAFT {
      background-color: var(--eui-function-info-400);
    }

    .WHITE{
      background-color: #FFF;
    }

    .progress-wrapper {
      display: flex;
      flex-direction: column;
      flex: 2 1;

      .money {
        margin-top: 24px;
        font: var(--eui-num-display-b2);
        color: var(--eui-text-title);
      }

      .progress-view {
        height: 170px;
        width: 100%;
        display: flex;
        position: relative;
        .total-money {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          >.money-total {
            font: var(--eui-num-head-b2);
            color: var(--eui-text-title);
            display: flex;
            justify-content: center;
          }
          >.subtitle {
            font: var(--eui-font-note-r2);
            color: var(--eui-text-caption);
          }
        }
        .box {
          &:first-child {
            border-left: unset;
          }

          border-left: 1px solid var(--eui-static-white);
          display: flex;
          height: 6px;
        }
      }
    }

    .content-wrapper {
      padding: 0 16px;
      .content-border-bottom {
        border-bottom: 1px dashed var(--eui-line-divider-default);
        padding-bottom: 6px;
      }
      .content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        margin-top: 6px;
        .content-status {
          width: 72px;
          display: inline-block;
          margin-right: 8px;
        }
        .content-th-font {
          font: var(--eui-font-body-r1);
          color: var(--eui-text-caption);
        }
        .content-money {
          width: 72px;
          display: inline-block;
          margin-right: 8px;
          text-align: right;
        }
        .content-money-font {
          font: var(--eui-num-body-b1);
          color: #0A0A0A;
        }
        .money-padding {
          padding-right: 4px;
        }
        .td-warp {
          display: flex;
        }
        .left-v {
          display: flex;
          align-items: center;

          .dot {
            width: 6px;
            height: 6px;
            border-radius: 6px;
          }

          .status {
            margin-left: 6px;
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            color: #404040;
          }
          .status-sub {
            margin-left: 6px;
            font: var(--eui-font-body-r1);
            color: var(--eui-text-caption);
            margin-top: 2px;
          }
        }

        .money {
          font: var(--eui-num-body-b1);
          color: var(--eui-text-title);
        }
      }

      .content-m-t {
        margin-top: 18px;
      }

      .content-left {
        justify-content: unset;
      }
    }
  }
}
