@import '~@ekuaibao/eui-styles/less/token.less';

.hose-checking-detail-tab-wrapper {
  display: flex;
  flex-direction: column;
  background-color: #f7f7f7;
  flex: 1;
  .checking-detail-tab {
    .eui-tabs-content-holder {
      display: flex;
      .eui-tabs-content {
        .eui-tabs-tabpane {
          height: 100%;
          .ekb-tab-content {
            height: 100%;
          }
        }
      }
    }
  }
}

.hose-checking-detail-tab-wrapper-single {
  padding: 32px 16px 8px;
  background-color: @color-white-1;
  height: 100%;
  & > .ant-tabs {
    position: static;
  }
  .hose-checking-detail-tab-wrapper-title {
    font: var(--eui-font-body-b1);
    color: var(--eui-text-title);
    display: flex;
    justify-items: center;
    margin-bottom: 14px;
  }
  .checking-fee-detail-table-wrapper {
    margin: 0;
  }
}
