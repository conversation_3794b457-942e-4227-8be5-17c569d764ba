import React, { useEffect } from 'react';
import { app } from '@ekuaibao/whispered';
import { T } from '@ekuaibao/i18n';
import { useInstance as runInstance} from '@ekuaibao/react-ioc';
import { SettlementCheckingDetailVm } from '../vms/settlement-checking-detail.vm';
import { useObserver } from 'mobx-react-lite';
import { KEELVM } from '@ekuaibao/keel';
import { checkingSaveBill } from '../../setttlement-checkin-action'
const Money = app.require<any>('@elements/puppet/Money');

type Props = {
  hideButton?: boolean;
  value: any
}

export const CheckingBillStatistics: React.FC<Props> = (props) => {
  const vm: any = runInstance(SettlementCheckingDetailVm.NAME);
  const keel: any = !props.hideButton && runInstance(KEELVM);
  const { stackerManager } = props
  useEffect(() => {
    vm.getCheckingStatistics(props?.value?.legalEntityId);
  }, []);
  const renderItem = (item) => {
    const cl = item.type === 'total' ? 'item-wrapper bc fw-500' : 'item-wrapper item-hover';
    return (
      <div key={item.name} className={cl}>
        <div className="f-1 ta-l">{item.name}</div>
        <div className="dp-f f-1 jc-e">
          <Money value={item.value} />
        </div>
      </div>
    );
  };
  const checkDetail = async () => {
    const type = 'CHECKING_SAVE'
    let res: any = await checkingSaveBill({ type, checkingBillId: vm.checkingBill.id })
    if (res.value && res.value.state === 'FAILURE') {
      await app.open('@settlement-checkin:SubmitCheckingModal', { result: res.value.state, failureReasons: res.value.failureReasons, type: 'CHECKING_SAVE', value: res.value })
    }
    if (stackerManager) {
      stackerManager.push('CheckingBillStatisticsDetail', {
        value: vm.checkingStatisticsOriginData,
        checkingBill: vm.checkingBill,
        hideButton: props.hideButton
      })
    } else {
      keel.open('CheckingBillStatisticsDetail', {
        value: vm.checkingStatisticsOriginData,
        checkingBill: vm.checkingBill,
      });
    }
  }
  return useObserver(() => {
    return (
      <div className="checking-bill-wrapper">
        <div className="item-wrapper jc-b">
          <div className="fs-16 fw-500">
            <T name="对账概览" />
          </div>
          <div className="check-detail" onClick={checkDetail}>
            <T name="查看详情" />
          </div>
        </div>
        {vm.checkingStatisticsData.map((item) => {
          return renderItem(item);
        })}
      </div>
    );
  });
};

export default CheckingBillStatistics;
