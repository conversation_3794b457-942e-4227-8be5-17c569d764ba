import React from 'react';
import { T } from '@ekuaibao/i18n';
import { app } from '@ekuaibao/whispered';
import { ICheckingBillStatistics } from '../../types/interface';
import { useObserver } from 'mobx-react-lite';
import { useInstance } from '@ekuaibao/react-ioc';
import { CheckingBillStatisticsDetailVm } from '../vms/checking-bill-statistics-detail.vm';
const Money = app.require<any>('@elements/puppet/Money');

interface CheckingBillStatisticsDetailOverviewProps {}

export const CheckingBillStatisticsDetailOverview: React.FC<CheckingBillStatisticsDetailOverviewProps> = () => {
  const vm = useInstance<CheckingBillStatisticsDetailVm>(CheckingBillStatisticsDetailVm.NAME);
  const renderItem = (item) => {
    return (
      <div key={item.name} className="dp-f p-4">
        <div className="dp-f">
          <div style={{ width: 100, textAlign: 'right' }}>{item.name}</div>
          <span className="ml-4 fw-500">{item.count}</span>
        </div>
        <div className="dp-f f-1 ml-16">
          <div style={{ width: 60, textAlign: 'right' }}>
            <T name="金额/元：" />
          </div>
          <Money value={item.value} className="fw-500" />
        </div>
      </div>
    );
  };
  return useObserver(() => {
    const items = parseStatisticsData(vm.statisticsInfo);
    return (
      <div className="detail-overview">
        <div className="dp-f">
          <div>
            <div className="color-b1 money">{vm.statisticsInfo?.all?.total}</div>
            <div>
              <T name="子对账单个数" />
            </div>
          </div>
          <div className="ml-40">
            <Money showSymbol={false} value={vm.statisticsInfo?.all?.amount} className="color-b1 money" />
            <div>
              <T name="金额总计/元" />
            </div>
          </div>
        </div>
        <div className="line" />
        <div className="dp-f fd-c f-1">
          {items.map((item) => {
            return renderItem(item);
          })}
        </div>
      </div>
    );
  });
};

const parseStatisticsData = (data?: ICheckingBillStatistics) => {
  return [
    { name: i18n.get('待对账个数：'), count: data?.todo?.total ?? 0, value: data?.todo?.amount ?? 0 },
    { name: i18n.get('对账中个数：'), count: data?.doing?.total ?? 0, value: data?.doing?.amount ?? 0 },
    { name: i18n.get('对账完成个数：'), count: data?.done?.total ?? 0, value: data?.done?.amount ?? 0 },
  ];
};

export default CheckingBillStatisticsDetailOverview;
