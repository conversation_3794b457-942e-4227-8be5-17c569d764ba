import React, { useEffect } from 'react';
import { T } from '@ekuaibao/i18n';
import { app } from '@ekuaibao/whispered';
import { useInstance } from '@ekuaibao/react-ioc';
import { SettlementCheckingDetailVm } from '../vms/settlement-checking-detail.vm';
import { useObserver } from 'mobx-react-lite';
import { Table } from 'antd';
import { toJS } from 'mobx';
import { showModal } from '@ekuaibao/show-util';

type Props = {
  hideButton?: boolean;
  value: any;
};

export const InvoiceStatistics: React.FC<Props> = (props) => {
  const vm = useInstance(SettlementCheckingDetailVm.NAME);
  useEffect(() => {
    vm.getInvoiceStatistics(props?.value?.legalEntityId);
  }, []);
  const handleClick = () => {
    // 弹窗打开
    if (props?.hideButton) {
      const table = toJS(vm.invoiceStatisticsTable);
      if (Object.keys(table)?.length > 0) {
        if (table?.INVOICE_TODO) {
          showModal.confirm({
            tittle: i18n.get('提示'),
            content: i18n.get('商城账单尚未开票！'),
          });
          // showMessage.info(i18n.get('商城账单尚未开票！'))
        } else {
          app.open('@invoice-manage:UniteInvoiceMange', { fromLayer: true, value: props?.value });
        }
      }
    } else {
      app.go('/unite-invoice-manage');
    }
  };
  const rowClassName = () => {
    return 'bg-white';
  };
  const components = {
    header: {
      cell: TableHeader,
    },
  };
  return useObserver(() => {
    return (
      <div className="invoice-wrapper dp-f fd-c">
        <div className="item-wrapper jc-b">
          <div className="fs-16 fw-500">
            <T name="开票概览" />
          </div>
          <div className="check-detail" onClick={handleClick}>
            <T name="查看详情" />
          </div>
        </div>
        <div className="content f-1 dp-f fd-c">
          <Table
            size="middle"
            components={components}
            columns={vm.invoiceStatisticsColumns}
            pagination={false}
            dataSource={vm.invoiceStatisticsDataSource}
            rowClassName={rowClassName}
          />
        </div>
      </div>
    );
  });
};
const TableHeader = (props) => {
  const { className, ...others } = props;
  return <th {...others} className={`${className} bg-gray column-title`} />;
};

export default InvoiceStatistics;
