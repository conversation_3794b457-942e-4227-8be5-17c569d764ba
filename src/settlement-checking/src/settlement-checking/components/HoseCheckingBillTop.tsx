import React, { useEffect, useState } from 'react';
import { Button, Modal, Questionnaire, Tooltip, } from '@hose/eui';
import { T } from '@ekuaibao/i18n';
import { useInstance } from '@ekuaibao/react-ioc';
import { SettlementCheckingDetailVm } from '../vms/settlement-checking-detail.vm';
import { showMessage, showModal } from '@ekuaibao/show-util';
import { useObserver } from 'mobx-react-lite';
import { KEELVM } from '@ekuaibao/keel';
import { app } from '@ekuaibao/whispered';
import { TwoToneGeneralPrestored, TwoToneGeneralFacility, OutlinedDirectionLeft, OutlinedGeneralLoading } from '@hose/eui-icons';
import { checkingCheckingBillProgress, getBillSwitchConfig } from '../../setttlement-checkin-action';
import { IBillingPeriod, ICheckingBill, ISupplier } from '@ekuaibao/ekuaibao_types';
import { debounce } from 'lodash';
const Money = app.require<any>('@elements/puppet/Money');
// @ts-ignore
import CopyToClipboard from 'react-copy-to-clipboard';
import { questionnaireConfig } from "../../helpers/questionnaireConfig";
import CheckingCommonHeader from './HoseCheckingCommonHeader';

type IProps = {
  value: ICheckingBill;
};

const BUTTON_MAP: any = {
  'CONFIRMED': '取消确认账单',
  'UNCONFIRMED': '确认本期账单',
  'CONFIRMING': '确认本期账单',
  'CANCELCONFIRMING': '取消确认账单',
}

export const HoseCheckingBillTop: React.FC<IProps> = ({ value, bus }) => {
  const textConfirm = '「确认本期账单」意味着本账期数据锁定，不可新增、修改费用，或生成新的子对账单。点击确认按钮后，直到您在「开票概览」完成发票批次拆分，或在「结算概览」生成结算单前，仍可取消确认本期账单，以方便数据新增或修正。'
  const textCancelConfirm = '取消确认后，账单可以新增、修改数据，也可以重新生成子对账单'
  const vm: any = useInstance(SettlementCheckingDetailVm.NAME);
  const keel: any = useInstance(KEELVM);
  const [stateBill, setStateBill] = useState();
  const [loading, setLoading] = useState(false)
  let close = false
  let maxCount = 0
  useEffect(() => {
    checkBillState('init')
    return () => {
      close = true
    }
  }, []);

  const checkBillState = async (action?: string) => {
    const result = await vm.getStateBill()
    maxCount++
    const resultState = result.value
    setStateBill(resultState)
    if (!close && maxCount <= 100 && (resultState === "CONFIRMING" || resultState === 'CANCELCONFIRMING')) {
      setTimeout(() => {
        return checkBillState(action)
      }, 3000);
    } else {
      if (action === 'click') {
        const text = resultState === 'CONFIRMED' ? (vm?.checkingBill?.importMethod === 'api' ? '提交商城审核中' : '本期对账完成') : '确认取消完成';
        showMessage.success(text);
        if (resultState === 'CONFIRMED') {
          Questionnaire.initSurvey({
            sid: questionnaireConfig.confirmBill.sid,
            channelId: questionnaireConfig.confirmBill.channelId,
            width: questionnaireConfig.confirmBill.width,
            externalUserId: app.getState()['@common'].userinfo?.data?.staff?.userId,
            externalCompanyId: app.getState()['@common'].userinfo?.data?.staff?.corporationId,
          })
        }
        bus.emit('checkingBilled:confirmed')
      }
      if (action === 'check' && resultState !== stateBill) {
        showMessage.success(resultState === 'CONFIRMED' ? '账单已被确认' : '账单已被取消')
        return false
      }
    }
    return resultState
  }

  const prevConfirm = async () => {
    const currentState = await checkBillState('check')
    console.log(currentState)
    if (currentState === false) {
      return setLoading(false)
    }
    if (currentState === 'CONFIRMED') {
      setLoading(true)
      try {
        await vm.confirmCancelCheckingBill()
        await checkBillState('click')
        setLoading(false)
      } catch (error) {
        showMessage.error(error?.message)
        setLoading(false)
      }

    } else if (currentState === 'UNCONFIRMED') {
      Modal.confirm({
        title: '确认本期账单',
        content: '确认账单后不可在本账期新增、修改费用，或生成新的子对账单，是否确认账单数据已核对完成？',
        centered: true,
        onCancel() {},
        async onOk() {
          setLoading(true),
          confirm()
        }
      })
    }
  };

  const confirm = async () => {
    try {
      await vm.confirmCheckingBill();
      await checkBillState('click')
      setLoading(false)
    } catch (e) {
      showMessage.error(e.message);
      setLoading(false)
    }
  };

  const showToast = (message: string) => {
    Modal.error({
      title: '提示',
      content: message,
      centered: true,
      onOk() { },
    });
  };

  const launchChecking = async () => {
    try {
      await vm.launchChecking();
      await app.open('@settlement-checkin:SubmitCheckingModal', {
        result: 'PENDING',
        failureReasons: [],
        type: 'CHECKING_PROGTESS',
        progressMode: 'progress',
        checkingBillId: vm.checkingBill.id,
        action: checkingCheckingBillProgress,
      });
      keel.open('CheckingBillStatisticsDetail', {
        value: vm.checkingStatisticsOriginData,
        checkingBill: vm.checkingBill,
      });
      await vm.refreshData();
    } catch (e) {
      showToast(e?.message);
    }
  };
  const handleCopyBtnClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    showMessage.success(i18n.get('复制成功！'));
  };

  const debouncedHandleClick = debounce(prevConfirm, 300); // 300ms内只允许调用一次

  return useObserver(() => {
    // @ts-ignore
    return (
      <div className="detail-top-warp">
        <div className="detail-title" >
          <CheckingCommonHeader/>
        </div>
        <div className="detail-top">
          <div className="detail-title-warp">
            <div>
              <span className="supplier-name">{(value?.supplierArchiveId as ISupplier)?.name ?? '-'}</span>
              <span className='account-type'>
                {value?.settlementType === 'PRIECHARGE' ? (
                  <>
                    <TwoToneGeneralPrestored className="account-img" />
                    <span className="prestorei-font">
                      预存账户
                    </span>
                  </>
                ) : (
                  <>
                    <TwoToneGeneralFacility className="account-img" />
                    <span className="extension-font">
                      授信账户
                    </span>
                  </>
                )}
              </span>
            </div>
            <div>
              <Tooltip
                placement="bottomRight"
                title={stateBill == 'UNCONFIRMED' || stateBill == 'CONFIRMING' ? textConfirm : textCancelConfirm}
              >
                <Button
                  category="secondary"
                  theme="highlight"
                  onClick={debouncedHandleClick}
                  disabled={loading || stateBill == 'CONFIRMING' || stateBill == 'CANCELCONFIRMING'}
                  className="mr-8"
                  loading={loading || stateBill == 'CONFIRMING' || stateBill == 'CANCELCONFIRMING'}
                  loadingIcon={<OutlinedGeneralLoading spin fontSize={12} />}
                >
                  <T name={BUTTON_MAP[stateBill]} />
                </Button>
              </Tooltip>
              <Button
                category="primary"
                onClick={launchChecking}
                disabled={stateBill == 'CONFIRMING' || stateBill == 'CANCELCONFIRMING'}
              >
                <T name="生成子对账单" />
              </Button>
            </div>
          </div>
          <div className="detail-msg-warp">
            <span className="company-date">{(value?.billPeriod as IBillingPeriod)?.name ?? '-'}</span>
            <span className="line"></span>
            <div className="info-wrapper">
              <T name="结算金额：" />
              <Money
                className="company-money"
                showSymbol={false}
                value={value?.splitType === 'MACH' ? value?.settlementAmount : value?.feeTypeAmount}
              />
            </div>
            <span className="line"></span>
            <div className="info-wrapper">
              <T name="企业对账单号：" />
              <CopyToClipboard text={value.code}>
                <span className="company-code" onClick={handleCopyBtnClick} style={{ cursor: 'pointer' }}>
                  {value.code}
                </span>
              </CopyToClipboard>
            </div>
          </div>
        </div>
      </div>
    );
  });
};

export default HoseCheckingBillTop;
