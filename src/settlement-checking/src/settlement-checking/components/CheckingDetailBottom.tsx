import React from 'react';
import './CheckingDetailBottom.less';
import { Button } from 'antd';
import { app } from '@ekuaibao/whispered';
import { T } from '@ekuaibao/i18n';
import { useInstance } from '@ekuaibao/react-ioc';
import { FeeDetailVm } from '../vms/FeeDetail.vm';
import { showMessage, showModal } from '@ekuaibao/show-util';
import { useObserver } from 'mobx-react-lite';

export const CheckingDetailBottom: React.FC = () => {
  const vm = useInstance<FeeDetailVm>(FeeDetailVm.NAME);

  const handleUnChecking = async () => {
    if (!vm.selectedKeys?.length) {
      return showMessage.warning('请选择不对账明细');
    }
    const action = async (keys: string[]) => {
      try {
        await vm.unCheckingBill(keys);
        await vm.fetch();
      } catch (e) {
        showMessage.error(e.message);
      }
    };
    beforeAction(i18n.get('本期不对账'), action);
  };

  const handleBatchChecking = async () => {
    if (!vm.selectedKeys?.length) {
      return showMessage.warning('请选择对账明细');
    }
    const modify = async (selectedKeys) => {
      const selectedData = vm.dataSource.filter((item) => vm.selectedKeys.includes(item.id));
      await app.open('@settlement-checkin:BatchModifyFeeInfo', { selectedKeys, selectedData });
      await vm.fetch();
    };
    beforeAction(i18n.get('修改'), modify);
  };

  const beforeAction = (title: string, action: (keys: string[]) => {}) => {
    const [count, message, keys] = vm.checkData(title);
    if (!count) {
      return showModal.confirm({ title: i18n.get('提示'), content: <div style={{ marginTop: 16 }}>{message}</div> });
    }
    if (!!message) {
      return showModal.confirm({
        title: i18n.get('提示'),
        content: <div style={{ marginTop: 16 }}>{message}</div>,
        onOk: () => {
          action(keys);
        },
      });
    }
    action(keys);
  };

  return useObserver(() => (
    <div className="checking-detail-bottom-wrapper">
      <Button className="bottom-btn" type={'primary'} disabled={!vm.selectedKeys.length} onClick={handleBatchChecking}>
        <T name="批量修改" />
      </Button>
    </div>
  ));
};

export const UnCheckingDetailBottom: React.FC = (props) => {
  const vm = useInstance<FeeDetailVm>(FeeDetailVm.NAME);

  const handleChecking = async () => {
    if (!vm.selectedKeys?.length) {
      return showMessage.warning('请选择对账明细');
    }
    try {
      await vm.restoreCheckingBill();
      await vm.fetch();
    } catch (e) {
      showMessage.error(e.message);
    }
  };

  return useObserver(() => (
    <div className="checking-detail-bottom-wrapper">
      <Button className="bottom-btn" disabled={!vm.selectedKeys.length} onClick={handleChecking}>
        <T name="移入费用明细" />
      </Button>
    </div>
  ));
};
