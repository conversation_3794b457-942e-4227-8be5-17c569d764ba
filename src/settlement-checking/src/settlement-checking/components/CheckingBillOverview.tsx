/*
 * @Description:
 * @Creator: chencan<PERSON>han
 * @Date: 2022-03-11 10:54:34
 */
import React from 'react';
import { T } from '@ekuaibao/i18n';
import { app } from '@ekuaibao/whispered';
import { ICheckingBill, IBillingPeriod, ISupplier } from '@ekuaibao/ekuaibao_types';
import CREDIT from '../../images/CREDIT.svg';
import PRIECHARGE from '../../images/PRIECHARGE.svg';
import { showMessage } from '@ekuaibao/show-util';
// @ts-ignore
import CopyToClipboard from 'react-copy-to-clipboard';

const EKBIcon = app.require('@elements/ekbIcon');
const Money = app.require<any>('@elements/puppet/Money');

interface CheckingBillOverviewProps {
  value: ICheckingBill;
}
export const CheckingBillOverview: React.FC<CheckingBillOverviewProps> = ({ value }) => {
  const handleCopyBtnClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    showMessage.success(i18n.get('复制成功！'));
  };
  return (
    <div className="detail-info-wrapper">
      <div className="date">
        <EKBIcon name="#ico-7-icon_date" />
        <div className="fw-500">{(value?.billPeriod as IBillingPeriod)?.name ?? '-'}</div>
      </div>
      <div className="info-wrapper">
        <div>
          <Money
            className="money"
            showSymbol={false}
            value={value?.splitType === 'MACH' ? value?.settlementAmount : value?.feeTypeAmount}
          />
          <div>
            <T name="对账单金额/元" />
          </div>
        </div>
        <div className="supplier-name">
          <T name="供应商名称：" />
          <div className="mt-4">{(value?.supplierArchiveId as ISupplier)?.name ?? '-'}</div>
        </div>
        <div className="account-type">
          <T name="账户类型：" />
          <div className="mt-4 dp-f">
            <img style={{ marginRight: 4 }} src={value?.settlementType === 'PRIECHARGE' ? PRIECHARGE : CREDIT} />
            {value?.settlementType === 'PRIECHARGE' ? '预存账户' : '授信账户'}
          </div>
        </div>
        <div className="company-code">
          <T name="企业对账单号：" />
          <CopyToClipboard text={value.code}>
            <div className="mt-4" onClick={handleCopyBtnClick} style={{ cursor: 'pointer' }}>
              {value.code}
            </div>
          </CopyToClipboard>
        </div>
      </div>
    </div>
  );
};

export default CheckingBillOverview;
