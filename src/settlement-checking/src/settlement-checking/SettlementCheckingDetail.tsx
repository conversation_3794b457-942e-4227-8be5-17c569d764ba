import React from 'react';
import FeeDetailWrapper from './components/FeeDetailWrapper';
import { inject, provider } from '@ekuaibao/react-ioc';
import { FeeDetailVm } from './vms/FeeDetail.vm';
import { SettlementCheckingDetailVm } from './vms/settlement-checking-detail.vm';
import { DepSettlementVm } from './vms/DepSettlement.vm';
import { ICheckingBill } from '@ekuaibao/ekuaibao_types';
import CheckingBillOverview from './components/CheckingBillOverview';
import styles from './SettlementCheckingDetail.module.less';
import FeeTypeStatistics from './components/FeeTypeStatistics';
import BillStatistics from './components/BillStatistics';
import CheckingBillTop from './components/CheckingBillTop';
import { Alert } from 'antd';
import { EnhanceConnect } from '@ekuaibao/store';
import { getConfigSettlementList, getDepartementCheckConfig } from '../setttlement-checkin-action';
import { get } from 'lodash';
interface SettlementCheckingDetailProps {
  [key: string]: any;
  value: ICheckingBill;
}
interface SettlementCheckingDetailState {}
const hasCheckConfig = (config: any) => config?.type === 'BILL_FIELD'
@provider(
  [FeeDetailVm.NAME, FeeDetailVm],
  [SettlementCheckingDetailVm.NAME, SettlementCheckingDetailVm],
  [DepSettlementVm.NAME, DepSettlementVm],
)
@EnhanceConnect((state: any) => ({
  KA_ZY_Reconciliation_Settlement: state['@common'].powers.KA_ZY_Reconciliation_Settlement,
  userInfo: state['@common'].userinfo,
}))

export default class SettlementCheckingDetail extends React.Component<
  SettlementCheckingDetailProps,
  SettlementCheckingDetailState
> {
  @inject(SettlementCheckingDetailVm.NAME) detailVm: SettlementCheckingDetailVm;
  state = {
    isApportions: false, // 结算单配置法人实体维度是否设置分摊
    depParams:{},// 是否是部门对账，是否在拓展中心对账结算配置部门字段
  };

  async componentWillMount() {
    // this.props?._id无值为从结算概览穿透过来的，这种场景下，如果该供应商账户的结算单设置结算维度中法人实体包含明细分摊，则异常对账单详情卡片
    if (!this.props?._id) {
      let isApportions = false;
      const supplierAccountId = get(this.props, 'value.supplierAccountId.id');
      const settlementList = await getConfigSettlementList();
      if (settlementList?.items?.length && supplierAccountId) {
        const settlementItem = settlementList?.items.find((el: any) => el.supplierAccountId === supplierAccountId);
        if (
          settlementItem &&
          settlementItem?.settlementDimension?.type === 'legalEntity' &&
          settlementItem?.settlementDimension?.apportions === true
        ) {
          isApportions = true;
        }
      }
      this.setState({ isApportions });
    }
  }
  getDepartementCheckConfig = async () => {
    const { corporation = {} } = this.props.userInfo
    const { value } = await getDepartementCheckConfig(corporation.id)
    let depParams:any = {}
    if (hasCheckConfig(value)) {
      depParams.expenseDepartment = 'expenseDepartment'
    }
    this.setState({ depParams })
  }
  componentDidMount() {
    this.detailVm.init(this.props.value);
    this.getDepartementCheckConfig()
  }

  render() {
    const { stackerManager } = this.props;
    const { tppReason, tppState } = this.props.value;
    const { isApportions, depParams } = this.state;
    return (
      <div className={styles['settlement-checking-detail-wrapper']}>
        {!(this.props.value as any)?.hideButton && <CheckingBillTop />}
        <div className="content-wrapper">
          {tppState !== 'agree' && tppReason && (
            <Alert message={this.props.value.tppReason} type="warning" showIcon closable style={{ marginBottom: 8 }} />
          )}
          <CheckingBillOverview value={this.props.value} />
          <FeeTypeStatistics value={this.props.value} searchApportions={isApportions} depParams={depParams}/>
          {(this.props.value as any)?.hideButton &&
          (this.props.value as any)?.hiddenCheckBill &&
          (this.props.value as any)?.hiddenInvoice &&
          (this.props.value as any)?.hiddenSettle ? null : (
            <BillStatistics
              hideButton={(this.props.value as any)?.hideButton}
              hiddenCheckBill={isApportions ? isApportions : (this.props.value as any)?.hiddenCheckBill}
              hiddenInvoice={(this.props.value as any)?.hiddenInvoice}
              hiddenSettle={(this.props.value as any)?.hiddenSettle}
              value={this.props.value}
              stackerManager={stackerManager}
            />
          )}
          <FeeDetailWrapper
            settlementManager={this.props.KA_ZY_Reconciliation_Settlement}
            value={this.props.value}
            isApportions={isApportions}
            sourceType={'SettlementCheckingDetail'}
          />
        </div>
      </div>
    );
  }
}
