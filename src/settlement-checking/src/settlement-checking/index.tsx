import { app } from '@ekuaibao/whispered';
import React, { PureComponent } from 'react';
import { Keel, registerComponentsCellar } from '@ekuaibao/keel';
const KeelSingleViewHeader = app.require<any>('@elements/puppet/KeelSingleViewHeader');
const KeelViewBody = app.require<any>('@elements/puppet/KeelViewBody');

@registerComponentsCellar([
  {
    key: 'SettlementCheckingList',
    getComponent: () => import('./SettlementCheckingList'),
    title: i18n.get('企业对账单'),
    hiddenHeader: true
  },
  {
    key: 'SettlementCheckingDetail',
    getComponent: () => import('./HoseSettlementCheckingDetail'),
    title: i18n.get('对账单详情'),
    hiddenHeader: true,
  },
  {
    key: 'CheckingBillStatisticsDetail',
    getComponent: () => import('./HoseCheckingBillStatisticsDetail'),
    title: i18n.get('对账概览'),
    hiddenHeader: true,
  },
  {
    key: 'SettlementStatisticsDetail',
    getComponent: () => import('./SettlementStatisticsDetail'),
    title: i18n.get('结算概览'),
    hiddenHeader: true,
  },
])
export default class SettlementCheckingView extends PureComponent {
  render() {
    return (
      <Keel>
        <KeelSingleViewHeader viewKey="SettlementCheckingList" />
        <KeelViewBody classNameKey="content-main" />
      </Keel>
    );
  }
}
