/**
 *  Created by pw on 2021/6/17 下午1:10.
 */
import React, { useEffect, useState, useRef } from 'react';
import './SettlementStatisticsDetail.less';
import { Button, Table } from 'antd';
import { T } from '@ekuaibao/i18n';
import { app } from '@ekuaibao/whispered';
import TableWrapper from '../components/TableWrapper';
import { useObserver } from 'mobx-react-lite';
import { useInstance, provider } from '@ekuaibao/react-ioc';
import { SettlementStatisticsBillVm, SettlementStatisticsDetailVm } from './vms/SettlementStatisticsDetail.vm';
import { baseInfoStatisticalColumn } from './utils/SettlementStatisticsDetailUtil';
import { Map } from '@ekuaibao/datagrid/lib/types/utils';
import { Data } from '@ekuaibao/datagrid/lib/types/dataSource';
import { checkingSaveBill } from '../setttlement-checkin-action';
import { PageMode, PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination';
const Money = app.require<any>('@elements/puppet/Money');
import { showMessage } from '@ekuaibao/show-util';
import CheckingCommonHeader from './components/HoseCheckingCommonHeader';
interface Props {
  checkingBillId: string;
}

const SettlementStatisticsDetail: React.FC<any> = (props: Props) => {
  const { checkingBillId } = props;
  const detailVm = useInstance<SettlementStatisticsDetailVm>(SettlementStatisticsDetailVm.NAME);
  const billVm = useInstance<SettlementStatisticsBillVm>(SettlementStatisticsBillVm.NAME);
  useEffect(() => {
    detailVm.checkingBillId = checkingBillId;
    detailVm.fetch();
    billVm.init(checkingBillId);
    return () => {
      billVm.disposer!();
    };
  }, [checkingBillId]);

  return useObserver(() => (
    <div className="settlement-statistics-detail-wrapper">
      <div className="detail-title">
        <CheckingCommonHeader/>
      </div>
      <div className="settlement-statistics-detail-content">
        <SettlementBaseInfo checkingBillId={checkingBillId} />
        <SettlementStatisticsBill checkingBillId={checkingBillId} />
      </div>
      <SettlementBottom />
    </div>
  ));
};

interface SettlementBaseInfoProps {
  checkingBillId: string;
}

const SettlementBaseInfo: React.FC<SettlementBaseInfoProps> = (props) => {
  const { checkingBillId } = props;
  const billVm = useInstance<SettlementStatisticsBillVm>(SettlementStatisticsBillVm.NAME);
  const detailVm = useInstance<SettlementStatisticsDetailVm>(SettlementStatisticsDetailVm.NAME);
  const [loading, setLoading] = useState(false);
  const handleCreate = async () => {
    await app.open('@settlement-checkin:AddSettlementModal', {
      checkingBillId,
      isSettlementField: detailVm.hasSettlementRule,
    });
    await app.open('@settlement-checkin:SubmitCheckingModal', {
      result: 'PENDING',
      failureReasons: [],
      type: 'SETTLE_SAVE',
      checkingBillId: checkingBillId,
      action: checkingSaveBill,
    });
    await billVm.fetch();
    await detailVm.fetch();
  };

  const handleRefresh = async () => {
    setLoading(true);
    await billVm.fetch();
    await detailVm.fetch();
    setLoading(false);
  };

  const Btn: any = Button;
  return useObserver(() => (
    <div className="settlement-base-info-wrapper">
      <SettlementBaseInfoOverview />
      <SettlementBaseInfoList />
      {/*{!!billVm?.dataSource?.length ? null : (*/}
      <div className="base-info-action">
        <Btn size={'large'} type={'primary'} onClick={handleCreate}>
          <T name={'创建结算单'} />
        </Btn>
        <Button className={'ml-8'} loading={loading} size={'large'} onClick={handleRefresh}>
          <T name={'刷新结算数据'} />
        </Button>
      </div>
      {/*)}*/}
    </div>
  ));
};

const SettlementBaseInfoOverview: React.FC = () => {
  const detailVm = useInstance<SettlementStatisticsDetailVm>(SettlementStatisticsDetailVm.NAME);

  const OverviewItem: React.FC<OverviewItemProps> = (props) => {
    const { label, value, className = '' } = props;
    return (
      <div className={`column ${className}`}>
        <div className="label">
          <T name={label} />
        </div>
        <div className="column-value">{value}</div>
      </div>
    );
  };

  return useObserver(() => (
    <div className="settlement-base-info-overview">
      <div className="column">
        <Money className={'money'} value={detailVm?.statisticsInfo?.totalAmount} />
        <div className="label">
          <T name={'本期结算金额'} />
        </div>
      </div>
      <OverviewItem className="ml-72" label={'供应商名称：'} value={detailVm?.baseInfo?.supplierArchive?.name} />
      <OverviewItem className="ml-48" label={'账单期间：'} value={detailVm?.baseInfo?.name} />
      <div className="column ml-48">
        <div className="label">
          <T name={'收款信息：'} />
        </div>
        {detailVm?.baseInfo?.supplierArchive ? (
          <div className="column-value">
            {detailVm?.baseInfo?.supplierArchive?.defaultPaymentAccountId?.icon?.length && (
              <img className="bank-img" src={detailVm?.baseInfo?.supplierArchive?.defaultPaymentAccountId?.icon} />
            )}
            <div className="bank-name">{`${
              detailVm?.baseInfo?.supplierArchive?.defaultPaymentAccountId?.bank || '-'
            }`}</div>
            <div className="bank-code">{detailVm?.baseInfo?.supplierArchive?.defaultPaymentAccountId?.accountNo}</div>
          </div>
        ) : null}
      </div>
    </div>
  ));
};
interface IBaseInfoList {}
const SettlementBaseInfoList: React.FC<IBaseInfoList> = (props: IBaseInfoList) => {
  const detailVm = useInstance<SettlementStatisticsDetailVm>(SettlementStatisticsDetailVm.NAME);
  return useObserver(() => (
    <div className="settlement-base-info-list">
      <Table
        className="settlement-base-info-list-table"
        rowKey={'entityId'}
        dataSource={detailVm?.statisticsInfo?.statistics}
        columns={baseInfoStatisticalColumn(detailVm.hasSettlementRule ? detailVm?.statisticsInfo?.statistics : null)}
        pagination={false}
        scroll={{ y: '300px' }}
      />
    </div>
  ));
};

const SettlementStatisticsBill: React.FC<SettlementBaseInfoProps> = ({ checkingBillId }) => {
  const vm = useInstance<SettlementStatisticsBillVm>(SettlementStatisticsBillVm.NAME);
  const [isVisibleSaveDiff, setIsVisibleSaveDiff] = useState(false);
  const _instance = useRef(null);

  const handleSorterChange = (sorter: Map<'ascend' | 'descend'>) => {
    vm.sorters = sorter;
  };

  const handleFilterChange = (filter: Map<any>) => {
    vm.filters = filter;
  };

  const handleSelectedChange = (selectedKeys: string[], selectedRowsData: Map<Data>) => {
    vm.selectedKeys = selectedKeys;
  };
  const handleColumnChooseChange = (visibleColumns: string[]) => {
    vm.updateColumns(visibleColumns);
  };
  const handleColumnReset = () => {
    vm.resetColumns();
  };
  const handlePageChange = (pagination: PaginationConfig, pageMode: PageMode) => {
    vm.currentPage = pagination.current;
    vm.pageSize = pagination.size;
    vm.pageMode = pageMode;
  };

  const refreshState = (obj: any) => {
    setIsVisibleSaveDiff(obj);
    if (obj.hasOwnProperty('isVisibleSaveDiff')) {
      setIsVisibleSaveDiff(obj['isVisibleSaveDiff']);
    }
  };

  const getInstance = (instance: any) => {
    _instance.current = instance;
  };

  const onSaveDiffScenes = () => {
    if (_instance) {
      let visibleColumns = _instance?.current?.getVisibleColumns() ?? [];
      visibleColumns =
        visibleColumns.length > 0 && visibleColumns.filter((d: any) => d.dataField).map((d: any) => d.dataField);
      vm.saveScene(visibleColumns)
        .then(() => {
          showMessage.success(i18n.get('保存成功'));
          setIsVisibleSaveDiff(false);
        })
        .catch((err: any) => {
          showMessage.error(err.message);
        });
    }
  };
  return useObserver(() => (
    <div className="settlement-bill-wrapper">
      <div className="content-wrapper">
        <TableWrapper
          tableProps={{
            dataSource: vm.dataSource,
            columns: vm.columns,
            filters: vm.filters,
            scrolling: vm.scrolling,
            pageSize: vm.dataSource.length,
            groupPanel: { visible: true, allowColumnDragging: false },
            selectedRowKeys: vm.selectedKeys,
            onSelectedChange: handleSelectedChange,
            onSorterChange: handleSorterChange,
            onFilterChange: handleFilterChange,
            disabledScroll: true,
            fixedSelect: true,
            getInstance: getInstance,
          }}
          columnChooserProps={{
            defaultVisibleColumns: vm.columns.map((item) => item.dataIndex).filter((item) => !!item),
            columns: vm.allColumns,
            onChange: handleColumnChooseChange,
            onReset: handleColumnReset,
            refreshState: (params: any) => refreshState(params),
          }}
          paginationProps={{
            totalLength: vm.dataTotal,
            pagination: {
              current: vm.currentPage,
              size: vm.pageSize,
            },
            disabledScroll: true,
            onChange: handlePageChange,
            pageMode: vm.pageMode,
          }}
        />
        {isVisibleSaveDiff && (
          <div
            style={{
              position: 'absolute',
              right: '62px',
              fontSize: '14px',
              color: 'var(--brand-base)',
              cursor: 'pointer',
              lineHeight: '50px',
            }}
            onClick={() => {
              onSaveDiffScenes();
            }}
          >
            {i18n.get('保存变更')}
          </div>
        )}
      </div>
    </div>
  ));
};

const SettlementBottom: React.FC = () => {
  const billVm = useInstance<SettlementStatisticsBillVm>(SettlementStatisticsBillVm.NAME);

  const handleSettlement = () => {
    billVm.submitSettlementBill();
    app.open('@settlement-checkin:SubmitCheckingModal', {
      result: 'PENDING',
      failureReasons: [],
      type: 'SETTLE_SUBMIT',
      checkingBillId: billVm.checkingBillId,
      action: checkingSaveBill,
    });
  };

  const Btn: any = Button;
  return useObserver(() => (
    <div className="settlement-bottom-wrapper">
      <Btn disabled={!billVm?.selectedKeys?.length} size={'large'} type={'primary'} onClick={handleSettlement}>
        <T name={'发起结算'} />
      </Btn>
    </div>
  ));
};

interface OverviewItemProps {
  label: string;
  value: string;
  className?: string;
}

export default provider(
  [SettlementStatisticsDetailVm.NAME, SettlementStatisticsDetailVm],
  [SettlementStatisticsBillVm.NAME, SettlementStatisticsBillVm],
)(SettlementStatisticsDetail);
