import React from 'react';
import styles from './CheckingBillStatisticsDetail.module.less';
import CheckingBillStatisticsDetailOverview from './components/CheckingBillStatisticsDetailOverview';
import CheckingBillStatisticsList from './components/CheckingBillStatisticsList';
import { ICheckingBillStatistics } from '../types/interface';
import { ICheckingBill } from '@ekuaibao/ekuaibao_types';
import { inject, provider } from '@ekuaibao/react-ioc';
import { CheckingBillStatisticsDetailVm } from './vms/checking-bill-statistics-detail.vm';
import CheckingBillStatisticsDetailBottom from './components/CheckingBillStatisticsDetailBottom';
import CheckingCommonHeader from './components/CheckingCommonHeader';

interface CheckingBillStatisticsDetailProps {
  value: ICheckingBillStatistics;
  checkingBill: ICheckingBill;
}
interface CheckingBillStatisticsDetailState { }
@provider([CheckingBillStatisticsDetailVm.NAME, CheckingBillStatisticsDetailVm])
export default class CheckingBillStatisticsDetail extends React.Component<
CheckingBillStatisticsDetailProps,
CheckingBillStatisticsDetailState
> {
  @inject(CheckingBillStatisticsDetailVm.NAME) vm: CheckingBillStatisticsDetailVm;
  componentDidMount() {
    const { value, checkingBill, stackerManager } = this.props;
    this.vm.init(value, checkingBill, stackerManager);
  }
  componentWillUnmount() {
    this.vm.clean();
  }

  render() {
    return (
      <div className={styles['checking-bill-statistics-detail-wrapper']}>
        <CheckingCommonHeader title='对账概览' />
        <div className="content">
          <CheckingBillStatisticsDetailOverview />
          <CheckingBillStatisticsList />
        </div>
        <CheckingBillStatisticsDetailBottom hideButton={this.props.hideButton} />
      </div>
    );
  }
}
