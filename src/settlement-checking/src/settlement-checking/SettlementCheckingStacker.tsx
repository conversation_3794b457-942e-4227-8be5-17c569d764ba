
import React, { PureComponent } from 'react';
import { app } from '@ekuaibao/whispered';
import { EnhanceStackerManager, IStackerManagerProps } from '@ekuaibao/enhance-stacker-manager'
import styles from './SettlementCheckingViewStacker.module.less'

const EKBBreadcrumb = app.require<any>('@ekb-components/business/breadcrumb/Breadcrumb')
type Props = IStackerManagerProps & {
    value: any
    immersiveable: any
    getStacker: (stacker: any) => {}
}
@EnhanceStackerManager([
    {
        key: 'SettlementCheckingDetail',
        getComponent: () => import('./SettlementCheckingDetail'),
        title: i18n.get('对账单详情')
    },
    {
        key: 'CheckingBillStatisticsDetail',
        getComponent: () => import('./CheckingBillStatisticsDetail'),
        title: i18n.get('对账概览'),
    },
    {
        key: 'BillInfoView',
        getComponent: () => app.require<any>('@bills/elements/BillInfoView').load(),
        title: i18n.get('单据详情'),
    }
])
export default class SettlementCheckingViewStacker extends PureComponent<Props> {
    componentDidMount() {
        const { stackerManager, value, getStacker } = this.props
        getStacker && getStacker(stackerManager)
        stackerManager?.push('SettlementCheckingDetail', {
            value
        })
    }
    handleMenuClick(line: any, i: number) {
        let { stackerManager } = this.props
        stackerManager?.open(i, { ...line })
    }


    renderBreadcrumb() {
        const { immersiveable, stackerManager } = this.props
        const array = stackerManager?.values()
        let items: any = []
        array?.forEach((line: any, i: number) => {
            items.push({
                key: i,
                onClick: () => this.handleMenuClick(line, i),
                title: line.viewTitle || line.title
            })
        })
        return <EKBBreadcrumb immersiveable={immersiveable} items={items} />
    }

    render() {

        return (
            <>
                <div className={styles.header}>
                    {this.renderBreadcrumb()}
                </div>
                <div className={styles.SettlementCheckingViewStacker} >
                    {this.props.children}
                </div>
            </>
        );
    }
}
