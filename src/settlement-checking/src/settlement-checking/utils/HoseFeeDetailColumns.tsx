/**
 *  Created by pw on 2021/6/8 下午6:26.
 */
import React from 'react';
import { app } from '@ekuaibao/whispered';
import { Column } from '@ekuaibao/datagrid/esm/types/column';
import { GlobalFieldMapIF } from '@ekuaibao/ekuaibao_types';
const Money = app.require<any>('@elements/puppet/Money');
import { isObject } from '@ekuaibao/helpers';
import { isStaffs, isDepartments } from '@ekuaibao/lib/lib/propertySetIs';
import moment from 'moment';
import { stateMap } from './data-map';
const getColumnType = app.require<any>('@elements/HoseTable/filter');
export const fixedPath = 'form.feeTypeForm.';
export const apportionsPath = 'apportionForm.';
import { Tooltip } from 'antd';
export const noneType = '@@none';
const COLUMN_PROPERTY_MAPPING = {};
export function getColumnPropertyMapping() {
  return COLUMN_PROPERTY_MAPPING;
}
export function detailColumns(
  fields: string[] = [],
  staffs: any,
  dimensionItemMap: any,
  groupDimension?: string[],
): Column[] {
  const globalFieldMap: GlobalFieldMapIF = app.getState('@common.globalFields.baseDataPropertiesMap') as any;
  const fieldColumns = fields
    .map((field) => {
      const fieldStr = Array.isArray(field) ? field.join('.') : field
      const key = fieldStr.replace(fixedPath, '');
      const globalField = globalFieldMap[key];
      if (!globalField) return undefined;
      let type = globalField?.dataType?.type || globalField?.dataType?.elemType?.type || 'text';
      if (isStaffs(globalField)) {
        type = 'organization:staff'
      }
      if (isDepartments(globalField)) {
        type = 'organization:department'
      }
      COLUMN_PROPERTY_MAPPING[`${fixedPath}${globalField.name}`] = globalField;
      const item: any = {
        label: globalField.label,
        title: globalField.label,
        dataIndex: `${fixedPath}${globalField.name}`.split('.'),
        type,
        filterType: type,
        allowFiltering: true,
        sorter: true,
        minWidth: 160,
        key: `${fixedPath}${globalField.name}`,
        entity: globalField?.dataType?.entity,
        canExport: true,
        ellipsis: true,
      }
      const columnType = getColumnType(item)
      return {
        ...item,
        ...columnType,
        render: (text, record) => {
          return (
            <ColumnRender
              field={globalField}
              value={text}
              record={record}
              staffs={staffs}
              dimensionItemMap={dimensionItemMap}
            />
          );
        },
      };
    })
    .filter((item) => !!item);
  const item: any = {
    label: i18n.get('费用类型'),
    title: i18n.get('费用类型'),
    dataIndex: ['form', 'feeTypeId'],
    type: noneType,
    filterType: 'ref',
    allowFiltering: true,
    sorter: true,
    fixed: 'left',
    minWidth: 150,
    key: 'form.feeTypeId',
  }
  const columnType = getColumnType(item)
  const feeType = {
    ...item,
    ...columnType,
    render: (text, record) => {
      return <ColumnRender value={text} record={record} />;
    },
  };

  let columnData = [feeType, ...fieldColumns, ...statusCol()];
  if (groupDimension && groupDimension?.length > 0) {
    for (const columnDataItem of columnData) {
      for (let j = 0; j < groupDimension.length; j++) {
        if (columnDataItem?.dataIndex === groupDimension[j]) {
          columnDataItem.groupIndex = j;
        }
      }
    }
  }

  return columnData;
}

export function apportionColumns(fields: string[] = [], staffs: any): Column[] {
  const globalFieldMap: GlobalFieldMapIF = app.getState('@common.globalFields.baseDataPropertiesMap') as any;
  const fieldColumns = fields
    .map((field) => {
      const key = field.replace(fixedPath, '');
      const globalField = globalFieldMap[key];
      if (!globalField) return undefined;
      const type = globalField?.dataType?.type || globalField?.dataType?.elemType?.type || 'text';
      return {
        label: globalField.label,
        title: globalField.label,
        dataIndex: `${apportionsPath}${globalField.name}`,
        type,
        filterType: type,
        allowFiltering: true,
        minWidth: 160,
        canExport: true,
        key: `${fixedPath}${globalField.name}`,
        entity: globalField?.dataType?.entity,
        render: (text, record) => {
          return <ColumnRender field={globalField} value={text} record={record} staffs={staffs} />;
        },
      };
    })
    .filter((item) => !!item);
  return fieldColumns;
}

const statusCol = () => {
  return [
    {
      label: i18n.get('对账状态'),
      title: i18n.get('对账状态'),
      dataIndex: 'checkingState',
      sorter: true,
      type: noneType,
      dataType: 'checking',
      key: 'checkingState',
      allowFiltering: true,
      filterType: 'list',
      filterDataSource: [{
        value: 'CHECKING_TODO',
        label: '待对账'
      }, {
        value: 'CHECKING_DOING',
        label: '对账中'
      }, {
        value: 'CHECKING_DONE',
        label: '已对账'
      }]
    },
    {
      label: i18n.get('开票状态'),
      title: i18n.get('开票状态'),
      dataIndex: 'invoiceState',
      sorter: true,
      type: noneType,
      dataType: 'invoice',
      key: 'invoiceState',
      allowFiltering: true,
      filterType: 'list',
      filterDataSource: [
        {
          value: 'NONE',
          label: '未开票'
        }, {
          value: 'INVOICE_TODO',
          label: '待开票'
        }, {
          value: 'INVOICE_DOING',
          label: '开票中'
        }, {
          value: 'INVOICE_DONE',
          label: '已开票'
        }]
    },
    {
      label: i18n.get('结算状态'),
      title: i18n.get('结算状态'),
      sorter: true,
      type: noneType,
      dataIndex: 'settlementState',
      dataType: 'settlement',
      key: 'settlementState',
      allowFiltering: true,
      filterType: 'list',
      filterDataSource: [{
        value: 'NONE',
        label: '未结算'
      }, {
        value: 'SETTLEMENT_TODO',
        label: '待结算'
      }, {
        value: 'SETTLEMENT_DOING',
        label: '结算中'
      }, {
        value: 'SETTLEMENT_DONE',
        label: '已结算'
      }]
    },
  ].map((item) => {
    const columnType = getColumnType(item)
    return {
      ...item,
      ...columnType,
      minWidth: 150,
      render: (text, record) => {
        return <ColumnRender field={item} value={text} record={record} />;
      },
    };
  });
};

const ColumnRender: React.ReactNode = (props: any) => {
  const { field, value, staffs, dimensionItemMap } = props;
  if (!value) {
    return <Null />;
  }
  if (
    field?.dataType.type === 'list' &&
    field?.dataType.elemType.entity === 'organization.Staff' &&
    staffs &&
    staffs.length
  ) {
    if (value.length) {
      const vv = value
        .map((id: string) => {
          let staff = staffs.find((v: any) => v.id === id) || {};
          return staff.name;
        })
        .join(',');
      let vstr =
        value.length > 2
          ? value
            .slice(0, 2)
            .map((id: string) => {
              let staff = staffs.find((v: any) => v.id === id) || {};
              return staff.name;
            })
            .join(',') + `...`
          : vv;
      return <Tooltip title={vv}>{vstr}</Tooltip>;
    }
    return <Null />;
  }
  if (field?.dataType.type === 'list' && field?.dataType?.elemType?.entity?.startsWith('basedata.Dimension.')) {
    if (value.length) {
      const entity = field?.dataType?.elemType?.entity;
      const vv = value
        .map((id: string) => {
          let dimensionItem = dimensionItemMap?.[entity]?.find((v: any) => v.id === id) || {};
          return `${dimensionItem.name}(${dimensionItem.code})`;
        })
        .join(',');
      let vstr =
        value.length > 2
          ? value
            .slice(0, 2)
            .map((id: string) => {
              let dimensionItem = dimensionItemMap?.[entity]?.find((v: any) => v.id === id) || {};
              return `${dimensionItem.name}(${dimensionItem.code})`;
            })
            .join(',') + `...`
          : vv;
      return <Tooltip title={vv}>{vstr}</Tooltip>;
    }
    return <Null />;
  }
  if (field?.dataType?.type === 'money') {
    return <Money style={{ textAlign: 'right' }} value={value} />;
  }
  if (field?.dataType?.type === 'date') {
    return moment(value).format('YYYY-MM-DD');
  }
  if (field?.dataType?.type === 'dateRange' && value) {
    const { start, end } = value;
    return <span>{`${moment(start).format('YYYY-MM-DD')} ~ ${moment(end).format('YYYY-MM-DD')}`}</span>;
  }
  if (field?.dataType?.entity === 'basedata.city' && value && value?.length) {
    const cities: any[] = JSON.parse(value);
    return <span>{cities.map((city) => city.label).join(',')}</span>;
  }
  if (isObject(value)) {
    const valueName = value?.active ? value?.name : `${value?.name}(已停用)`;
    return !!value?.name ? <span>{valueName}</span> : <Null />;
  }
  if (field?.type === noneType) {
    return <div>{stateMap(field.dataType)[value]}</div>;
  }

  return !!value ? <span>{value}</span> : <Null />;
};

const Null: React.FC = () => <span>{'-'}</span>;
