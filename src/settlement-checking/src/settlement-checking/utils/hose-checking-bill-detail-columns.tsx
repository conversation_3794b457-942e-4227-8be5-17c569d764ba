import React from 'react';
import { app } from '@ekuaibao/whispered';
import { stateMap } from '@ekuaibao/lib/lib/enums';
const { createColumns } = app.require('@lib/columns-util');
import styles from './column.module.less';
import { GlobalFieldIF } from '@ekuaibao/ekuaibao_types';
import { CheckingBillStatisticsDetailVm } from '../vms/checking-bill-statistics-detail.vm';
import { Tooltip } from 'antd';
import { mapping } from './mapping';
const getColumnType = app.require<any>('@elements/HoseTable/filter');
export const fixPath = 'form';
const EkbIcon = app.require('@elements/icon');
export function getExpenseFields() {
  const allFields: GlobalFieldIF[] = app.getState('@common.globalFields.data');
  return allFields.filter((f) => ['expense', 'flow', 'pay'].includes(f.ability) || !f.ability?.length);
}

export function billColumns(fields?: string[]) {
  const allFields = getExpenseFields();
  let fs = allFields;
  if (!!fields?.length) {
    const f: any[] = [];
    fields.forEach((item) => {
      f.push(allFields.find((f) => f.name === item));
    });
    fs = f.filter((item) => !!item);
  }
  const col = createColumns(fs, fixPath, mapping);
  const idx = col.findIndex((v: any) => v.key === 'state');
  col.splice(idx, 0, nodeStateColumns());
  return col.map((item: any) => {
    if (item.key === 'state') {
      item = { ...item, ...parseState() };
    }
    if (item.filterType === 'list' && item.filterDataSource instanceof Array) {
      item.lookup = {
        dataSource: item.filterDataSource,
        displayExpr: 'label',
        valueExpr: 'value',
      };
    }
    if (isStaff(item.entity)) {
      item.filterType = 'organization:staff'
    }
    if (isDepartment(item.entity)) {
      item.filterType = 'organization:department'
    }
    const columnType = getColumnType(item)
    const column = {
      width: 216,
      ellipsis: true,
      ...columnType,
      ...item,
      dataIndex: item?.dataIndex?.split?.('.')
    }
    if(!column?.render) {
      column.render = text => text ?? '-'
    }
    return column
  });
}

const isStaff = (entity) => {
  return entity === 'organization.Staff'
}

const isDepartment = (entity) => {
  return entity === 'organization.Department'
}

export function parseState() {
  const stateMapRes = stateMap();
  const BILL_STATUS: any = state(stateMapRes);
  return {
    filterType: 'list',
    filterDataSource: Object.keys(BILL_STATUS).map((key) => ({ label: BILL_STATUS[key], value: key })),
    render(text: any, record: any) {
      let state = BILL_STATUS[text];
      return (
        <span className={styles.urgentWrapper}>
          {record.isUrgent && <i className={styles.urgent} />}
          {state ? state : '-'}
        </span>
      );
    },
  };
}

const state = (stateMap: any) => {
  const oo: { [key: string]: string } = {};
  Object.keys(stateMap).forEach((key: string) => {
    if (key === 'paid') {
      oo[key] = i18n.get('已完成(待确认)');
    } else if (key === 'archived') {
      oo[key] = i18n.get('已完成(已确认)');
    } else if (['receiving', 'sending', 'PROCESS'].includes(key)) {
      return null;
    } else {
      oo[key] = stateMap[key].text;
    }
  });
  return oo;
};

export const option = (vm: CheckingBillStatisticsDetailVm, stackerManager?: any) => {
  return {
    title: i18n.get('操作'),
    label: i18n.get('操作'),
    width: 200,
    fixed: 'right',
    render: (value: string, record: any) => {
      const handleViewClick = (stackerManager) => {
        vm.openBillDetail(record, stackerManager);
      };

      const handleEditClick = () => {
        vm.openEditBill(record);
      };

      const handleErrorClick = () => {
        vm.openErrorLog(record?.generateError?.errorMessage);
      };

      const handleRemind = async () => {
        const info = await app.invokeService('@expense-manage:get:backlog:info:byId', record.id)
        record.plan = info?.value?.plan || {}
        vm.handleRemind(record)
      }

      const userInfo = app.getState('@common.userinfo');
      const isOwner = record?.ownerId?.id === userInfo?.staff?.id;
      const canEdit = isOwner && (record.state === 'draft' || record.state === 'rejected');
      return (
        <div style={{ display: 'flex' }}>
          <div style={{ color: 'var(--eui-primary-pri-500)', cursor: 'pointer' }}>
            {canEdit ? (
              <span className={'mr-8'} onClick={handleEditClick}>
                {i18n.get('编辑')}
              </span>
            ) : null}
            {!canEdit ? <span onClick={handleViewClick.bind(this, stackerManager)}>{i18n.get('查看详情')}</span> : null}
          </div>
          {
            record?.state === 'approving' &&
            <div className={'ml-8'} style={{ color: 'var(--eui-primary-pri-500)', cursor: 'pointer' }} onClick={handleRemind}>
              <span >{i18n.get('催办')}</span>
            </div>
          }
          {record?.generateError?.errorMessage?.length && (
            <div style={{ color: 'var(--eui-primary-pri-500)', cursor: 'pointer' }}>
              <span className={'ml-8'} onClick={handleErrorClick}>
                {i18n.get('错误日志')}
              </span>
            </div>
          )}
        </div>
      );
    },
  };
};

export const nodeStateColumns = () => {
  return {
    title: i18n.get('当前审批人'),
    filterType: 'text',
    key: 'nodeState.staffName',
    dataIndex: 'nodeState.staffName',
    label: (
      <Tooltip placement="bottom" title={i18n.get('暂不支持导出，打印')}>
        <span style={{ color: '#333' }}>{i18n.get('当前审批人')}</span>
        <EkbIcon name="EDico-help" />
      </Tooltip>
    ),
    className: 'fs-14',
    sorter: true,
  };
};
