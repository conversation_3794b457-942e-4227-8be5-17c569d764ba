import { Fetch } from '@ekuaibao/fetch';
import { app } from '@ekuaibao/whispered';
import { get } from 'lodash'

export async function openFeeDetailPre(data: any, vm: any) {
  const [res, userinfo, feeDetail, expenseTemplate] = await Promise.all([
    app.dataLoader('@common.feetypes').load(),
    app.dataLoader('@common.userinfo').load(),
    vm.getFeeDetailInfo(data.id),
    // 获取对账单模版，对账单模版id写死的：后台给的拼接规则
    app.invokeService('@custom-specification:get:specification:components', {
      ids: [`${Fetch.ekbCorpId}:system:%E5%AF%B9%E8%B4%A6%E5%8D%95`],
    }),
  ]);
  const { related } = app.require('@lib/requisition-relate');
  const requisition = feeDetail?.value?.requisitionId;
  // 获取申请事项中的明细数据
  // 查询申请事项应该用linkRequisitionInfo这个 id,没有情况下用 flowid
  const id = get(requisition, 'form.linkRequisitionInfo', requisition?.id ?? requisition)
  const result = await app.invokeService('@bills:get:related:detail:list', { ids: [id] });
  const applicationListDetails = result?.items ?? [];
  const expenseLink = requisition ? [requisition] : [];
  const tempComponent = expenseTemplate?.items?.[0]?.components?.find((item) => item.type === 'expenseLinks') ?? {};
  // 为了适配老得逻辑，问了一圈也没人知道为啥要把关联的申请单和报销单模版的关联申请组件保存到内存中
  related.setExpenseLink(expenseLink);
  related.setExpenseSpecification(tempComponent);
  const feeTypes = res?.data;
  return [feeTypes, userinfo, feeDetail, applicationListDetails];
}

export function openFeeDetailAfter() {
  const { related } = app.require('@lib/requisition-relate');
  related.setExpenseLink([]);
  related.setExpenseSpecification({});
}
