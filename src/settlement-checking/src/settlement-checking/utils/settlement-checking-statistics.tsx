import React from 'react';
import { app } from '@ekuaibao/whispered';
const Money = app.require<any>('@elements/puppet/Money');

interface Col {
  name: string;
  type: string;
  width?: number;
  data?: any;
}
export const statisticsColumns = (
  data: IFeeTypeStatistics,
  extendCol: Col | Col[] = { name: i18n.get('法人实体'), type: 'text', data: {} },
  colWidth = 150,
) => {
  const result = data?.col?.map((item) => {
    return {
      ...item,
      type: 'money',
      data: item,
    };
  });
  const arrayCol = Array.isArray(extendCol) ? extendCol : [extendCol]
  return [...arrayCol, ...result].map((item) => {
    return {
      label: item.name,
      title: item.name,
      dataIndex: 'id',
      minWidth: item.width ?? colWidth,
      render: (value, record) => {
        return <Render value={value} feeData={item} tableData={data?.table} type={item.type} record={record} />;
      },
    };
  });
};

interface RenderProps {
  value: string;
  record: { [key: string]: string };
  feeData: { [key: string]: string };
  tableData: { [key: string]: any };
  type: 'money' | 'text';
}

const Render: React.FC<RenderProps> = ({ type, feeData, tableData, value, record }) => {
  if (type === 'text') return <span>{record.name}</span>;
  const tData = tableData?.[value];
  if (type === 'total') {
    const sum = tData ? Object.values(tData).reduce((p, n) => new Big(p).plus(n).toFixed(2), 0) : 0
    return <Money value={sum} />;
  }

  return <Money value={tData?.[feeData.id]} />;
};

/*
* {
  col: [{ id: 'NONE', name: '-' }],
  row: [
    { id: '55kcIUa--ZpI00:catering', name: '餐饮' },
    { id: '_8QcIWMyHs3k00', name: '统一开票的' },
  ],
  table: { NONE: { _8QcIWMyHs3k00: 705, '55kcIUa--ZpI00:catering': 671 } },
};*/
export interface IFeeTypeStatistics {
  row: { id: string; name: string }[];
  col: { id: string; name: string }[];
  table: { [key: string]: { [key: string]: string | number } };
}
