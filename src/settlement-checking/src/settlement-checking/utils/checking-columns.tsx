import React from 'react';
import { app } from '@ekuaibao/whispered';
import { T } from '@ekuaibao/i18n';
import { Tooltip } from 'antd';
import { checkingState, invoiceState, settlementState } from './data-map';
import { SettlementCheckingListVm } from '../vms/settlement-checking-list.vm';
import { ICheckingBill } from '@ekuaibao/ekuaibao_types';
import { enterChecking as enterCheckingAction } from '../../setttlement-checkin-action';
import { showModal } from '@ekuaibao/show-util';
const Money = app.require<any>('@elements/puppet/Money');

export const columns = (vm: SettlementCheckingListVm) => {
  return [
    {
      title: i18n.get('账户'),
      dataIndex: ['supplierAccountId', 'name'],
      sorter: true,
      minWidth: 200,
      render: (value) => {
        if (value?.length > 8) {
          return (
            <Tooltip placement="top" title={value}>
              {value}
            </Tooltip>
          );
        }

        return value ?? '-';
      },
    },
    {
      title: i18n.get('消费总额'),
      dataIndex: 'feeTypeAmount',
      sorter: true,
      minWidth: 150,
      render: (value) => {
        return <Money style={{ textAlign: 'right' }} isShowThousandsSeparator={true} value={value} />;
      },
    },
    {
      title: i18n.get('企业对账单号'),
      dataIndex: 'code',
      sorter: true,
      minWidth: 200,
    },
    {
      title: i18n.get('总条数'),
      dataIndex: 'feeTypeCount',
      sorter: true,
      minWidth: 100,
    },
    {
      title: i18n.get('账单周期'),
      dataIndex: ['billPeriod', 'name'],
      sorter: true,
      minWidth: 120,
      render: (value) => {
        return value ?? '-';
      },
    },
    {
      title: i18n.get('对账状态'),
      dataIndex: 'checkingState',
      sorter: true,
      minWidth: 120,
      render: (value) => {
        return checkingState()[value] ?? '-';
      },
    },
    {
      title: i18n.get('开票状态'),
      dataIndex: 'invoiceState',
      sorter: true,
      minWidth: 120,
      render: (value) => {
        return invoiceState()[value] ?? '-';
      },
    },
    {
      title: i18n.get('结算状态'),
      dataIndex: 'settlementState',
      sorter: true,
      minWidth: 120,
      render: (value) => {
        return settlementState()[value] ?? '-';
      },
    },
    {
      title: i18n.get('操作'),
      dataIndex: 'operation',
      fixed: 'right',
      width: 100,
      render: (value: any, record: ICheckingBill) => {
        const handleClick = async () => {
          try {
            const t = await cantEnter(record, vm?.KA_ZY_Reconciliation_Settlement)
            if (t) return;
            vm.openDetail(record);
          } catch {
            vm.openDetail(record);
          }
        };
        return (
          <div style={{ color: 'var(--eui-primary-pri-500)', cursor: 'pointer' }} onClick={handleClick}>
            <T name="查看详情" />
          </div>
        );
      },
    },
  ];
};
export const cantEnter = async (record: ICheckingBill, hasCharge?: boolean) => {
  const {
    value: { enterChecking },
  } = await enterCheckingAction({ id: record?.id });
  const cant = hasCharge && !enterChecking
  if (cant) {
    showModal.warning({
      title: i18n.get('未匹配订单'),
      content: i18n.get('该供应商账单没有完成匹配订单，不可查看详情'),
    });
  }
  return cant;
};
