/**
 *  Created by pw on 2021/6/17 下午2:38.
 */
import React from 'react';
import { Column } from '@ekuaibao/datagrid/lib/types/column';
import { app } from '@ekuaibao/whispered';
import { MoneyIF } from '@ekuaibao/ekuaibao_types';
import { SettlementStatisticsBillVm } from '../vms/SettlementStatisticsDetail.vm';
const Money = app.require<any>('@elements/puppet/Money');

export function settlementStatisticsBillColumn(vm: SettlementStatisticsBillVm): Column[] {
  const actions = {
    title: '操作',
    label: '操作',
    width: 180,
    fixed: 'right',
    render: (value: string, record: any) => {
      const handleClick = () => {
        vm.openBillDetail(record);
      };

      const handleEdit = () => {
        vm.openEditBill(record);
      };

      const handleDel = () => {
        vm.delSettlementBill(record);
      };

      const isEdit = record.state === 'draft' || record.state === 'rejected';

      return (
        <div className="action">
          <a style={{ marginRight: 8 }} onClick={handleClick}>
            查看
          </a>
          {isEdit && (
            <a style={{ marginRight: 8 }} onClick={handleEdit}>
              编辑
            </a>
          )}
          {isEdit && (
            <a style={{ color: '#f4526b' }} onClick={handleDel}>
              删除
            </a>
          )}
        </div>
      );
    },
  };
  return actions;
}

const createColumn = (tabCol: any) => {
  if (tabCol?.length === 0) {
    return [];
  }
  return tabCol?.[0]?.checkingField?.map((item: any) => ({
    title: <ColumnTitleCP title={item.fieldLabel} />,
    dataIndex: 'checkingField',
    key: item.fieldLabel,
    render: (value: any) => (
      <div className="base-info-statistical-column-value">
        {value?.find((i: any) => i.fieldLabel === item.fieldLabel)?.fieldName ?? ''}
      </div>
    ),
  }));
};
export function baseInfoStatisticalColumn(tabCol: any) {
  const entityNameField = {
    title: <ColumnTitleCP title={'法人实体'} />,
    dataIndex: 'entityName',
    key: 'entityName',
    render: (value: string) => <div className="base-info-statistical-column-value">{value}</div>,
  };
  let col: Array<any> = tabCol ? createColumn(tabCol) ?? [entityNameField] : [entityNameField];
  return col.concat([
    {
      title: <ColumnTitleCP title={'待结算'} />,
      dataIndex: 'todoAmount',
      key: 'todoAmount',
      width: '20%',
      render: (value: MoneyIF) => <Money value={value} valueSize={14} fontWeight={600} />,
    },
    {
      title: <ColumnTitleCP title={'结算中'} />,
      dataIndex: 'doingAmount',
      key: 'doingAmount',
      width: '20%',
      render: (value: MoneyIF) => <Money value={value} valueSize={14} fontWeight={600} />,
    },
    {
      title: <ColumnTitleCP title={'已结算'} />,
      dataIndex: 'doneAmount',
      key: 'doneAmount',
      width: '20%',
      render: (value: MoneyIF) => <Money value={value} valueSize={14} fontWeight={600} />,
    },
  ]);
}

interface ColumnTitleCPProps {
  title: string;
}

const ColumnTitleCP: React.FC<ColumnTitleCPProps> = (props) => {
  const { title } = props;
  return <div className="base-info-statistical-column-title">{title}</div>;
};
