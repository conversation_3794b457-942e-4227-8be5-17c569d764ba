import React from 'react'
import { Space } from '@hose/eui'

const DotText = ({ status, text }) => {
  const statusMap: any = {
    NONE: 'var(--eui-decorative-neu-500)',
    TODO: 'var(--eui-decorative-neu-500)',
    DOING: 'var(--eui-function-info-500)',
    DONE: 'var(--eui-function-success-500)'
  }
  return <Space>
    <svg xmlns="http://www.w3.org/2000/svg" width="6" height="6" viewBox="0 0 6 6" fill="none">
      <circle cx="3" cy="3" r="3" fill={statusMap[status]} />
    </svg>
    {i18n.get(text)}
  </Space>
}
export const checkingState = () => ({
  NONE: <DotText status={'NONE'} text={'未对账'} />,
  CHECKING_TODO: <DotText status={'TODO'} text={'待对账'} />,
  CHECKING_DOING: <DotText status={'DOING'} text={'对账中'} />,
  CHECKING_DONE: <DotText status={'DONE'} text={'已对账'} />,
});

export const invoiceState = () => ({
  NONE: <DotText status={'NONE'} text={'未开票'} />,
  INVOICE_TODO: <DotText status={'TODO'} text={'待开票'} />,
  INVOICE_DOING: <DotText status={'DOING'} text={'开票中'} />,
  INVOICE_DONE: <DotText status={'DONE'} text={'已开票'} />,
});
export const settlementState = () => ({
  NONE: <DotText status={'NONE'} text={'未结算'} />,
  SETTLEMENT_TODO: <DotText status={'TODO'} text={'待结算'} />,
  SETTLEMENT_DOING: <DotText status={'DOING'} text={'结算中'} />,
  SETTLEMENT_DONE: <DotText status={'DONE'} text={'已结算'} />
});

export const stateMap = (type: 'checking' | 'invoice' | 'settlement') => {
  const a = {
    checkingState: checkingState(),
    invoiceState: invoiceState(),
    settlementState: settlementState(),
  };
  return a[`${type}State`];
};

export const searchOptionDepartment = () => {
  return {
    key: 'expenseDepartment',
    label: i18n.get('费用承担部门'),
    style: {
      width: 130
    },
    placeholder: i18n.get('请选择要搜索的部门'),
    type: 'department',
    format: (value) => {
      if (!value?.length) {
        return ''
      }
      const str = value.map(item => `"${item}"`).join(',')
      return `form.feeTypeForm.expenseDepartment.id.in(${str})`
    }
  }
}
