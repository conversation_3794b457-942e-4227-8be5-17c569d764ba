@import '~@ekuaibao/eui-styles/less/token';

.settlement-checking-detail-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  .font-size-2;
  :global {
    .fw-500 {
      font-weight: 500;
    }
    .fs-16 {
      .font-size-3;
    }

    .bc {
      background-color: #f7f7f7;
    }

    .ml-16 {
      margin-left: @space-6;
    }

    .ta-r {
      text-align: right;
    }
    .ta-l {
      text-align: left;
    }

    .dp-f {
      display: flex;
    }

    .fd-c {
      flex-direction: column;
    }

    .jc-b {
      justify-content: space-between;
    }

    .f-1 {
      flex: 1;
    }

    .as-e {
      align-self: flex-end;
    }

    .money {
      font-family: DINAlternate, DINAlternate-Bold, sans-serif;
      .font-size-6;
      font-weight: 700;
    }
    .content-wrapper {
      flex: 1;
      background-color: #f7f7f7;
      padding: @space-6;
    }
    .detail-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: @space-5 @space-6;
      .title {
        .font-size-4;
        font-weight: 500;
      }
      .action {
        .font-size-2;
        display: flex;
        > button {
          display: flex;
          margin-left: @space-6;
        }
      }
    }
    .detail-info-wrapper {
      margin-bottom: @space-6;

      .date {
        display: flex;
        align-items: center;
        .font-size-3;
        margin-bottom: @space-4;
        > div {
          margin-left: @space-4;
        }
      }
      .account-type,.company-code {
        margin-left: @space-10;
      }
      .info-wrapper {
        display: flex;
        color: @color-white-1;
        background-color: @color-brand;
        padding: 28px @space-8;
        border-radius: @radius-2;
        align-items: flex-end;
        box-shadow: 0px 2px 6px 0px rgba(34, 178, 204, 0.25);
        .mt-4 {
          margin-top: @space-2;
        }

        > :first-child {
          margin-right: @space-11;
        }
      }
    }
  }
}

.feetype-statistics-wrapper {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  border-radius: @radius-2;
  margin-bottom: @space-6;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
  .font-size-2;
  :global {
    .ant-table {
      .font-size-2;
    }
    .ta-c {
      text-align: center;
    }
    .bg-white {
      background-color: @color-white-1;
      .ta-c;
    }
    .bg-gray {
      background-color: #f7f7f7;
      .ta-c;
    }
    .column-title {
      color: #263344;
      font-weight: 400;
      .ta-c;
    }
  }
}

.bill-statistics-wrapper {
  margin-bottom: @space-6;
  .font-size-2;
  :global {
    .wrapper {
      background-color: @color-white-1;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
    }

    .item-wrapper {
      padding: @space-5;
      display: flex;
      cursor: pointer;
    }
    .item-hover:hover {
      background-color: #f0ffff;
    }

    .check-detail {
      color: @color-brand;
    }

    .checking-bill-wrapper {
      color: #374353;
      .wrapper;
    }

    .invoice-wrapper {
      .wrapper;
      .ant-table {
        .font-size-2;
      }
      .ant-table-tbody > tr > td div{  // 修复开票概览金额栏ui错位问题，money原原件修改怕影响了其他页面
        justify-content: center ;
      }
      .ant-table-tbody > tr > td {
        border-bottom: none;
      }
      .ant-table-thead > tr > th {
        border-bottom: none;
      }

      .ta-c {
        text-align: center;
      }
      .bg-white {
        background-color: @color-white-1;
        .ta-c;
        height: 46px;
      }
      .bg-gray {
        background-color: #f7f7f7;
        .ta-c;
      }
      .column-title {
        color: #263344;
        height: 46px;
        .ta-c;
      }
    }

    .settlement-wrapper {
      .wrapper;
    }
  }
}
