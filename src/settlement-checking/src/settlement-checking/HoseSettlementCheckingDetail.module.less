@import '~@ekuaibao/eui-styles/less/token';

.hose-settlement-checking-detail-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: var(--eui-bg-filler);
  padding: 6px 16px 0px;
  .font-size-2;
  :global {
    .fw-500 {
      font-weight: 500;
    }
    .fs-16 {
      .font-size-3;
    }
    .mr-8 {
      margin-right: 8px;
    }

    .bc {
      background-color: #f7f7f7;
    }

    .ml-16 {
      margin-left: @space-6;
    }

    .ta-r {
      text-align: right;
    }
    .ta-l {
      text-align: left;
    }

    .dp-f {
      display: flex;
    }

    .fd-c {
      flex-direction: column;
    }

    .jc-b {
      justify-content: space-between;
    }

    .f-1 {
      flex: 1;
    }

    .as-e {
      align-self: flex-end;
    }
    .hose-content-wrapper {
      height: 100%;
      .detail-top-warp {
        .detail-title {
          display: flex;
          align-items: center;
          padding-top: 10px;
          font:var(--eui-font-body-r1);
          margin-bottom: 8px;
          cursor: pointer;
        }
        .detail-top {
          height: 84px;
          width: 100%;
          background-color: var(--eui-bg-body);
          border-radius: 8px 8px 0px 0px;
          border-bottom: 1px solid var(--eui-line-divider-default);
          color: var(--eui-text-title);
          .detail-title-warp {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px 0px;
            .supplier-name {
              color: var(--eui-text-title);
              font-size: 16px;
              margin-right: 8px;
              font-weight: 500;
            }
            .account-type {
              font-size: 14px;
              .account-img {
                margin-right: 4px;
              }
              .prestorei-font {
                color: var(--eui-primary-pri-500)
              }
              .extension-font {
                color: var(--eui-function-success-500);
              }
            }
          }
          .detail-msg-warp {
            display: flex;
            padding: 0 16px;
            font: var(--eui-font-body-r1);
            height: 24px;
            line-height: 24px;
            .company-date {
              font-style: normal;
              font-weight: 700;
              font-size: 16px;
            }
            .line {
              width: 1px;
              height: 16px;
              background-color: var(--eui-line-border-card);
              margin: 4px 8px 0px;
            }
            .info-wrapper {
              display: flex;
              justify-items: center;
              .company-money {
                font: var(--eui-num-head-b1);
              }
              .company-code {
                font: var(--eui-font-head-b1);
              }
            }
          }
        }
      }
      .detail-settlement-warp {
        display: flex;
        height: calc(100% - 132px);
      }
      .detail-settlement-reason-warp {
        display: flex;
        height: calc(100% - 172px);
      }
    }
  }
}

.hose-feetype-statistics-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px 16px 0;
  :global {
    .ant-table {
      .font-size-2;
    }
    .ta-c {
      text-align: center;
    }
    .bg-white {
      background-color: @color-white-1;
      .ta-c;
    }
    .bg-gray {
      background-color: #f7f7f7;
      .ta-c;
    }
    .column-title {
      color: #263344;
      font-weight: 400;
      .ta-c;
    }
  }
}
.hose-feetype-statistics-screen {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
  z-index: 999;
}

.hose-feetype-statistics-right {
  width: 100%;
  background-color: var(--eui-bg-body);
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
  border-bottom-right-radius: 8px;
  position: relative;
  :global {
    .feetype-statistics {
      overflow-y: scroll;
      position: absolute;
      height: 100%;
      width: 100%;
    }
  }
}

.hose-display-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.hose-bill-statistics-wrapper {
  background-color: var(--eui-bg-body);
  width: 320px;
  transition-property: width;
  transition-duration: .15s, 1s;
  -webkit-backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  position: relative;
  flex-shrink: 0;
  border-bottom-left-radius: 8px;
  :global {
    .hose-bill-statistics {
      position: absolute;
      height: 100%;
      width: 319px;
      overflow-y: auto;
      border-bottom-left-radius: 8px;
    }
    .display-none {
      display: none;
    }
    .item-wrapper {
      padding: @space-5 0;
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-left: 16px;
      .item-name {
        font: var(--eui-font-body-b1);
      }
    }
    .item-hover:hover {
      background-color: #f0ffff;
    }

    .check-detail-wrap {
      .hose-display-center;
      .pull-lose-left {
        .hose-display-center;
        width: 24px;
        height: 32px;
        border: 1px solid var(--eui-line-border-component);
        border-radius: 4px 0px 0px 6px;
        border-right: 1px solid #FFFFFF;
        position: relative;
        right: -1px;
        z-index: 1;
      }
    }
    .check-detail {
      color: var(--eui-text-link-normal);
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      margin-right: 8px;
      .detail-icon {
        margin-left: 5px;
      }
    }
    .detail-margin {
      margin-right: 16px;
    }
    .checking-bill-wrapper {
      color: var(--eui-text-title);
      border-bottom: 1px solid var(--eui-line-divider-default);
      border-right: 1px solid var(--eui-line-divider-default);
      padding-bottom: 20px;
    }
    .checking-bill-wrapper:last-child {
      border-bottom: 0;
    }

    .invoice-wrapper {
      .ant-table {
        .font-size-2;
      }
      .ant-table-tbody > tr > td div{  // 修复开票概览金额栏ui错位问题，money原原件修改怕影响了其他页面
        justify-content: center ;
      }
      .ant-table-tbody > tr > td {
        border-bottom: none;
      }
      .ant-table-thead > tr > th {
        border-bottom: none;
      }

      .ta-c {
        text-align: center;
      }
      .bg-white {
        background-color: @color-white-1;
        .ta-c;
        height: 46px;
      }
      .bg-gray {
        background-color: #f7f7f7;
        .ta-c;
      }
      .column-title {
        color: #263344;
        height: 46px;
        .ta-c;
      }
    }

    .settlement-wrapper {

    }
  }
}
.hose-bill-statistics-none {
  width: 25px;
  border-right: none;
  transition-property: width;
  transition-duration: .15s, 1s;
  -webkit-backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  :global {
    .hose-bill-statistics {
      position: absolute;
      height: 100%;
      width: 319px;
      overflow-y: auto;
      border-bottom-left-radius: 8px;
    }
    .display-none {
      display: none;
    }
    .pull-lose-right {
      width: 24px;
      height: 32px;
      border: 1px solid var(--eui-line-border-component);
      border-radius: 0px 6px 6px 0px;
      border-left: none;
      .hose-display-center;
      cursor: pointer;
      margin-top: 15px;
    }
    .pull-lose-left {
      .hose-display-center;
      width: 24px;
      height: 32px;
      border: 1px solid var(--eui-line-border-component);
      border-radius: 4px 0px 0px 6px;
      border-right: 1px solid #FFFFFF;
      position: relative;
      right: -1px;
      z-index: 1;
    }
  }
}
