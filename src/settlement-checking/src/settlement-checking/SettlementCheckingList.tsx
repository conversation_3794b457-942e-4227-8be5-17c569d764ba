import React from 'react';
import styles from './SettlemnetCheckingList.module.less';
import SettlementBillFilterView from './components/SettlementBillFilterView';
import SettlementBillList from './components/SettlementBillList';
import { inject, provider } from '@ekuaibao/react-ioc';
import { SettlementCheckingListVm } from './vms/settlement-checking-list.vm';
import { T } from '@ekuaibao/i18n';
import { PageHeader } from '@hose/eui';
interface SettlementCheckingListProps { }
interface SettlementCheckingListState { }
@provider([SettlementCheckingListVm.NAME, SettlementCheckingListVm])
export default class SettlementCheckingList extends React.Component<
  SettlementCheckingListProps,
  SettlementCheckingListState
> {
  @inject(SettlementCheckingListVm.NAME) vm: SettlementCheckingListVm;

  componentDidMount() {
    this.vm.init();
  }

  componentWillUnmount() {
    this.vm.clean();
  }

  render() {
    return (
      <div className={styles['settlement-checking-list-content']}>

        <div className={styles['settlement-checking-list-wrapper']}>
          <PageHeader
            className="site-page-header"
            title={<T name="企业对账单" />}
          />
          <SettlementBillFilterView />
          <SettlementBillList />
        </div>
      </div>
    );
  }
}
