@import '~@ekuaibao/eui-styles/less/token';
.hose-checking-bill-statistics-detail-wrapper {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  background-color: var(--eui-bg-filler);
  padding: 6px 16px 10px;

  :global {
    .fs-24 {
      .font-size-6;
    }

    .jc-e {
      justify-content: flex-end;
    }

    .color-b1 {
      color: @color-black-1;
    }

    .fw-700 {
      font-weight: 700;
    }

    .bc {
      background-color: #f7f7f7;
    }
    .ml-4 {
      margin-left: @space-2;
    }
    .ml-8 {
      margin-left: @space-4;
    }
    .ml-16 {
      margin-left: @space-6;
    }

    .ml-40 {
      margin-left: @space-9;
    }

    .ta-r {
      text-align: right;
    }

    .dp-f {
      display: flex;
    }

    .fd-c {
      flex-direction: column;
    }

    .jc-b {
      justify-content: space-between;
    }

    .f-1 {
      flex: 1;
    }

    .as-e {
      align-self: flex-end;
    }
    .ai-c {
      align-items: center;
    }
    .p-4 {
      padding: @space-2;
    }
    .money {
      font-family: DINAlternate, DINAlternate-Bold, sans-serif;
      .font-size-6;
      font-weight: 700;
    }
    .line {
      width: 1px;
      height: 60px;
      background: var(--eui-line-divider-default);
    }
    .content {
      display: flex;
      flex: 1;
      flex-direction: column;
    }
    .hose-detail-overview {
      background-color: #FFFFFF;
      border-radius: 8px 8px 0px 0px;
      padding-bottom: 8px;
      &-top {
        padding: 16px 16px 0;
        .detail-msg-warp {
          display: flex;
          font: var(--eui-font-body-r1);
          height: 24px;
          line-height: 24px;
          .company-name {
            font: var(--eui-font-body-b1);
            margin: 2px 4px 0 0;
          }
          .line {
            width: 1px;
            height: 16px;
            background-color: var(--eui-line-border-card);
            margin: 4px 8px 0px;
          }
          .info-wrapper {
            display: flex;
            justify-items: center;
            .company-money {
              font: var(--eui-num-head-b1);
            }
            .company-code {
              font: var(--eui-font-head-b1);
            }
          }
        }
      }
      &-wrap {
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin: 18px 0;
        .progress-left {
          margin-left: 78px;
        }
        .progress-right {
          margin-right: 108px;
        }
      }
    }
    .hose-checking-detail-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;
      background-color: #FFFFFF;
      padding: 16px;
      border-top: 1px solid var(--eui-line-divider-default);
      border-radius: 0px 0px 8px 8px;
      .checking-detail-list-table-container {
        position: relative;
        padding: 6px 24px;
        background-color: @color-white-1;
      }
      .checking-detail-list-column-chooser {
        position: absolute;
        width: 32px;
        height: 32px;
        top: 10px;
        right: 6px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: #f7f7f7;
        border-radius: @space-2;
        > div {
          margin-top: 2px;
        }
      }
      .checking-detail-list-search {
        right: 116px !important;
      }
      .checking-detail-list-scene {
        position: absolute;
        left: 24px;
        top: 16px;
        z-index: 500;
      }
    }
    .hose-checking-detail-info-bottom-wrapper {
      display: flex;
      align-items: center;
      button {
        margin-right: @space-4;
      }
      .color-primary{
        font: var(--eui-font-body-r1);
        color: var(--eui-primary-pri-500);
        cursor: pointer;
      }
    }
    .hose-screen{
      position: fixed;
      top: 0px;
      left: 0px;
      z-index: 999;
      height: 100%;
      width: 100%;
      margin: 0;
      padding: 20px;
      background: #fff;
      overflow-y: auto;
    }
    .detail-title {
      display: flex;
      align-items: center;
      padding-top: 10px;
      font:var(--eui-font-body-r1);
      margin-bottom: 8px;
      cursor: pointer;
    }
  }
}
