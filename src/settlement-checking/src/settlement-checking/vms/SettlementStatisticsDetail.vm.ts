/**
 *  Created by pw on 2021/6/17 下午1:28.
 */
import { observable, IReactionDisposer, reaction, computed, action } from 'mobx';
import { TableVm } from '@ekuaibao/collection-definition';
import { Column } from '@ekuaibao/datagrid/lib/types/column';
import { app } from '@ekuaibao/whispered';
import { ISettlementStatisticsDetail, ISettlementSupplierInfo } from '@ekuaibao/ekuaibao_types';
import { settlementStatisticsBillColumn } from '../utils/SettlementStatisticsDetailUtil';
import {
  getSettlementBaseInfo,
  getSettlementStatisticalInfo,
  getSettlementBIllIds,
  submitSettlement,
  delSettlementBill,
  getConfigSettlementList
} from '../../setttlement-checkin-action';
import { billColumns, fixPath } from '../utils/checking-bill-detail-columns';
import { mapping } from '../utils/mapping';
import { showMessage, showModal } from '@ekuaibao/show-util';
import { Resource } from '@ekuaibao/fetch';
const sceneFilterAction = new Resource('/api/flow/v2/filter');
const fetchFixer = app.require<any>('@elements/data-grid/fetchFixer').default;

export class SettlementStatisticsDetailVm {
  static NAME = Symbol('Settlement_Statistics_Detail');

  @observable statisticsInfo: ISettlementStatisticsDetail;
  @observable baseInfo: ISettlementSupplierInfo;
  @observable checkingBillId: string = '';
  @observable hasSettlementRule?: boolean = false;

  async fetch() {
    const { value } = await getSettlementStatisticalInfo({ checkingBillId: this.checkingBillId });
    const { value: baseInfo } = await getSettlementBaseInfo({ checkingBillId: this.checkingBillId });
    this.hasSettlementRule = await this.getConfigSettlementList(baseInfo)
    this.statisticsInfo = value;
    this.baseInfo = baseInfo;
  }
  async getConfigSettlementList(baseInfo: any) {
    // supplierAccountId
    const { items = [] } = await getConfigSettlementList();
    return items.find((item: any) => item.supplierAccountId === baseInfo?.supplierAccountId)?.mappings?.find((i: any) => i.sourceField === 'expenseDepartment')
  }
}

export class SettlementStatisticsBillVm extends TableVm<any> {
  static NAME = Symbol('Settlement_Statistics_Bill');

  checkingBillId: string = '';
  @observable columns: Column[] = [];
  @observable selectedKeys: string[] = [];

  public disposer: IReactionDisposer | null = null;

  @computed get allColumns() {
    return billColumns();
  }

  init = async(checkingBillId: string) => {
    this.checkingBillId = checkingBillId;
    let columns = await this.getScene();
    if (!columns?.length) {
      columns = mapping.settlementBill;
    }
    this.createColumns(columns);
    this.disposer = reaction(
      () => [this.filters, this.sorters, this.pageSize, this.pageMode, this.currentPage],
      () => {
        this.fetch();
      },
      { fireImmediately: true },
    );
  }

  @action createColumns = (fields?: string[]) => {
    this.columns = [...billColumns(fields), ...settlementStatisticsBillColumn(this)];
  };

  updateColumns(columns: string[]) {
    const fields = columns.map((item) => item.replace(`${fixPath}.`, ''));
    this.createColumns(fields);
  }

  resetColumns() {
    this.createColumns(mapping.checkingBill);
  }

  async fetch() {
    const ids = await getSettlementBIllIds({ checkingBillId: this.checkingBillId });
    if (ids?.items?.length) {
      const params = this.buildParams(ids);
      const result = await app.invokeService('@expense-manage:search:flow:by:filter', { params, noFormType: true });
      this.dataSource = result.items;
      this.dataTotal = result.count
    } else {
      this.dataSource = [];
      this.dataTotal = 0
    }
  }

  buildParams = (ids: any) => {
    const p = this.params();
    const fixP = fetchFixer(p);
    const idsStr = ids?.items?.map((item: string) => `"${item}"`);
    return { status: { state: undefined }, ...fixP, otherFilters: [`id.in(${idsStr})`] };
  };

  async submitSettlementBill() {
    try {
      await submitSettlement({ checkingBillId: this.checkingBillId, flowIds: this.selectedKeys });
      await this.fetch();
      this.selectedKeys = [];
      showMessage.success('发起结算成功');
    } catch (e) {
      showMessage.error(e?.message);
    }
  }

  async delSettlementBill(record: any) {
    showModal.confirm({
      title: i18n.get('提示'),
      content: i18n.get('确认要删除？'),
      cancelText: i18n.get('取消'),
      okText: i18n.get('确定'),
      onOk: () => {
        delSettlementBill({ checkingBillId: this.checkingBillId, flowIds: [record.id] })
          .then((res) => {
            showMessage.success(i18n.get('删除成功'));
            this.fetch();
          })
          .catch((err) => {
            showMessage.error(err?.errorMessage || err?.message);
          });
      },
    });
  }

  openBillDetail = async (record: any) => {
    const resp = await app.invokeService('@expense-manage:get:backlog:info:byId', record.id);
    const flowId = resp.value;
    app.open(
      '@bills:BillInfoPopup',
      {
        title: i18n.get('单据详情'),
        backlog: { id: -1, flowId },
        invokeService: '@expense-manage:get:backlog:info:byId',
        params: record.id,
        showAllFeeType: true,
        buttons: [],
      },
      true,
    );
  };

  openEditBill = async (record: any) => {
    const resp = await app.invokeService('@expense-manage:get:backlog:info:byId', record.id);
    const flowId = resp.value;
    const userInfo = app.getState('@common.userinfo');
    app.open(
      '@bills:BillInfoEditePopup',
      {
        title: i18n.get('单据详情'),
        backlog: { id: -1, flowId },
        invokeService: '@expense-manage:get:backlog:info:byId',
        params: record.id,
        showAllFeeType: true,
        buttons: { [userInfo?.staff?.id]: ['freeflow.edit'] },
        callBack: () => {
          this.fetch();
          app.close();
        },
      },
      true,
    );
  };

  async saveScene(visibleColumns: any) {
    const filter = [JSON.stringify({ text: '全部', scene: 'all', sceneIndex: 'all', defaultColumns: visibleColumns })];
    return sceneFilterAction.POST('/$type', {
      type: 'CHECKING_BILL_SETTLEMENT_STATISTICS',
      filter,
    });
  }

  async getScene() {
    const { value } = await sceneFilterAction.GET('/$type', {
      type: 'CHECKING_BILL_SETTLEMENT_STATISTICS',
    });
    if (value?.filter?.length) {
      const [column] = value?.filter;
      return JSON.parse(column)?.defaultColumns?.map((col: string) => col.replace('form.', ''));
    }
    return [];
  }
}
