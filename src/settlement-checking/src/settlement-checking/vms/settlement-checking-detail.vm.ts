import { ICheckingBill } from '@ekuaibao/ekuaibao_types';
import {
  getFeeTypeStatistics,
  getInvoiceStatistics,
  launchChecking,
  getCheckingStatistics,
  confirmChecking,
  getSettlemetStatistics,
  getBillState,
  confirmCancelChecking,
} from '../../setttlement-checkin-action';
import { action, observable, IReactionPublic, toJS } from 'mobx';
import { statisticsColumns } from '../utils/settlement-checking-statistics';
import { ICheckingBillStatistics, IStatisticsValue } from '../../types/interface';

export class SettlementCheckingDetailVm {
  static NAME = Symbol('SETTLEMENT_CHECKING_DETAIL_VM');
  reaction: IReactionPublic;
  checkingBill: ICheckingBill;
  hideButton: boolean;
  legalEntityId: string;
  splitType: string;
  expenseDepartment: string;
  depParams: any;
  @observable isRefresh = false;
  @observable isCompleteChecking = false;
  init = (value: ICheckingBill) => {
    this.expenseDepartment = value?.expenseDepartment
    this.hideButton = value?.hideButton
    this.legalEntityId = value?.legalEntityId
    this.splitType = value?.splitType
    this.checkingBill = value;
  };
  clean = () => {
    this.reaction?.dispose();
  };
  getDatasource= () => toJS(this.feeTypeStatisticsDatasource)
  getDepParams = () => this.depParams
  getStateBill = () => {
    return getBillState({ id: this.checkingBill.id });
  };
  confirmCheckingBill = () => {
    return confirmChecking({ id: this.checkingBill.id });
  };
  confirmCancelCheckingBill = () => {
    return confirmCancelChecking({ id: this.checkingBill.id });
  };
  launchChecking = () => {
    return launchChecking({ id: this.checkingBill.id });
  };
  hasDepartment= () => this.checkingBill?.expenseDepartment?.length > 0 ?? false
  createParams = (legalEntityId: any) => {
    const params = { id: this.checkingBill?.id } as any
    if (legalEntityId) {
      params.legalEntityId=legalEntityId
    } else if(this.hasDepartment()){
      params.expenseDepartment = this.checkingBill?.expenseDepartment
    }
    return params
  }
  @action refreshData = async () => {
    this.isRefresh = !this.isRefresh;
    await Promise.all([
      this.getCheckingStatistics(),
      this.getFeeTypeStatistics(),
      this.getInvoiceStatistics(),
      this.getSettlementStatistics(),
    ]);
  };

  // 对账概览数据获取
  checkingStatisticsOriginData = {};
  @observable checkingStatisticsData: IStatisticsValue[] = [
    { name: i18n.get('子对账总数：0个'), value: 0, state: i18n.get('子对账单数量'), num: 0, type: 'total' },
    { name: i18n.get('待对账：0个'), state: i18n.get('待对账'), num: 0, value: 0, type: 'wait' },
    { name: i18n.get('对账中：0个'), state: i18n.get('对账中'), num: 0, value: 0, type: 'waiting' },
    { name: i18n.get('已对账：0个'), state: i18n.get('已对账'), num: 0, value: 0, type: 'waitEnd' },
  ];
  @action getCheckingStatistics = async (legalEntityId: any) => {
    const params = this.createParams(legalEntityId)
    const r = await getCheckingStatistics(params);
    this.checkingStatisticsOriginData = r.value;
    this.checkingStatisticsData = parseStatisticsData(r.value);
  };

  // 对账明细统计数据
  @observable feeTypeStatisticsDatasource = [];
  @observable feeTypeStatisticsColumns = [];
  @action getFeeTypeStatistics = async (legalEntityId: any, apportionsLegalEntityId?: string, depParams?:any) => {
    let params = this.createParams(apportionsLegalEntityId)
    if (depParams) {
      if (!!!params?.id) {
        this.feeTypeStatisticsDatasource = []
        return
      }
      this.depParams = depParams
      params = { ...params, ...depParams }
    }
    const r = await getFeeTypeStatistics(params);
    if (!r?.value) return;
    const firstCol = (this.expenseDepartment||depParams?.expenseDepartment)?{ name: i18n.get('部门'), type: 'text', data: {} }:{ name: i18n.get('法人实体'), type: 'text', data: {} }
    this.feeTypeStatisticsColumns = statisticsColumns(r?.value,  [ firstCol, { name: i18n.get('费用合计'), type: 'total', data: {} }]);
    if (legalEntityId && !apportionsLegalEntityId) {
      this.feeTypeStatisticsDatasource = r?.value?.row?.filter(e => e?.id === legalEntityId);
    } else {
      this.feeTypeStatisticsDatasource = r?.value?.row;
    }
  };

  // 开票概览数据
  @observable invoiceStatisticsDataSource = [];
  @observable invoiceStatisticsColumns = [];
  @observable hoseInvoiceStatisticsColumns = [];
  @observable invoiceStatisticsTable = [];
  @action getInvoiceStatistics = async (legalEntityId: any) => {
    const params = this.createParams(legalEntityId)
    const r = await getInvoiceStatistics(params);
    let col = r?.value?.col;
    if (!col?.length) {
      r.value.col = [{ name: i18n.get('金额') }];
    }
    this.invoiceStatisticsColumns = statisticsColumns(r.value, {
      name: i18n.get('状态'),
      width: 100,
      type: 'text',
      data: {},
    });
    this.hoseInvoiceStatisticsColumns = r?.value?.col;
    this.invoiceStatisticsDataSource = r?.value?.row;
    this.invoiceStatisticsTable = r?.value?.table;
  };

  // 结算概览数据
  @observable settlementStatisticsData: IStatisticsValue[] = [
    { name: i18n.get('结算总数：0个'), value: 0, state: i18n.get('结算总数'), num: 0, type: 'total' },
    { name: i18n.get('待结算：0个'), value: 0, state: i18n.get('待结算'), num: 0, type: 'wait' },
    { name: i18n.get('结算中：0个'), value: 0, state: i18n.get('结算中'), num: 0, type: 'waiting' },
    { name: i18n.get('已结算：0个'), value: 0, state: i18n.get('已结算'), num: 0, type: 'waitEnd' },
  ];
  @action getSettlementStatistics = async (legalEntityId: any) => {
    const params = this.createParams(legalEntityId)
    const r = await getSettlemetStatistics(params);
    this.settlementStatisticsData = parseSettleStatisticsData(r.value);
  };
}

const parseStatisticsData = (data: ICheckingBillStatistics) => {
  const { all, todo, doing, done } = data;

  return [
    { name: i18n.get('子对账总数：{__k0}个', { __k0: all?.total }), value: all?.amount, state: i18n.get('子对账单数量'),  num: all?.total, type: 'total' },
    { name: i18n.get('待对账：{__k0}个', { __k0: todo?.total }), value: todo.amount, state: i18n.get('待对账'), num: todo?.total, type: 'wait' },
    { name: i18n.get('对账中：{__k0}个', { __k0: doing?.total }), value: doing.amount, state: i18n.get('对账中'), num: doing?.total, type: 'waiting' },
    { name: i18n.get('已对账：{__k0}个', { __k0: done?.total }), value: done.amount, state: i18n.get('已对账'), num: done?.total, type: 'waitEnd' },
  ];
};

const parseSettleStatisticsData = (data: ICheckingBillStatistics) => {
  const { all, todo, doing, done } = data;
  return [
    {
      name: i18n.get('结算总数：{__k0}个', { __k0: all?.total }),
      value: all?.amount,
      state: i18n.get('结算数量'),
      num: all?.total,
      type: 'total'
    },
    {
      name: i18n.get('待结算：{__k0}个', { __k0: todo?.total }),
      value: todo?.amount,
      state: i18n.get('待结算'),
      num: todo?.total,
      type: 'wait'
    },
    {
      name: i18n.get('结算中：{__k0}个', { __k0: doing?.total }),
      value: doing?.amount,
      state: i18n.get('结算中'),
      num: doing?.total,
      type: 'waiting'
    },
    {
      name: i18n.get('已结算：{__k0}个', { __k0: done?.total }),
      value: done?.amount,
      state: i18n.get('已结算'),
      num: done?.total,
      type: 'waitEnd'
    },
  ];
};
