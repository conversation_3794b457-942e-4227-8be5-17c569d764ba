import { app } from '@ekuaibao/whispered';
import { TableVm } from '@ekuaibao/collection-definition';
import { action, IReactionPublic, observable, reaction } from 'mobx';
import { parseQuery2Select } from '@ekuaibao/lib/lib/parseQuery2Select';
import { getBillingPeriodList, getCheckingBillList, getSupplierArchiveList } from '../../setttlement-checkin-action';
import {
  IBillingPeriod,
  ISupplier,
  ICheckingBill,
  ESettlementState,
  ECheckingState,
  EInvoiceState,
} from '@ekuaibao/ekuaibao_types';
import { columns } from '../utils/checking-columns';
import { QuerySelect } from 'ekbc-query-builder';
import { IKeel, KEELVM } from '@ekuaibao/keel';
import { inject } from '@ekuaibao/react-ioc';
import { IReactionDisposer } from 'mobx/lib/internal';

const fetchFixer = app.require<any>('@elements/data-grid/fetchFixer').default;

export class SettlementCheckingListVm extends TableVm<ICheckingBill> {
  static NAME = Symbol('SETTLEMENT_CHECKING_LIST_VM');

  @inject(KEELVM) keel: IKeel;

  reaction?: IReactionPublic;
  disposer?: IReactionDisposer;
  columns = columns(this);

  @observable loading: boolean = false;
  @observable supplier?: string;
  @observable KA_ZY_Reconciliation_Settlement?: boolean;
  @observable supplierList: ISupplier[] = [];
  @observable billPeriod?: string;
  @observable billPeriodList: IBillingPeriod[] = [];
  @observable settlementValue?: ESettlementState;
  @observable invoiceValue?: EInvoiceState;
  @observable checkingValue?: ECheckingState;

  @action setSettlementValue = (value?: ESettlementState) => {
    this.settlementValue = value;
  };
  @action setInvoiceValue = (value?: EInvoiceState) => {
    this.invoiceValue = value;
  };
  @action setCheckingState = (value?: ECheckingState) => {
    this.checkingValue = value;
  };
  @action setSupplier = (value?: string) => {
    this.supplier = value;
  };
  @action setBillPeriod = (value?: string) => {
    this.billPeriod = value;
  };

  @action getData = async () => {
    this.loading = true
    const p = this.getParams();
    const result = await getCheckingBillList(p);
    this.loading = false
    this.dataSource = result.items;
    this.dataTotal = result.count;
  };

  @action getSupplier = async () => {
    const param = new QuerySelect().filterBy('active==true').select('id, name').value();
    const su = await getSupplierArchiveList(param);
    this.supplierList = su.items;
  };

  @action getPeriod = async () => {
    const param = new QuerySelect().filterBy('active==true').select('id, name').value();
    const pe = await getBillingPeriodList(param);
    this.billPeriodList = pe.items;
  };

  @action reset = () => {
    this.supplier = undefined;
    this.billPeriod = undefined;
    this.settlementValue = undefined;
    this.invoiceValue = undefined;
    this.checkingValue = undefined;
  };

  init = async () => {
    await Promise.all([await this.getSupplier(), await this.getPeriod(), this.getData()]);
  };

  initData() {
    this.disposer = reaction(
      () => [this.pageSize, this.sorters, this.pageMode, this.currentPage],
      () => {
        this.getData();
      },
    );
  }

  getParams = () => {
    const p = this.params();
    if (!p?.sorters?.['billPeriod.name']) {
      p.sorters['billPeriod.name'] = 'desc';
    }
    const param = fetchFixer(p);
    const query = parseQuery2Select(param);
    query.filterBy('feeTypeCount>0');
    query.select('supplierArchiveId(id, name),supplierAccountId(id, name),billPeriod(id, name),`...`');
    if (!!this.settlementValue) {
      query.filterBy(`settlementState=="${this.settlementValue}"`);
    }
    if (!!this.invoiceValue) {
      query.filterBy(`invoiceState=="${this.invoiceValue}"`);
    }
    if (!!this.checkingValue) {
      query.filterBy(`checkingState=="${this.checkingValue}"`);
    }
    if (!!this.billPeriod) {
      query.filterBy(`billPeriod=="${this.billPeriod}"`);
    }
    if (!!this.supplier) {
      query.filterBy(`supplierArchiveId=="${this.supplier}"`);
    }

    return query.value();
  };

  openDetail = async (line) => {
    await this.keel.open('SettlementCheckingDetail', { value: line });
  };
  clean = () => {
    this.reaction?.dispose();
    this.disposer && this.disposer();
  };
}

export const settlementState = [
  { label: i18n.get('未结算'), value: ESettlementState.NONE },
  { label: i18n.get('待结算'), value: ESettlementState.TODO },
  { label: i18n.get('结算中'), value: ESettlementState.DOING },
  { label: i18n.get('已结算'), value: ESettlementState.DONE },
];
export const invoiceState = [
  { label: i18n.get('未开票'), value: EInvoiceState.NONE },
  { label: i18n.get('待开票'), value: EInvoiceState.TODO },
  { label: i18n.get('开票中'), value: EInvoiceState.DOING },
  { label: i18n.get('已开票'), value: EInvoiceState.DONE },
];
export const checkingState = [
  { label: i18n.get('待对账'), value: ECheckingState.TODO },
  { label: i18n.get('对账中'), value: ECheckingState.DOING },
  { label: i18n.get('已对账'), value: ECheckingState.DONE },
];
