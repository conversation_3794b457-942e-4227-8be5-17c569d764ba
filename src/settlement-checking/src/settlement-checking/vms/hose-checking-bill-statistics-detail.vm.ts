import { ICheckingBillStatistics } from '../../types/interface';
import { GlobalFieldIF, ICheckingBill } from '@ekuaibao/ekuaibao_types';
import { TableVm } from '@ekuaibao/collection-definition';
import { action, computed, IReactionPublic, observable, reaction } from 'mobx';
import { billColumns, fixPath, option } from '../utils/hose-checking-bill-detail-columns';
import {
  deleteBillChecking,
  getBillIds,
  getCheckingStatistics,
  submitBillChecking,
} from '../../setttlement-checkin-action';
import { app, app as api } from '@ekuaibao/whispered';
import { mapping } from '../utils/mapping';

const fetchFixer = app.require<any>('@elements/data-grid/fetchFixer').default;
const { exportExcel } = app.require('@lib/export-excel-service');
import MessageCenter from '@ekuaibao/messagecenter';
import { Resource } from '@ekuaibao/fetch';
import { showMessage, showModal } from '@ekuaibao/show-util'
const sceneFilterAction = new Resource('/api/flow/v2/filter');

type ParamsTypes = {
  id: string
}

type RecordType = {
  id: string
  form: {
    title: string
  }
  plan: {
    taskId: string
    nodes?: ParamsTypes[]
  }
}

type CurrentNodeType = {
  counterSigners: string[]
  approverId: {
    name: string
  }
  type: 'countersign' | 'ebot'
}

export class HoseCheckingBillStatisticsDetailVm extends TableVm<any> {
  static NAME = Symbol('CHECKING_BILL_STATISTICS_DETAIL_VM');
  bus = new MessageCenter();
  reaction: IReactionPublic;
  URGE: number = 23; // 催办
  lastTime: number = 0
  userInfo = app.getState('@common.userinfo')
  @observable checkingBillInfo: ICheckingBill;
  @observable columns = [];
  @observable statisticsInfo: ICheckingBillStatistics;
  @observable selectedKeys: string[] = [];
  @observable search: string = '';
  @observable total: number = 0;
  @observable allFlowIds: string[] = [];
  @observable activeScene = 'CHECKING_ALL';
  @observable isSelectAll: boolean = false;
  pageSize: any = localStorage.getItem(`${this.userInfo.staff?.id}CheckingOverview`) ? Number(localStorage.getItem(`${this.userInfo.staff?.id}CheckingOverview`)) : this.pageSize
  tableColumnPropertyMap: Record<string, GlobalFieldIF> = {};

  @computed get allColumns() {
    return billColumns();
  }
  hasDepartment = () => this.checkingBillInfo?.expenseDepartment?.length > 0 ?? false;
  init = async (value: ICheckingBillStatistics, checkingBill: ICheckingBill, stackerManager?: any) => {
    this.statisticsInfo = value;
    this.checkingBillInfo = checkingBill;
    this.buildTableColumnMap();
    let columns = await this.getScene();
    if (!columns?.length) {
      columns = mapping.checkingBill;
    }
    this.createColumns(columns, stackerManager);
    reaction(
      () => [this.filters, this.sorters, this.search, this.pageSize, this.pageMode, this.currentPage, this.activeScene],
      async (data, reaction) => {
        this.reaction = reaction;
        await this.getBill();
      },
      {
        fireImmediately: true,
      },
    );
    await this.getCheckingStatisticsInfo();
  };

  buildTableColumnMap() {
    const allFields: GlobalFieldIF[] = app.getState('@common.globalFields.data');
    allFields.forEach((field) => {
      this.tableColumnPropertyMap[`${fixPath}.${field.name}`] = field;
    });
  }

  refreshData = async () => {
    await Promise.all([this.getCheckingStatisticsInfo(), this.getBill()]);
  };

  getCheckingStatisticsInfo = async () => {
    const params = { id: this.checkingBillInfo?.id };

    if (this.hasDepartment()) {
      params.expenseDepartment = this.checkingBillInfo?.expenseDepartment;
    } else if (this.checkingBillInfo?.splitType === 'MACH') {
      params.legalEntityId = this.checkingBillInfo?.legalEntityId;
    }
    const statisticsInfo = await getCheckingStatistics(params);
    this.statisticsInfo = statisticsInfo.value;
  };

  submitChecking = () => {
    return submitBillChecking({ checkingBillId: this.checkingBillInfo.id, flowIds: this.isSelectAll ? this.allFlowIds : this.selectedKeys });
  };

  deleteChecking = () => {
    return deleteBillChecking({ checkingBillId: this.checkingBillInfo.id, flowIds: this.isSelectAll ? this.allFlowIds : this.selectedKeys });
  };

  updateColumns(columns: string[]) {
    const fields = columns.map((item) => {
      const fieldStr = Array.isArray(item) ? item.join('.') : item
      return fieldStr.replace(`${fixPath}.`, '')
    });
    this.createColumns(fields);
  }

  resetColumns() {
    this.createColumns(mapping.checkingBill);
  }

  handleRemind = (record: RecordType) => {
    const title: string = record?.form?.title || ''
    const id = record?.id || ''
    const taskId = record?.plan?.taskId || ''
    showModal.confirm({
      className: 'confirmCopyModal-wrapper',
      title: i18n.get('发送催办消息'),
      content: i18n.get(
        `系统将发送一条消息提醒 {__k0} 审批。不建议频繁使用此功能催促审批人。若长时间没有审批，建议通过电话等其他联系方式联系审批人。`,
        { __k0: this.getApproveMember(record) },
      ),
      okText: i18n.get('确认'),
      cancelText: i18n.get('取消'),
      onOk: () => {
        const newTime = new Date().valueOf()
        if (newTime - this.lastTime > 60000) {
          api.invokeService('@bills:bill:reminde', id, taskId).then(this.reloadData)
          api.invokeService('@common:insert:assist:record', {
            title: `催办${title}单据`,
          })
          this.lastTime = newTime
        } else {
          showMessage.warning(i18n.get('操作频繁'))
        }
      },
    })
  }

  handleAllRemind = (list: any, selectedKeys: string[]) => {
    showModal.confirm({
      className: 'confirmCopyModal-wrapper',
      title: i18n.get('发送催办消息'),
      content: i18n.get(
        `系统将发送一条消息提醒 {__k0} 审批。不建议频繁使用此功能催促审批人。若长时间没有审批，建议通过电话等其他联系方式联系审批人。`,
        { __k0: this.getAllApproveMember(list) },
      ),
      okText: i18n.get('确认'),
      cancelText: i18n.get('取消'),
      onOk: () => {
        const newTime = new Date().valueOf()
        if (newTime - this.lastTime > 60000) {
          api.invokeService('@bills:bill:allReminde', selectedKeys).then(this.reloadData)
          api.invokeService('@common:insert:assist:record', {
            title: `催办批量单据`,
          })
          this.lastTime = newTime
        } else {
          showMessage.warning(i18n.get('操作频繁'))
        }
      },
    })
  }

  reloadData = () => {
    api.invokeService('@layout5:refresh:menu:data')
  }

  getAllApproveMember = (line: any) => {
    let staffNames: any = new Set(line.map((item: any) => item?.nodeState?.staffName?.split(','))?.flat())
    staffNames = [...staffNames]
    return i18n.get(`{__k0}等{__k1}人`, {
      __k0: staffNames.slice(0, 10).join(),
      __k1: staffNames.length,
    })
  }

  getApproveMember = (line: RecordType) => {
    const { plan } = line
    const { taskId, nodes } = plan
    const currentNode: CurrentNodeType = nodes?.find((node: ParamsTypes) => node.id === taskId) as any
    if (currentNode.type === 'countersign') {
      const approvingSigners = currentNode.counterSigners
        .filter((item: any) => item.state === 'APPROVING' || item.state === null)
        .map((item: any) => item.signerId.name)
      return i18n.get(`{__k0}等{__k1}人`, {
        __k0: approvingSigners.slice(0, 10).join(),
        __k1: approvingSigners.length,
      })
    } else if (currentNode.type === 'ebot') {
      return 'Ebot'
    } else if (currentNode.type === 'invoicingApplication') {
      return i18n.get("开票申请")
    }  else {
      return currentNode.approverId ? currentNode.approverId.name : i18n.get('未选择')
    }
  }

  @action getBill = async () => {
    const params = { id: this.checkingBillInfo.id };

    if (this.hasDepartment()) {
      params.expenseDepartment = this.checkingBillInfo?.expenseDepartment;
    } else if (this.checkingBillInfo?.splitType === 'MACH') {
      params.legalEntityId = this.checkingBillInfo?.legalEntityId;
    }
    if (!this.allFlowIds?.length) {
      const ids = await getBillIds(params);
      this.allFlowIds = ids?.items;
      this.total = this.allFlowIds?.length;
    }
    if (this.allFlowIds?.length) {
      const params = this.buildParams(this.allFlowIds);
      params.formType = 'checking';
      const result = await app.invokeService('@expense-manage:search:flow:by:filter', {
        params,
        otherParams: { join$100: 'id,generateError,/flow/v2/flows/error' },
      });
      this.dataSource = result.items;
      this.total = result.count
    } else {
      this.dataSource = [];
      this.total = 0
    }
  };

  @action createColumns = (fields?: string[], stackerManager?: any) => {
    this.columns = [...billColumns(fields), option(this, stackerManager)];
  };

  @action cleanSelectedKeys = () => {
    this.selectedKeys = [];
  };

  buildParams(ids: string[]) {
    const p = this.params();
    p.filters = p.filters ? p.filters : {};
    const stateMap: Record<string, string[]> = {
      CHECKING_ALL: [],
      CHECKING_TODO: ['draft', 'rejected', 'nullify'],
      CHECKING_DOING: ['approving', 'pending', 'paying', 'failure'],
      CHECKING_DONE: ['paid', 'archived'],
    };
    const sceneStates = stateMap[this.activeScene];
    p.filters.state = p.filters.state ? p.filters.state : [];
    p.filters.state = p.filters.state.concat(sceneStates);
    p.sorters = p.sorters ? p.sorters : {};
    p.sorters = { ...p.sorters, id: 'ascend' };
    const fixP = fetchFixer(p, this.tableColumnPropertyMap);
    const idsStr = ids?.map((item: string) => `"${item}"`);
    const page = fixP?.page || {};
    let search = `id.in(${idsStr})`
    if (this.search?.trim()?.length) {
      search = `((form.title.contains(\"${this.search}\"))|| (lower(form.code).contains(lower(\"${this.search}\")))) && id.in(${idsStr})`
    }
    return {
      status: { state: undefined },
      ...fixP,
      page,
      otherFilters: [search],
    };
  }

  openBillDetail = async (record: any, stackerManager?: any) => {
    const riskDataParams = ['draft', 'rejected'].includes(record?.state)
      ? { id: record.id, level: 'OutOfLimitReject' }
      : { id: record.id };
    const [resp, riskData] = await Promise.all([
      app.invokeService('@expense-manage:get:backlog:info:byId', record.id),
      app.invokeService('@bills:get:flow:risk:warning', riskDataParams),
    ]);
    const flowId = resp.value;
    if (stackerManager) {
      return stackerManager?.push('BillInfoView', {
        dataSource: flowId,
        riskData,
      });
    }
    const bottomToolbar = ['freeflow.printed']
    record?.state === 'approving' && bottomToolbar.push('freeflow.urge')
    app.open(
      '@bills:BillInfoPopup',
      {
        title: i18n.get('单据详情'),
        backlog: { id: -1, flowId },
        invokeService: '@expense-manage:get:backlog:info:byId',
        params: record.id,
        riskData,
        showAllFeeType: true,
        bottomToolbar,
        onFooterButtonsClick: (type: number, line: any) => {
          if(this.URGE === 23) {
            this.handleRemind(line?.flowId)
          }
        }
      },
      true,
    );
  };

  openEditBill = async (record: any) => {
    const [resp, riskData] = await Promise.all([
      app.invokeService('@expense-manage:get:backlog:info:byId', record.id),
      app.invokeService('@bills:get:flow:risk:warning', {
        id: record.id,
        level: 'OutOfLimitReject',
      }),
    ]);
    const flowId = resp.value;
    const userInfo = app.getState('@common.userinfo');
    app.open(
      '@bills:BillInfoEditePopup',
      {
        title: i18n.get('单据详情'),
        backlog: { id: -1, flowId },
        invokeService: '@expense-manage:get:backlog:info:byId',
        params: record.id,
        showAllFeeType: true,
        riskTip: riskData,
        buttons: { [userInfo?.staff?.id]: ['freeflow.edit'] },
        callBack: () => {
          this.getBill();
          app.close();
        },
      },
      true,
    );
  };
  onExport = async () => {
    const resp = await getBillIds({ id: this.checkingBillInfo.id });
    if (resp?.items?.length) {
      return exportExcel(
        {
          exportType: 'export_selected',
          data: { keys: resp.items },
          showAllFeeType: true,
          needAsyncExport: true,
          noFlow: false,
          unNeedState: true,
          others: { isManage: true },
          otherfilter: `id.in(${resp.items.map((v: string) => `"${v}"`).join(',')})`,
        },
        this.bus,
      );
    }
  };

  openErrorLog = (value: string[]) => {
    app.open('@bills:LogModal', {
      title: i18n.get('错误日志'),
      hasPagination: false,
      showHistoryIcon: false,
      onGetLog: async () => {
        const result: any = { items: [] };
        if (value?.length) {
          result.items = value.map((msg: string) => {
            return { __left: msg };
          });
        }
        return result;
      },
    });
  };

  clean = () => {
    this.reaction?.dispose();
  };

  async saveScene(visibleColumns: any) {
    const filter = [JSON.stringify({ text: '全部', scene: 'all', sceneIndex: 'all', defaultColumns: visibleColumns })];
    return sceneFilterAction.POST('/$checkingBillStatistics', {
      checkingBillStatistics: 'CHECKING_BILL_STATISTICS',
      filter,
    });
  }

  async getScene() {
    const { value } = await sceneFilterAction.GET('/$checkingBillStatistics', {
      checkingBillStatistics: 'CHECKING_BILL_STATISTICS',
    });
    if (value?.filter?.length) {
      const [column] = value?.filter;
      return JSON.parse(column)?.defaultColumns?.map((col: string) => col?.replace?.('form.', ''));
    }
    return [];
  }
}
