/**
 *  Created by pw on 2021/6/8 下午5:21.
 */
import { TableVm } from '@ekuaibao/collection-definition';
import {action, computed, IReactionDisposer, IReactionPublic, observable, reaction} from 'mobx';
import { Column } from '@ekuaibao/datagrid/esm/types/column';
import { Fetch, Resource } from '@ekuaibao/fetch';
import {
  apportionColumns,
  detailColumns,
  fixedPath,
  getColumnPropertyMapping,
  noneType,
} from '../utils/HoseFeeDetailColumns';
import { app } from '@ekuaibao/whispered';
import parseQuery2Select from '@ekuaibao/lib/lib/parseQuery2Select';
import { ECheckingState, GlobalFieldMapIF, ICheckingBill, ICheckingBillDetail } from '@ekuaibao/ekuaibao_types';
import { inject } from '@ekuaibao/react-ioc';
import { SettlementCheckingDetailVm } from './settlement-checking-detail.vm';
import { CheckingDetailTab, FeeDetailFilterType } from '../../types/enums';
import { showMessage } from '@ekuaibao/show-util';

const checkingAction = new Resource('/api/checking/v2');
const dimensionItems = new Resource('/api/v1/basedata/dimensionItems');
const checkingActionV2 = new Resource('/api/v2/checking');
const exportAction = new Resource('/api/checking/v2/detail/export/async');
const sceneFilterAction = new Resource('/api/flow/v2/filter');
const exportDepartmentDetail = new Resource('/api/checking/v2/detail/department/export/async');// 部门对账列表导出
const { selectFeeTypeForm } = app.require('@lib/entity-select-join');

const fetchFixer = app.require<any>('@elements/data-grid/fetchFixer').default;
type IColumnOther={
  canExport:boolean
}
export class HoseFeeDetailVm extends TableVm<ICheckingBillDetail> {
  static NAME = Symbol('CHECKING_FEE_DETAIL_LIST');

  @inject(SettlementCheckingDetailVm.NAME) checkingDetailVm: SettlementCheckingDetailVm;

  defaultColumns: string[] = [];
  reaction: IReactionPublic;
  @observable checkingBill: ICheckingBill;
  @observable columns: (Column & IColumnOther)[] = [];
  @observable search: string = '';
  @observable activeScene = 'all';
  @observable groupDimension: any[] = []
  @observable selectedKeys: string[] = [];
  @observable tabType: CheckingDetailTab;
  @observable updateTabColumn: any;
  @observable allDataTotal: number;
  @observable defaultScene: string[] = [];
  @observable settlementCharge: boolean = false;
  @observable filterFeeDetailByType: FeeDetailFilterType = FeeDetailFilterType.ALL;
  @observable isDep?: boolean // 部门科室对账
  staffs = [];
  dimensionItemMap = {}; // 自定义档案
  public disposer: IReactionDisposer | null = null;
  async init(value: ICheckingBill, isDep: boolean = false, sourceType: string = '') {
    this.checkingBill = value;
    await this.fetchColumn();
    this.disposer = reaction(
      () => [
        this.currentPage,
        this.pageSize,
        this.search,
        this.sorters,
        this.filters,
        this.tabType,
        this.checkingDetailVm.isRefresh,
        this.filterFeeDetailByType,
      ],
      (data, reaction) => {
        this.reaction = reaction;
        this.fetch(isDep);
      },
      { fireImmediately: true },
    );
  }
  getDimensionItemByDimensionId = (id: string) => {
    const param = { distinct: '', orderBy: [] };
    return dimensionItems.POST('/search/$id', { id, param });
  };
  async getDimensionItems(fields: string[] = []) {
    const globalFieldMap: GlobalFieldMapIF = app.getState('@common.globalFields.baseDataPropertiesMap') as any;
    const promiseArr: any[] = [];
    fields.forEach((el) => {
      const dataType = globalFieldMap?.[el]?.dataType;
      if (
        dataType?.type === 'list' &&
        dataType?.elemType?.type === 'ref' &&
        dataType?.elemType?.entity?.startsWith('basedata.Dimension.')
      ) {
        const id = Fetch.ekbCorpId + ':' + dataType?.elemType?.entity?.split('basedata.Dimension.')[1];
        promiseArr.push(this.getDimensionItemByDimensionId(id));
      }
    });
    if (promiseArr.length) {
      const res = await Promise.all(promiseArr);
      const param = {};
      res.forEach((el: any) => {
        if (el?.items?.length) {
          const dimensionStr = el?.items[0]?.dimensionId?.split(':')[1];
          if (dimensionStr) {
            const key = 'basedata.Dimension.' + dimensionStr;
            param[key] = el?.items;
          }
        }
      });
      this.dimensionItemMap = param;
    }
  }

  async fetchColumn() {
    this.staffs = await app.dataLoader('@common.staffs').load();
    const { items } = await checkingAction.GET('/detail/fields');
    await this.getDimensionItems(items);
    this.columns = detailColumns(items, this.staffs, this.dimensionItemMap);
    this.updateTabColumn && this.updateTabColumn();
  }

  async fetchApportionColumn(id: string) {
    this.staffs = await app.dataLoader('@common.staffs').load();
    const { items } = await checkingAction.GET('/detail/searchApportions/$id', { id });
    const apportionsColumns = apportionColumns(items || [], this.staffs);
    return { columns: apportionsColumns };
  }

  buildParams() {
    let p = this.params();
    if (this.settlementCharge && this.groupDimension && this.groupDimension?.length > 0) {
      //不是默认的5中类型，一次性差全部数据
      p.page = {
        currentPage: 1,
        pageSize: 10000,
      };
    }
    const filterKeys = Object.keys(p.filters ?? {});
    if (filterKeys.includes('form.feeTypeId')) {
      p = {
        ...p,
        filters: {
          'form.feeTypeId.name': p.filters['form.feeTypeId'],
          ...p.filters,
        },
      };
      delete p.filters['form.feeTypeId'];
    }

    const param = fetchFixer(p, getColumnPropertyMapping());
    const query = parseQuery2Select(param);
    const feeTypeForm = selectFeeTypeForm();
    const globalFieldMap: GlobalFieldMapIF = app.getState('@common.globalFields.baseDataPropertiesMap');

    if (this.tabType === CheckingDetailTab.NO_CHECKING_DETAIL) {
      query.filterBy('active==false');
    } else {
      query.filterBy('active==true');
    }
    if (this.filterFeeDetailByType === FeeDetailFilterType.IncompleteFee) {
      query.filterBy('incomplete==true');
    }
    query.select(`flowId(state),requisitionId(...),form(${feeTypeForm},feeTypeId(...), specificationId(...),...),...`);
    if (this.search?.trim()?.length) {
      query.filterBy(`${fixedPath}expenseDepartment.name.contains("${this.search}")`);
    }
    if (this?.checkingBill?.expenseDepartment?.length > 0) {
      query.filterBy(`form.feeTypeForm.expenseDepartment.id=="${this?.checkingBill?.expenseDepartment}"`);
    } else if (this?.checkingBill?.splitType === 'MACH' && globalFieldMap?.['u_对账法人实体']) {
      if (this?.checkingBill?.legalEntityId === 'NONE') {
        query.filterBy(`isNUll(form.feeTypeForm.u_对账法人实体)`);
      } else {
        query.filterBy(`form.feeTypeForm.u_对账法人实体.id=="${this?.checkingBill?.legalEntityId}"`);
      }
    }
    return [
      { ...query.value(), id: this.checkingBill.id },
      {
        join: 'form.feeTypeForm.invoiceForm.invoiceCorporationId,invoiceCorporation,/v2/invoice/unify/corporation/list',
        join$1: `form.feeTypeForm.invoiceForm.attachments.fileId,fileId,/v1/attachment/attachments`,
        join$2: `form.feeTypeForm.orders,thirdPartyOrders,/v2/order/orders`,
        join$3: 'form.feeTypeForm.orders,ordersData,/v1/order/orders',
        join$4:
          'form.feeTypeForm.linkDetailEntities.linkDetailEntityId,linkDetailEntityId,/form/v3/requisition/info/details/byId?join=feeTypeId,feeTypeId,/v1/form/feeTypes',
      },
    ];
  }

  @computed get defaultVisibleColumns(): string[] {
    this.defaultColumns = this.columns.filter(this.filter).map((column) => column.dataIndex!);
    return this.defaultColumns;
  }

  @computed get allColumns() {
    const globalFieldMap: GlobalFieldMapIF = app.getState('@common.globalFields.baseDataPropertiesMap');
    const fields = Object.keys(globalFieldMap).filter((item) => !!item);
    return detailColumns(fields, this.staffs, this.dimensionItemMap).filter(this.filter);
  }

  filter = (item) => {
    return item.dataIndex !== 'form.feeTypeId' && item.type !== noneType;
  };

  checkData = (title = '') => {
    const selectData = this.dataSource.filter((item) => this.selectedKeys.includes(item.id));
    const modifyAbleData = selectData.filter((item) => item.checkingState === ECheckingState.TODO);
    if (!modifyAbleData.length) {
      return [0, i18n.get('选择的数据中没有待对账的数据，不能{__k0}', { __k0: title })];
    }
    const unModifyDataLength = this.selectedKeys.length - modifyAbleData.length;
    const message = unModifyDataLength
      ? i18n.get('选择的数据中有{__k0}条待对账数据可以{__k1}，另外{__k2}条不可以{__k1}系统将自动过滤', {
          __k0: modifyAbleData.length,
          __k1: title,
          __k2: unModifyDataLength,
        })
      : '';
    return [modifyAbleData.length, message, modifyAbleData.map((item) => item.id)];
  };

  updateColumns(columns: string[], groupDimension?: string[]) {
    this.columns = detailColumns(columns, this.staffs, this.dimensionItemMap, groupDimension);
    this.groupDimension = groupDimension
  }

  resetColumns() {
    this.columns = detailColumns(this.defaultColumns, this.staffs, this.dimensionItemMap);
  }

  unCheckingBill = (keys) => {
    return checkingAction.PUT('/detail/disable/batch', { ids: keys });
  };

  restoreCheckingBill = () => {
    return checkingAction.PUT('/detail/restore/batch', { ids: this.selectedKeys });
  };

  fetch = async (isDep = false) => {
    const [params] = this.buildParams();
    let path = '/detail/$id';
    if (isDep) {
      path = '/detail/departmenDetail/$id';
    }
    if (this.filterFeeDetailByType === FeeDetailFilterType.DisabledFee) {
      path = '/detail/disable/$id';
    }
    const { total, items } = await checkingAction.POST(path, params);
    this.dataSource = items;
    this.dataTotal = total;
  };

  modifyFeeDetail = (data) => {
    return checkingAction.PUT('/detail', data);
  };

  getFeeDetailInfo = (feeTypeId: string) => {
    const [params, join] = this.buildParams();
    return checkingActionV2.POST('/detail/$id', { ...params, id: feeTypeId }, join);
  };

  onExport = async (legalEntityName?: string) => {
    const val: any = await app.open('@layout:AsyncExportModal', { isShowApportions: true });
    let params: any = {
      checkingBillId: this.checkingBill.id,
      taskName: val?.taskName,
      active: this.tabType !== CheckingDetailTab.NO_CHECKING_DETAIL,
      includeApportions: val?.includeApportions || false,
      exportHead: this.getColParams ?? [],
    };
    if (legalEntityName) {
      params.legalEntityName = legalEntityName;
    }
    if (this.isDep) {
      if (!!!this.dataSource.length) {
        return showMessage.error(i18n.get("数据为空，无法导出"))
      }
      params = {...params, ...this.depExportParams()}
      return exportDepartmentDetail.POST('', params)
    }
    return exportAction.POST('', params);
  };
  depExportParams = () => {
    const [params] = this.buildParams();
    const {id, ...query} = params
    return {id, query}
  }
 @computed get getColParams () {
    return this.columns?.filter(it => it.canExport)?.map((it: any) => ({ label: it.label, name: Array.isArray(it.dataIndex) ? it?.dataIndex?.join('.') : it.dataIndex }))
  }
  clean = () => {
    this.reaction?.dispose();
  };

  async saveScene(visibleColumns: any) {
    const filter = [JSON.stringify({ text: '全部', scene: 'all', sceneIndex: 'all', defaultColumns: visibleColumns })];
    return sceneFilterAction.POST('/$feeDetailStatistics', {
      feeDetailStatistics: 'FEE_DETAIL_STATISTICS',
      filter,
    });
  }

  async getScene() {
    const { value } = await sceneFilterAction.GET('/$feeDetailStatistics', {
      feeDetailStatistics: 'FEE_DETAIL_STATISTICS',
    });
    if (value?.filter?.length) {
      const [column] = value?.filter;
      return JSON.parse(column)
        ?.defaultColumns?.map((col: string) => col.replace('feeTypeForm.', '').replace('form.', ''))
        ?.filter((column: string) => column !== 'feeTypeId');
    }
    return [];
  }

  @action
  handleFilterChange = (filter: Record<string, any>) => {
    this.currentPage = 1
    this.filters = filter;
  }

}

export interface IScene {
  active?: boolean;
  scene: string;
  sceneIndex: string;
  text: string;
  defaultColumns: string[];
  condition?: string;
  filters?: any[];
  sorter?: any;
}
