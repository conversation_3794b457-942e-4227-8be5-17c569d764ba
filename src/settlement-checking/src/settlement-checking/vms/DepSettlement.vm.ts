/**
 * @description DepSettlementTable 的数据仓库
 * create by zhang<PERSON>g on 2022/1/13
 */

import { observable, action } from 'mobx';
import { Resource } from '@ekuaibao/fetch';
import { TableVm } from '@ekuaibao/collection-definition';
import { app } from '@ekuaibao/whispered';
import { ICheckingBillDetail } from '@ekuaibao/ekuaibao_types';
import { Map } from '@ekuaibao/datagrid/lib/types/utils';
const departmentCheck = new Resource('/api/checking/v1');

export class DepSettlementVm extends TableVm<ICheckingBillDetail> {
  static NAME = Symbol('DEPARTMENT_CHECKING_BILL');
  @observable sorters: Map<'ascend' | 'descend'>
  @observable test: any = 1;
  @observable dataSource: any[] = [];
  @observable pageData: any[] = [];
  @observable total: number = 0;

  defaultColumns: any = [
    {
      title: '部门名称',
      dataIndex: 'departmentName',
      minWidth: 200,
    },

    {
      title: '对账人',
      dataIndex: 'checkerName',
      minWidth: 200,
    },
    {
      title: '部门对账单条数',
      dataIndex: 'checkerTotal',
      minWidth: 200,
    },
    {
      title: '部门对账金额',
      dataIndex: 'amountTotal',
      minWidth: 200,
    },
    {
      title: '状态',
      dataIndex: 'stateName',
      minWidth: 200,
    },
  ];
  persionNameList: any = [];
  departmentNameList: any = [];
  search: string;

  async init(value: any) {
    this.getStateValue();
    const { items } = await departmentCheck.GET('/deptChecking/$id', { id: value.id });
    this.total = items.length;
    this.dataSource = this.buildList(items);
    this.handlePage();
  }

  async refrshStatus(value: any) {
    const { items } = await departmentCheck.GET('/deptChecking/refresh/$id', { id: value.id });
    this.total = items.length;
    this.dataSource = this.buildList(items);
    this.handlePage();
  }

  // 从仓库中拿人员和部门信息
  async getStateValue() {
    const back = await app.dataLoader('@common.department').load();
    this.departmentNameList = back.list;
    this.persionNameList = await app.dataLoader('@common.staffs').load();
  }

  // 处理返回的数据
  buildList(list: any[]) {
    const statusList: { [propname: string]: string } = {
      COMPLETED: '已完成',
      INCOMPLETE: '未完成',
      NO_HANDLE: '无需对账',
    };
    const back = list.map((item: any) => {
      item.departmentName =
        this.departmentNameList.find((nameItem: any) => item.departmentId === nameItem.id)?.name ?? '';
      item.checkerName = this.buildNameList(item.checkerId).toString();
      item.stateName = statusList[item.state];
      return item;
    });
    return back;
  }

  // 把对账人的ID转换成中文
  buildNameList(list: string[]) {
    const back: string[] = [];
    list.forEach((item: any) => {
      const name = this.persionNameList.find((nameItem: any) => item === nameItem.id)?.name ?? '';
      if (name) {
        back.push(name);
      }
    });
    return back;
  }

  // 处理翻页
  @action handlePage() {
    let startNumber: number, endNumber: number;

    // 有搜索的情况
    if (this.search) {
      startNumber = this.pageSize * (this.currentPage - 1);
      endNumber = startNumber + this.pageSize;
      const back = this.dataSource.filter((item) => item.departmentName.indexOf(this.search) > -1);
      if (this.pageMode === 'pagination') {
        this.pageData = back.slice(startNumber, back.length > endNumber ? endNumber : back.length);
      } else {
        this.pageData = back
      }
      return;
    }

    // 无搜索的情况
    startNumber = this.pageSize * (this.currentPage - 1);
    endNumber = startNumber + this.pageSize;
    if (this.pageMode === 'pagination') {
      this.pageData = this.dataSource.slice(
        startNumber,
        this.dataSource.length > endNumber ? endNumber : this.dataSource.length,
      );
    } else {
      this.pageData = this.dataSource
    }
  }

  // 搜索方法
  @action searchValue() {
    this.currentPage = 1;
    const back = this.dataSource.filter((item) => item.departmentName.indexOf(this.search) > -1);
    this.total = back.length;
    this.pageData = back;
  }

  // 清除搜索
  @action clearPageData() {
    this.total = this.dataSource.length;
    this.handlePage();
  }
}
