import { action, observable } from 'mobx';
import { app } from '@ekuaibao/whispered';
import { Fetch } from '@ekuaibao/fetch';
import { batchModify } from '../../setttlement-checkin-action';

export interface IModifyRule {
  key?: string;
  value?: string;
  options: any[] | string;
}

const initValue = { key: undefined, value: undefined, options: [] };

export class BatchModifyFeeTypeVm {
  static NAME = Symbol('BATCH_MODIFY_FEETYPE_VM');

  private valueSelctCatch: any = {};

  selectedKeys = [];
  @observable checkedStaff = new Map()
  @observable globalFields = [];
  @observable modifyRules: IModifyRule[] = [initValue];
  @action initValue = async (selectedKeys: string[] = [], selectedData: any[]) => {
    this.selectedKeys = selectedKeys;
    await this.getGlobalFields(selectedData);
  };

  @action setModifyRules = (value: IModifyRule[]) => {
    this.modifyRules = value;
  };

  @action addRule = (index) => {
    const orRules = [...this.modifyRules];
    orRules.splice(index + 1, 0, initValue);
    this.modifyRules = orRules;
  };

  @action delRule = (index) => {
    const orRules = [...this.modifyRules];
    orRules.splice(index, 1);
    this.modifyRules = orRules;
  };

  @action ruleChange = async (rule, index) => {
    const options = await this.getValueSelect(rule);
    rule.options = options;
    const orRules = [...this.modifyRules];
    orRules.splice(index, 1, rule);
    this.modifyRules = orRules;
  };

  saveValue = () => {
    const rules = this.modifyRules.filter((item) => !!item.key && !!item.value);
    if (!rules.length) return Promise.reject({ message: i18n.get('没有需要保存的字段') });
    const result: { [key: string]: string } = {};
    rules.forEach((item) => {
      result[item.key] = item.value;
    });

    return batchModify({ ids: this.selectedKeys, values: result });
  };

  getGlobalFields = async (selectedData) => {
    if (!selectedData?.length) {
      this.globalFields = []
      return
    }
    const globalFieldsMap = app.getState()['@common'].globalFields.baseDataPropertiesMap;
    const fields: any[] = selectedData.reduce((acc: any[], item: any) => {
      item?.form?.specificationId?.components?.forEach?.((cp: any) => {
        const field: any = globalFieldsMap[cp.field]
        if (field && !acc.find(i => i.name === cp.field)) {
          acc.push(field)
        }
      });
      return acc
    }, [])

    this.globalFields = fields.filter(
      (item) =>
        item.dataType?.entity === 'organization.Department' || item.dataType?.entity?.startsWith('basedata.Dimension') || item.dataType?.entity === 'organization.Staff' || (item.dataType?.type === 'list' && item?.dataType?.elemType?.entity === 'organization.Staff')
    );
  };

  getValueSelect = async (item: IModifyRule) => {
    if (!item?.key) return [];
    if (!!this.valueSelctCatch[item.key]) {
      return this.valueSelctCatch[item.key];
    }
    const field = this.globalFields.find((line) => line.name === item.key);
    if (field?.dataType?.entity === 'organization.Department') {
      const department = await app.dataLoader('@common.department').load();
      this.valueSelctCatch[item.key] = department.data;
      return department.data;
    }
    if (field?.dataType?.entity?.startsWith('basedata.Dimension')) {
      const ref = field.dataType.entity.split('.');
      const dimension = ref[ref.length - 1];
      const { items } = await app.invokeService('@custom-dimension:get:DimensionItems', {
        id: `${Fetch.ekbCorpId}:${dimension}`,
      });
      this.valueSelctCatch[item.key] = items;
      return items;
    }
    if (field?.dataType?.entity === 'organization.Staff' || field?.dataType?.elemType?.entity === 'organization.Staff') {
      return field?.dataType?.elemType?.entity === 'organization.Staff' ? 'isMultiStaff' : 'isStaff';
    }
    return [];
  };
}
