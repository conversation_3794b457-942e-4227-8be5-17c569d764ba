@import '~@ekuaibao/eui-styles/less/token';

.settlement-checking-list-content {
  background-color: var(--eui-bg-base);
}

.settlement-checking-list-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 12px;
  border-radius: 8px;
  background-color: var(--eui-bg-body);

  :global {
    .site-page-header {
      background-color: var(--eui-bg-body);
      border-radius: 8px 8px 0 0;
    }

    .d-f {
      display: flex;
    }

    .filter-wrapper {
      padding:0 16px;

      .w-960 {
        width: 1000px;
      }

      label {
        .mr-12;
      }

      .jc-s {
        justify-content: flex-start !important;
      }

      .item {
        display: block;
        padding-left: 30px;
        width: 30%;
        align-items: center;
        display: flex;
        justify-content: flex-end;
      }
    }

    .mr-12 {
      margin-right: @space-5;
    }

    .ml-8 {
      margin-left: @space-4;
    }

    .mb-16 {
      margin-bottom: @space-6;
    }

    .list-wrapper {
      background-color: #fff;
      flex: 1;
      flex-direction: column;
      display: flex;
      padding: @space-6;

      .ta-r {
        text-align: right;
      }
    }
  }
}