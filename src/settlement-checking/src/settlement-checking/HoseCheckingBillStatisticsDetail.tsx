import React from 'react';
import styles from './HoseCheckingBillStatisticsDetail.module.less';
import HoseCheckingBillStatisticsDetailOverview from './components/HoseCheckingBillStatisticsDetailOverview';
import HoseCheckingBillStatisticsList from './components/HoseCheckingBillStatisticsList';
import { ICheckingBillStatistics } from '../types/interface';
import { ICheckingBill } from '@ekuaibao/ekuaibao_types';
import { inject, provider } from '@ekuaibao/react-ioc';
import { HoseCheckingBillStatisticsDetailVm } from './vms/hose-checking-bill-statistics-detail.vm';
import CheckingCommonHeader from './components/HoseCheckingCommonHeader';

interface CheckingBillStatisticsDetailProps {
  value: ICheckingBillStatistics;
  checkingBill: ICheckingBill;
}
interface CheckingBillStatisticsDetailState { }
@provider([HoseCheckingBillStatisticsDetailVm.NAME, HoseCheckingBillStatisticsDetailVm])
export default class HoseCheckingBillStatisticsDetail extends React.Component<
CheckingBillStatisticsDetailProps,
CheckingBillStatisticsDetailState
> {
  @inject(HoseCheckingBillStatisticsDetailVm.NAME) vm: HoseCheckingBillStatisticsDetailVm;
  componentDidMount() {
    const { value, checkingBill, stackerManager } = this.props;
    this.vm.init(value, checkingBill, stackerManager);
  }
  componentWillUnmount() {
    this.vm.clean();
  }

  handleBack = (index: number) => {
    this.props.keel?.closeTo(index);
  }

  render() {
    return (
      <div className={styles['hose-checking-bill-statistics-detail-wrapper']}>
        <div className="detail-title" >
          <CheckingCommonHeader/>
        </div>
        <div className="content">
          <HoseCheckingBillStatisticsDetailOverview />
          <HoseCheckingBillStatisticsList hideButton={this.props.hideButton} />
        </div>
      </div>
    );
  }
}
