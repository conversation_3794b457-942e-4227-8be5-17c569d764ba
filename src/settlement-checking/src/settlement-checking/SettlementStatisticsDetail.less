@import '~@ekuaibao/eui-styles/less/token.less';

.settlement-statistics-detail-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: #f7f7f7;
  justify-content: space-between;
  overflow: hidden;
  .settlement-statistics-detail-content {
    padding: @space-6;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;
    .settlement-base-info-wrapper {
      display: flex;
      flex-direction: column;
      border-radius: @space-2;
      .settlement-base-info-overview {
        height: 110px;
        padding-left: @space-8;
        display: flex;
        align-items: center;
        background-color: @color-white-1;
        .column {
          display: flex;
          flex-direction: column;
          .money {
            .font-size-6;
            .font-weight-3;
            font-family: DINAlternate, DINAlternate-Bold, sans-serif;
            color: #333333;
          }
          .label {
            .font-size-2;
            .font-weight-2;
            opacity: 0.76;
            color: #666666;
          }
          .column-value {
            .font-size-2;
            .font-weight-2;
            margin-top: @space-2;
            display: flex;
            align-items: center;
            opacity: 0.92;
            color: #434343;
            .bank-img {
              width: 20px;
              height: 20px;
            }
            .bank-name {
              .font-size-2;
              .font-weight-2;
              margin-left: @space-2;
              opacity: 0.92;
              color: #434343;
            }
            .bank-code {
              .font-size-2;
              .font-weight-3;
              margin-left: @space-2;
              opacity: 0.92;
              color: #434343;
            }
          }
        }
        .ml-48 {
          margin-left: 48px;
        }
        .ml-72 {
          margin-left: 72px;
        }
      }
      .settlement-base-info-list {
        margin-top: @space-6;
        display: flex;
        flex-shrink: 0;
        background-color: @color-white-1;
        .settlement-base-info-list-table {
          width: 100%;
        }
      }
      .base-info-action {
        display: flex;
        margin-top: @space-6;
      }
    }
    .settlement-bill-wrapper {
      min-height: 400px;
      margin-top: @space-6;
      display: flex;
      flex-direction: column;
      flex: 1;
      //overflow: auto;
      .content-wrapper {
        min-height: 400px;
        display: flex;
        flex-direction: column;
        position: relative;
      }
    }
  }

  .settlement-bottom-wrapper {
    height: 50px;
    padding-left: @space-6;
    display: flex;
    flex-shrink: 0;
    background: @color-white-1;
    box-shadow: 0px 1px 0px 0px #f0efef inset;
    align-items: center;
  }

  .detail-title {
    display: flex;
    align-items: center;
    font:var(--eui-font-body-r1);
    margin: 16px 0 -8px 16px;
    cursor: pointer;
  }
}

.base-info-statistical-column-title {
  .font-size-2;
  .font-weight-2;
  color: #263344;
  opacity: 0.92;
}

.base-info-statistical-column-value {
  .font-size-2;
  .font-weight-2;
  color: #374353;
  opacity: 0.92;
}
