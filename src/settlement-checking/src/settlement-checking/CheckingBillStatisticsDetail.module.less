@import '~@ekuaibao/eui-styles/less/token';
.checking-bill-statistics-detail-wrapper {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  :global {
    .fs-24 {
      .font-size-6;
    }

    .jc-e {
      justify-content: flex-end;
    }

    .color-b1 {
      color: @color-black-1;
    }

    .fw-700 {
      font-weight: 700;
    }

    .bc {
      background-color: #f7f7f7;
    }
    .ml-4 {
      margin-left: @space-2;
    }
    .ml-8 {
      margin-left: @space-4;
    }
    .ml-16 {
      margin-left: @space-6;
    }

    .ml-40 {
      margin-left: @space-9;
    }

    .ta-r {
      text-align: right;
    }

    .dp-f {
      display: flex;
    }

    .fd-c {
      flex-direction: column;
    }

    .jc-b {
      justify-content: space-between;
    }

    .f-1 {
      flex: 1;
    }

    .as-e {
      align-self: flex-end;
    }
    .ai-c {
      align-items: center;
    }
    .p-4 {
      padding: @space-2;
    }
    .money {
      font-family: DINAlternate, DINAlternate-Bold, sans-serif;
      .font-size-6;
      font-weight: 700;
    }
    .line {
      width: 1px;
      height: 77px;
      opacity: 1;
      background: #e6e6e6;
      margin: 0 @space-9;
    }
    .content {
      display: flex;
      flex: 1;
      flex-direction: column;
      background-color: #f7f7f7;
      padding: @space-6;
    }
    .detail-overview {
      display: flex;
      align-items: center;
      padding: @space-7;
      background-color: @color-white-1;
    }
    .checking-detail-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-top: @space-6;
      position: relative;
      .checking-detail-list-table-container {
        position: relative;
        padding: 6px 24px;
        background-color: @color-white-1;
      }
      .checking-detail-list-column-chooser {
        position: absolute;
        width: 32px;
        height: 32px;
        top: 10px;
        right: 6px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: #f7f7f7;
        border-radius: @space-2;
        > div {
          margin-top: 2px;
        }
      }
      .checking-detail-list-search {
        right: 116px !important;
      }
      .checking-detail-list-scene {
        position: absolute;
        left: 24px;
        top: 16px;
        z-index: 500;
      }
    }
    .checking-detail-info-bottom-wrapper {
      padding: @space-6;
      button {
        margin-right: @space-4;
      }
      .color-primary{
        color: var(--brand-base);
        cursor: pointer;
      }
    }
  }
}
