/**
 *  Created by pw on 2021/6/2 下午4:02.
 */
 import React, { useEffect, useState } from 'react';
 import { ApiSynchroConfigColumns } from '../helpers/tableColums';
 import './StatementsSplitConfig.less';
 import { getApiSyncList, deleteApiSyncConfig } from '../setttlement-checkin-action';
 import { app } from '@ekuaibao/whispered';
 import MessageCenter from '@ekuaibao/messagecenter';
 import { SplitIF } from '@ekuaibao/ekuaibao_types';
 import { showModal } from '@ekuaibao/show-util';
 const HoseTable = app.require<any>('@elements/HoseTable/TableResize');
 
 interface Props {
   bus: MessageCenter;
 }
 
 const ApiSynchroConfig: React.FC<Props> = (props) => {
   const { bus } = props;
   const [data, setData] = useState([]);
 
   useEffect(() => {
     bus.on('apiSynchroConfig:refresh:table:data', fetch);
     fetch();
     return () => {
       bus.un('apiSynchroConfig:refresh:table:data', fetch);
     };
   }, []);
 
   const fetch = async () => {
     const res = await getApiSyncList();
     setData(res.items);
   };
 
   const handleEdit = async (line: any) => {
     await app.open('@settlement-checkin:ApiSynchroConfigModal', { config: line });
     fetch();
   };
 
   const handleDelete = async (line: SplitIF) => {
     showModal.confirm({
       title: i18n.get('提示'),
       content: i18n.get(`确定删除「${line.name}」？`),
       onOk: async () => {
         await deleteApiSyncConfig({
           id: line.id
         });
         await fetch();
       },
     });
   };
 
   return (
     <div className="statements-split-config-wrapper">
       <div className="statements-split-config-wrapper-table">
        <HoseTable
          style={{ height: '100%' }}
          dataSource={data}
          scroll={{ y: 'calc(100vh - 240px)' }}
          isToolBar={true}
          columns={ApiSynchroConfigColumns({ onEdit: handleEdit, statusChange: handleDelete })}
          bordered={false}
        />
       </div>
     </div>
   );
 };
 
 export default ApiSynchroConfig;
 