import React from 'react';
import { Select } from '@hose/eui';
import { Category } from '../../supplier-dimension/vms';
import { ECategory } from '../../types/enums';
import { useInstance } from '@ekuaibao/react-ioc';
import { BillingPeriodVm } from './vms/billing-period.vm';
import { useObserver } from 'mobx-react-lite';

interface FilterViewProps {}

export const FilterView: React.FC<FilterViewProps> = () => {
  const vm = useInstance<BillingPeriodVm>(BillingPeriodVm.NAME);
  const handleStateChange = (value: ECategory) => {
    vm.setCategory(value);
  };

  return useObserver(() => (
    <div className="filter-wrapper">
      <label style={{ marginRight: 12 }}>{i18n.get('状态')}</label>
      <Select<ECategory>
        value={vm.category}
        style={{ width: 100 }}
        onChange={handleStateChange}
      >
        {Category?.map((item) => (
          <Option key={item.value} value={item.value}>
            {item.label}
          </Option>
        ))}
      </Select>
    </div>
  ));
};

export default FilterView;
