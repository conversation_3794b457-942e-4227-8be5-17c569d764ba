import { TableVm } from '@ekuaibao/collection-definition';
import { app } from '@ekuaibao/whispered';
import { disableBillingPeriod, getBillingPeriodList, restoreBillingPeriod } from '../../../setttlement-checkin-action';
import { action, IReactionPublic, observable, reaction } from 'mobx';
import { columns } from '../columns';
import { parseQuery2Select } from '@ekuaibao/lib/lib/parseQuery2Select';
import { IBillingPeriod } from '@ekuaibao/ekuaibao_types';
import { ECategory } from '../../../types/enums';
const fetchFixer = app.require<any>('@elements/data-grid/fetchFixer').default;

export class BillingPeriodVm extends TableVm<IBillingPeriod> {
  static NAME = Symbol.for('BILLING_PERIOD_VM');
  @observable columns = columns(this);
  @observable category = ECategory.ALL;
  reaction: IReactionPublic;
  @action setCategory = (value: ECategory) => {
    this.category = value;
  };

  init = async () => {
    reaction(
      () => [this.category, this.currentPage, this.pageSize, this.sorters],
      (data, reaction) => {
        this.reaction = reaction;
        this.getPeriods();
      },
      {
        fireImmediately: true,
      },
    );
  };

  getPeriods = async () => {
    const params = this.getParams();
    const result = await getBillingPeriodList(params);
    this.dataSource = result.items;
    this.dataTotal = result.count;
  };

  getParams = () => {
    const p = this.params();
    const param = fetchFixer(p);
    const query = parseQuery2Select(param);
    if (this.category !== ECategory.ALL) {
      query.filterBy(`active==${this.category === ECategory.ENABLE}`);
    }
    return query.value();
  };

  disable = async (line: IBillingPeriod) => {
    return disableBillingPeriod({ id: line.id });
  };

  restore = async (line: IBillingPeriod) => {
    return restoreBillingPeriod({ id: line.id });
  };

  clean = () => {
    this.reaction?.dispose();
  };
}
