import React from 'react';
import MessageCenter from '@ekuaibao/messagecenter';
import PeriodList from './PeriodList';
import { provider } from '@ekuaibao/react-ioc';
import { BillingPeriodVm } from './vms/billing-period.vm';
import styles from './billing-period.module.less';
import FilterView from './FilterView';

interface BillingPeriodConfigProps {
  bus: MessageCenter;
}
interface BillingPeriodConfigState {}
@provider([BillingPeriodVm.NAME, BillingPeriodVm])
export default class Index extends React.Component<BillingPeriodConfigProps, BillingPeriodConfigState> {
  render() {
    return (
      <div className={styles['billing-period-config-wrapper']}>
        <FilterView />
        <PeriodList bus={this.props.bus} />
      </div>
    );
  }
}
