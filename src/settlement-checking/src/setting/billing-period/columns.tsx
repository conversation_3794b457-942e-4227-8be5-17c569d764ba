import React from 'react';
import styles from '../../supplier-dimension/elements/action.module.less';
import { T } from '@ekuaibao/i18n';
import { showMessage } from '@ekuaibao/show-util';
import { BillingPeriodVm } from './vms/billing-period.vm';
import { Space } from '@hose/eui';

export const renderStatus = ({ status, label }) => {
  if (status) {
    return (
      <Space>
        <div style={{ height: 6, width: 6, borderRadius: 6, background: 'var(--eui-function-success-500)' }}></div>
        <div style={{ color: 'var(--eui-text-title)' }}>{label}</div>
      </Space>
    );
  }
  return (
    <Space>
      <div style={{ height: 6, width: 6, borderRadius: 6, background: 'var(--eui-decorative-neu-500)' }}></div>
      <div style={{ color: 'var(--eui-text-title)' }}>{label}</div>
    </Space>
  );
};

export const columns = (vm: BillingPeriodVm) => {
  return [
    {
      title: i18n.get('对账单期间'),
      dataIndex: 'name',
      sorter: true,
    },
    {
      title: i18n.get('开始时间'),
      dataIndex: 'startTime',
      render: (value) => {
        return new Date(value).format('YYYY-MM-DD');
      },
      sorter: true,
    },
    {
      title: i18n.get('结束时间'),
      dataIndex: 'endTime',
      render: (value) => {
        return new Date(value).format('YYYY-MM-DD');
      },
      sorter: true,
    },
    {
      title: i18n.get('状态'),
      dataIndex: 'active',
      render: (value, line) => {
        return renderStatus({ status: value, label: value ? '已打开' : '已关闭' });
      },
      sorter: true,
    },
    {
      title: i18n.get('操作'),
      dataIndex: 'operation',
      sorter: false,
      render: (value: any, record) => {
        const handleDisable = async () => {
          try {
            await vm.disable(record);
            await vm.getPeriods();
          } catch (e) {
            showMessage.error(e.message);
          }
        };
        const handleAble = async () => {
          try {
            await vm.restore(record);
            await vm.getPeriods();
          } catch (e) {}
        };
        return (
          <div className={styles['actions']}>
            {record?.active ? (
              <span className="col-btn" onClick={handleDisable}>
                <T name="关闭" />
              </span>
            ) : (
              <span className="col-btn" onClick={handleAble}>
                <T name="打开" />
              </span>
            )}
          </div>
        );
      },
    },
  ];
};
