import React, { useEffect } from 'react';
import { useInstance } from '@ekuaibao/react-ioc';
import { BillingPeriodVm } from './vms/billing-period.vm';
import { useObserver } from 'mobx-react-lite';
import MessageCenter from '@ekuaibao/messagecenter';
import { Map } from '@ekuaibao/datagrid/lib/types/utils';
import { app } from '@ekuaibao/whispered';
import { ConfigProvider, Pagination } from '@hose/eui';
const HoseTable = app.require<any>('@elements/HoseTable/TableResize');

interface PeriodListProps {
  bus: MessageCenter;
}
export const PeriodList: React.FC<PeriodListProps> = ({ bus }) => {
  const vm = useInstance(BillingPeriodVm.NAME);
  useEffect(() => {
    bus.on('billingPeriodConfig:refresh:table:data', vm.getPeriods);
    vm.init();
    return () => {
      bus.un('billingPeriodConfig:refresh:table:data', vm.getPeriods);
      vm.clean();
    };
  }, []);

  const handlePageChange = (page, size) => {
    vm.currentPage = page;
    vm.pageSize = size;
  };

  const handleSortChange = (sorter: Map<'ascend' | 'descend'>) => {
    const sorterObj = {
      [sorter.field]: sorter.order,
    };
    vm.sorters = sorterObj;
  };

  return useObserver(() => {
    return (
      <>
        <ConfigProvider>
          <HoseTable
            dataSource={vm.dataSource}
            columns={vm.columns}
            isToolBar={true}
            locale={{
              triggerDesc: '',
              triggerAsc: '',
              cancelSort: '',
            }}
            onChange={(pagination, filters, sorter) => handleSortChange(sorter)}
            bordered={false}
            scroll={{ y: 'calc(100vh - 328px)' }}
          />
        </ConfigProvider>
        <div className="pagination-wrapper">
          <Pagination
            total={vm.dataTotal}
            current={vm.currentPage}
            pageSize={vm.pageSize}
            showSizeChanger={true}
            showQuickJumper={true}
            onChange={handlePageChange}
          />
        </div>
      </>
    );
  });
};

export default PeriodList;
