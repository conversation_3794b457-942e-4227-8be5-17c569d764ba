/*
 * @Description: 结算单配置列表
 * @Creator: chencan<PERSON><PERSON>
 * @Date: 2021-06-18 19:35:16
 */
import React, { useEffect, useState } from 'react';
import MessageCenter from '@ekuaibao/messagecenter';
import { getConfigSettlementList, putConfigSettlement } from '../setttlement-checkin-action';
import { showModal, showMessage } from '@ekuaibao/show-util';
import { FeeGenerateConfigColumns } from '../helpers/tableColums';
import { app } from '@ekuaibao/whispered';
import './GenerateFeeConfig.less';
const HoseTable = app.require<any>('@elements/HoseTable/TableResize');

interface Props {
  bus: MessageCenter;
}
const handleOpenSettle = async (_menuConfig?: any) => {
  const config = await app.open('@settlement-checkin:SettleBillConfigModal', _menuConfig);
  const menuConfig: any = await app.open('@settlement-checkin:SettleBillMenuConfigModal', config);
  if (menuConfig?.lastStep) {
    await handleOpenSettle(menuConfig);
  }
};
const SettleBillConfig: React.FC<Props> = (props) => {
  const { bus } = props;
  const [data, setData] = useState([]);

  useEffect(() => {
    fetch();
    bus.on('settleBillConfig:refresh:table:data', fetch);
    return () => {
      bus.un('settleBillConfig:refresh:table:data', fetch);
    };
  }, []);

  const fetch = async () => {
    const res = await getConfigSettlementList();
    setData(res?.items);
  };

  const handleEdit = async (line: any) => {
    await handleOpenSettle({ data: line });
    fetch();
  };

  const handleDisabled = async (line: any) => {
    const content = line.active ? `确定停用「${line.name}」？` : `确定启用「${line.name}」？`;
    showModal.confirm({
      title: i18n.get('提示'),
      content,
      onOk() {
        putConfigSettlement({
          id: line?.id,
          description: line?.description,
          mappings: line?.mappings,
          name: line?.name,
          specificationId: line?.specificationId,
          supplierAccountId: line?.supplierAccountId,
          active: !line?.active,
          settlementDimension: line?.settlementDimension || {
            apportions: false,
            details: true,
            type: 'legalEntity',
          },
          rules: line?.rules ?? [],
        })
          .then((res) => {
            fetch();
          })
          .catch((err) => {
            showMessage.error(err?.message);
          });
      },
    });
  };

  return (
    <div className="generate-fee-config-wrapper">
      <div className="generate-fee-config-wrapper-table">
        <HoseTable
          style={{ height: '100%' }}
          dataSource={data}
          scroll={{ y: 'calc(100vh - 240px)' }}
          isToolBar={true}
          columns={FeeGenerateConfigColumns({ onEdit: handleEdit, statusChange: handleDisabled })}
          bordered={false}
        />
      </div>
    </div>
  );
};
export default SettleBillConfig;
