@import '~@ekuaibao/eui-styles/less/token';

.statements-config-wrapper {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  padding: 0 16px 16px 16px;
  .statements-config-desc {
    .font-size-2;
    .font-weight-2;
    margin-bottom: 8px;
    color: #142234;
    opacity: 0.44;
  }
  .statements-config-wrapper-table {
    display: flex;
    flex: 1;
    overflow: hidden;
    .table-wrapper {
      height: 100%;
    }
  }
  .statements-config-wrapper-footer {
    display: flex;
    align-items: center;
    height: 56px;
    flex-shrink: 0;
  }
  .settlement-setting-action {
    display: flex;
    .action-text {
      .font-size-2;
      .font-weight-2;
      cursor: pointer;
    }
    .edit-action {
      color: @brand-color;
    }
    .statement-rule-btn {
      color: var(--eui-primary-pri-500, #2555FF);
    }
    .disabled-action {
      margin-left: 16px;
      color: var(--eui-primary-pri-500, #2555FF);
    }
  }
}
