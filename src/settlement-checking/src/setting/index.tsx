/*
 * @Description:
 * @Creator: chencan<PERSON>han
 * @Date: 2021-06-18 19:28:31
 */
/**
 *  Created by pw on 2021/6/2 下午3:31.
 */
import React, { Component } from 'react';
import './index.less';
import { app } from '@ekuaibao/whispered';
import BillingPeriodConfig from './billing-period';
const withLoader = app.require<any>('@elements/data-grid-v2/withLoader');
const GenerateFeeConfig = withLoader(() => import('./GenerateFeeConfig'));
const StatementsConfig = withLoader(() => import('./StatementsConfig'));
const StatementsSplitConfig = withLoader(() => import('./StatementsSplitConfig'));
const SettleBillConfig = withLoader(() => import('./SettleBillConfig'));
const ApiSynchroConfig = withLoader(() => import('./ApiSynchroConfig'));
const OrderConfig = withLoader(() => import('./OrderConfig'));
import MessageCenter from '@ekuaibao/messagecenter';
import { getApiSyncBasicConfig } from '../setttlement-checkin-action';
import { EnhanceConnect } from '@ekuaibao/store';
import { Button, Tabs } from '@hose/eui';
import { OutlinedTipsAdd } from '@hose/eui-icons';
interface Props {
  [key: string]: any;
}
interface State {
  [key: string]: any;
}
@EnhanceConnect((state: any) => ({
  KA_ZY_Reconciliation_Settlement: state['@common'].powers.KA_ZY_Reconciliation_Settlement,
}))
export default class Setting extends Component<State, Props> {
  bus = new MessageCenter();
  constructor(props: Props | Readonly<Props>) {
    super(props);
    this.state = {
      apiSyncCtrip: false,
      apiSyncGeely: false,
      apiSyncTC: false,
      apiSyncTO: false,
      activeKey: 'generateFeeConfig',
    };
  }
  componentDidMount() {
    //吉利和携程基础配置获取
    this.getBasicConfig();
  }

  getBasicConfig = async () => {
    let apiSyncCtrip = await this.getApiSyncBasic('CTRIP');
    let apiSyncGeely = await this.getApiSyncBasic('GEELY_TRIP');
    let apiSyncTC = await this.getApiSyncBasic('TC_TRIP');
    let apiSyncTO = await this.getApiSyncBasic('TRAVEL_ONE');

    this.setState({
      apiSyncCtrip: apiSyncCtrip?.value?.apiSyncSwitch ?? false,
      apiSyncGeely: apiSyncGeely?.value?.apiSyncSwitch ?? false,
      apiSyncTC: apiSyncTC?.value?.apiSyncSwitch ?? false,
      apiSyncTO: apiSyncTO?.value?.apiSyncSwitch ?? false,
    });
  };

  getApiSyncBasic = async (type: string) => {
    try {
      return await getApiSyncBasicConfig({ billPlatform: type });
    } catch (err) {
      return {};
    }
  };

  handleTabChange = (tabKey: string) => {
    this.setState({ activeKey: tabKey });
  };
  handleOpenSettle = async (_menuConfig?: any) => {
    const config = await app.open('@settlement-checkin:SettleBillConfigModal', _menuConfig);
    const menuConfig: any = await app.open('@settlement-checkin:SettleBillMenuConfigModal', config);
    if (menuConfig?.lastStep) {
      await this.handleOpenSettle(menuConfig);
    }
  };
  handleCreate = async (type: string) => {
    if (type === 'statementsConfig') {
      await app.open('@settlement-checkin:EditStatementRuleModal');
    } else if (type === 'statementsSplitConfig') {
      await app.open('@settlement-checkin:EditStatementSplitRuleModal');
    } else if (type === 'billingPeriodConfig') {
      await app.open('@settlement-checkin:EditBillingPeriod');
      console.log('billingPeriodConfig');
    } else if (type === 'generateFeeConfig') {
      await app.open('@settlement-checkin:FeeGenerateConfig');
    } else if (type === 'settleBillConfig') {
      await this.handleOpenSettle();
    } else if (type === 'orderConfig') {
      await app.open('@settlement-checkin:OrderConfigModal');
    } else if (type === 'apiSynchroConfig') {
      await app.open('@settlement-checkin:ApiSynchroConfigModal');
    }
    this.bus.invoke(`${type}:refresh:table:data`);
  };

  renderTabs = () => {
    const { apiSyncCtrip = false, apiSyncGeely = false, apiSyncTC = false, apiSyncTO = false } = this.state;
    let tabs = [
      {
        key: 'generateFeeConfig',
        label: i18n.get('费用生成配置'),
        extraContent: <CreateBtn type={'generateFeeConfig'} onClick={this.handleCreate} />,
        children: <GenerateFeeConfig type={'generateFeeConfig'} bus={this.bus} />,
      },
      {
        key: 'statementsConfig',
        label: i18n.get('对账单配置'),
        extraContent: <CreateBtn type={'statementsConfig'} onClick={this.handleCreate} />,
        children: <StatementsConfig bus={this.bus} />,
      },
      {
        key: 'statementsSplitConfig',
        label: i18n.get('对账单拆分配置'),
        extraContent: <CreateBtn type={'statementsSplitConfig'} onClick={this.handleCreate} />,
        children: <StatementsSplitConfig bus={this.bus} />,
      },
      {
        key: 'billingPeriodConfig',
        label: i18n.get('对账单期间配置'),
        extraContent: <CreateBtn type={'billingPeriodConfig'} onClick={this.handleCreate} />,
        children: <BillingPeriodConfig bus={this.bus} />,
      },
      {
        key: 'settleBillConfig',
        label: i18n.get('结算单配置'),
        extraContent: <CreateBtn type={'settleBillConfig'} onClick={this.handleCreate} />,
        children: <SettleBillConfig bus={this.bus} />,
      },
    ];
    this.props?.KA_ZY_Reconciliation_Settlement &&
      tabs.push({
        key: 'orderConfig',
        label: i18n.get('订单匹配配置'),
        extraContent: <CreateBtn type={'orderConfig'} onClick={this.handleCreate} />,
        children: <OrderConfig type={'orderConfig'} bus={this.bus} />,
      });
    if (apiSyncCtrip || apiSyncGeely || apiSyncTC || apiSyncTO) {
      tabs.push({
        key: 'apiSynchroConfig',
        label: i18n.get('API同步对账单配置'),
        extraContent: <CreateBtn type={'apiSynchroConfig'} onClick={this.handleCreate} />,
        children: <ApiSynchroConfig bus={this.bus} />,
      });
    }
    return tabs;
  };

  render() {
    return (
      <div className="check-in-settlement-setting-wrapper">
        <Tabs
          tabBarExtraContent={this.renderTabs().find((tab) => tab.key === this.state.activeKey)?.extraContent}
          className={'setting-tab-wrapper'}
          defaultActiveKey={'generateFeeConfig'}
          onChange={this.handleTabChange}
          items={this.renderTabs()}
        />
      </div>
    );
  }
}

interface CreateBtnProps {
  type: string;
  onClick: (type: string) => void;
}

const CreateBtn: React.FC<CreateBtnProps> = (props) => {
  const { type, onClick } = props;
  return (
    <div className="check-in-settlement-setting-create">
      <Button icon={<OutlinedTipsAdd />} onClick={() => onClick(type)}>
        新建配置
      </Button>
    </div>
  );
};
