import MessageCenter from '@ekuaibao/messagecenter';
import React, { useEffect, useState, useCallback } from 'react';
import { app } from '@ekuaibao/whispered';
import { OrderConfigColumns } from '../helpers/tableColums';
import { activeOrderMatchConfig, setOrderMatchConfig, getOrderMatchConfigList } from '../setttlement-checkin-action';
const HoseTable = app.require<any>('@elements/HoseTable/TableResize');
interface IPorps {
  bus: MessageCenter;
}

const OrderConfig: React.FC<IPorps> = (params: IPorps) => {
  const { bus } = params;
  const [tableData, setTableData] = useState([]);
  const handleEdit = useCallback(async (line) => {
    const params = await app.open('@settlement-checkin:OrderConfigModal', { data: line });
    await setOrderMatchConfig(params);
    fetch();
  }, []);
  const handleDisabled = useCallback(async (line) => {
    await activeOrderMatchConfig({ id: line?.id, operate: !line?.active });
    fetch();
  }, []);
  const fetch = async () => {
    const { items } = await getOrderMatchConfigList({});
    setTableData(items);
  };
  useEffect(() => {
    fetch();
    bus.on('orderConfig:refresh:table:data', fetch);
    return () => {
      bus.un('orderConfig:refresh:table:data', fetch);
    };
  }, []);
  return (
    <div className="generate-fee-config-wrapper">
      <div className="generate-fee-config-wrapper-table">
        <HoseTable
          style={{ height: '100%' }}
          dataSource={tableData}
          scroll={{ y: 'calc(100vh - 240px)' }}
          isToolBar={true}
          columns={OrderConfigColumns({ onEdit: handleEdit, statusChange: handleDisabled })}
          bordered={false}
        />
      </div>
    </div>
  );
};
export default OrderConfig;
