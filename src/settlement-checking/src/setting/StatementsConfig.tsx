/**
 *  Created by pw on 2021/6/2 下午4:01.
 */
import React, { useState, useEffect } from 'react';
import './StatementsConfig.less';
import { StatementsConfigColumns } from '../helpers/tableColums';
import { getStatementConfigList, saveStatementConfig } from '../setttlement-checkin-action';
import { app } from '@ekuaibao/whispered';
import MessageCenter from '@ekuaibao/messagecenter';
import { showModal } from '@ekuaibao/show-util';
import { isObject } from '@ekuaibao/helpers';
const HoseTable = app.require<any>('@elements/HoseTable/TableResize');

interface Props {
  bus: MessageCenter;
}

const StatementsConfig: React.FC<Props> = (props) => {
  const { bus } = props;
  const [data, setData] = useState([]);

  useEffect(() => {
    bus.on('statementsConfig:refresh:table:data', fetch);
    fetch();
    return () => {
      bus.un('statementsConfig:refresh:table:data', fetch);
    };
  }, []);

  const fetch = async () => {
    const res = await getStatementConfigList();
    setData(res.items);
  };

  const handleEdit = async (line: any) => {
    await app.open('@settlement-checkin:EditStatementRuleModal', { config: line });
    await fetch();
  };

  const handleDisabled = async (line: any) => {
    showModal.confirm({
      title: i18n.get('提示'),
      content: i18n.get(`确定删除「${line.supplierAccountId.name}」？`),
      onOk: async () => {
        await saveStatementConfig({
          splitConfigId: isObject(line.splitConfigId) ? line.splitConfigId?.id : line.splitConfigId,
          supplierAccountId: isObject(line.supplierAccountId) ? line.supplierAccountId?.id : line.supplierAccountId,
          active: !line.active,
          id: line.id,
        });
        await fetch();
      },
    });
  };

  return (
    <div className="statements-config-wrapper">
      <div className="statements-config-desc">在「对账单拆分配置」完成规则编辑后，您可在此页面针对供应商指定对应的规则</div>
      <div className="statements-config-wrapper-table">
        <HoseTable
          style={{ height: '100%' }}
          dataSource={data}
          scroll={{ y: 'calc(100vh - 270px)' }}
          isToolBar={true}
          columns={StatementsConfigColumns({ onEdit: handleEdit, statusChange: handleDisabled })}
          bordered={false}
        />
      </div>
    </div>
  );
};

export default StatementsConfig;
