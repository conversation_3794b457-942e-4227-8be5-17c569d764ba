/**
 *  Created by pw on 2021/6/2 下午3:59.
 */
import React, { useEffect, useState } from 'react';
import MessageCenter from '@ekuaibao/messagecenter';
import { changeDetailConfigActive, getFeeConfigList, getSupplierAccount } from '../setttlement-checkin-action';
import { showModal, showMessage } from '@ekuaibao/show-util';
import { FeeGenerateConfigColumns } from '../helpers/tableColums';
import { app } from '@ekuaibao/whispered';
import './GenerateFeeConfig.less';
import { DatalinkIF } from '@ekuaibao/ekuaibao_types';
import { ConfigProvider, Select } from '@hose/eui';
const HoseTable = app.require<any>('@elements/HoseTable/TableResize');

interface Props {
  bus: MessageCenter;
}

const GenerateFeeConfig: React.FC<Props> = (props) => {
  const { bus } = props;
  const [data, setData] = useState([]);
  const [pageSize, setPageSize] = useState<number>(10);
  const [stateSelect, setStateSelect] = useState<string>('ENABLED');

  useEffect(() => {
    fetch();
    bus.on('generateFeeConfig:refresh:table:data', fetch);
    return () => {
      bus.un('generateFeeConfig:refresh:table:data', fetch);
    };
  }, []);

  const fetch = async () => {
    const res = await getFeeConfigList();
    setData(res.items);
  };

  const handleEdit = async (line: any) => {
    let datalink: DatalinkIF | undefined;
    if (line?.supplierAccountId?.id) {
      const { value } = await getSupplierAccount(line?.supplierAccountId?.id);
      datalink = value?.categoryIds?.find((datalink: DatalinkIF) => datalink.id === line.dataLinkEntityId?.id);
    }
    await app.open('@settlement-checkin:FeeGenerateConfig', { config: line, datalink });
    fetch();
  };

  const handleCopy = async (line: any) => {
    let datalink: DatalinkIF | undefined;
    if (line?.supplierAccountId?.id) {
      const { value } = await getSupplierAccount(line?.supplierAccountId?.id);
      datalink = value?.categoryIds?.find((datalink: DatalinkIF) => datalink.id === line.dataLinkEntityId?.id);
    }
    await app.open('@settlement-checkin:FeeGenerateConfig', {
      config: { ...line, id: undefined, name: line.name + '（副本）' },
      datalink,
    });
    fetch();
  };

  const handleStatusChange = async (line: any) => {
    const content = line.active ? `确定停用「${line.name}」？` : `确定启用「${line.name}」？`;
    showModal.confirm({
      title: i18n.get('提示'),
      content,
      onOk: async () => {
        await changeDetailConfigActive({
          active: `${!line.active}`,
          id: line.id
        }).then(()=>{
          setTimeout(() => {
            showMessage.success(line.active ? '已停用' : '已启用');
          }, 1000);
        }).catch((err) => {
          showMessage.error(err?.message);
        });
        await fetch();
      },
    });
  };

  return (
    <div className="generate-fee-config-wrapper">
      <div className="columnSelect">
        <span className="columnSelectTitle">状态</span>
        <Select
          defaultValue="已启用"
          style={{ width: 120 }}
          onSelect={(key) => {
            setStateSelect(key);
          }}
          options={[
            {
              value: 'ALL',
              label: '全部',
            },
            {
              value: 'ENABLED',
              label: '已启用',
            },
            {
              value: 'DEACTIVETED',
              label: '已停用',
            },
          ]}
        />
      </div>
      <ConfigProvider>
        <HoseTable
          className={'table-wrapper'}
          dataSource={
            stateSelect === 'ALL'
              ? data
              : data.filter((item) => item?.active === (stateSelect === 'ENABLED' ? true : false))
          }
          columns={FeeGenerateConfigColumns({
            onEdit: handleEdit,
            onCopy: handleCopy,
            statusChange: handleStatusChange,
          })}
          scroll={{ y: 'calc(100vh - 344px)' }}
          pagination={{
            pageSize: pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            onShowSizeChange: (current: any, size: number) => {
              setPageSize(size);
            },
            showTotal: () =>
              `共 ${
                (stateSelect === 'ALL'
                  ? data
                  : data.filter((item) => item?.active === (stateSelect === 'ENABLED' ? true : false))
                ).length
              } 条`,
          }}
          isToolBar={true}
          bordered={false}
          customClassName="invoice-wrapper-table"
        />
      </ConfigProvider>
    </div>
  );
};
export default GenerateFeeConfig;
