<?xml version="1.0" encoding="UTF-8"?>
<svg width="30px" height="30px" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 9</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="30" height="30" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="30" height="30" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="耀升" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="05_账单详情" transform="translate(-792.000000, -847.000000)">
            <g id="编组-3" transform="translate(267.000000, 778.000000)">
                <g id="编组-9" transform="translate(525.000000, 69.000000)">
                    <use id="矩形" stroke="#C1C1C1" mask="url(#mask-2)" stroke-width="2" stroke-dasharray="4,2" xlink:href="#path-1"></use>
                    <g id="编组-7" transform="translate(7.928932, 8.000000)" fill="#C1C1C1">
                        <path d="M8.07106781,0 L8.071,12.343 L12.7781746,7.63603897 L14.1923882,9.05025253 L7.12132034,16.1213203 L7.096,16.096 L7.07106781,16.1213203 L6.90558721e-14,9.05025253 L1.41421356,7.63603897 L6.071,12.293 L6.07106781,0 L8.07106781,0 Z" id="形状结合"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>