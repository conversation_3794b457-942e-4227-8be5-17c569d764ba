/**
 *  Created by pw on 2021/6/4 下午1:51.
 */
import React, { useEffect, useState } from 'react';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
import { T } from '@ekuaibao/i18n';
import { Button, Icon } from 'antd';
import DynamicWrapper from '../components/DynamicWrapper';
import { hideLoading, showLoading } from '@ekuaibao/lib/lib/lib-util';
import { saveFeeGenerateConfig } from '../setttlement-checkin-action';
import { showMessage } from '@ekuaibao/show-util';
import { Bus } from '@ekuaibao/template';
import './FeeGenerateConfig.less';
import CostMatchCondition from '../components/CostMatchCondition';
import CostMatchFeeType from '../components/CostMatchFeeType';
import CostMatchMappingRule from '../components/CostMatchMappingRule/CostMatchMappingRule';
import CostFilterCodition from '../components/CostFilterCodition';
import RelationFieldComponent from '../components/RelationFieldComponent';
import RequisitionMapping from '../components/RequisitionMapping';
import ApportionWay from '../components/ApportionWay';
import { DatalinkIF, SupplierSettingDetailConfigIF } from '@ekuaibao/ekuaibao_types';
import { Mapping } from '../components/RequisitionMapping/helper';

interface ISupplierSettingDetailCondition extends SupplierSettingDetailConfigIF {}

interface Props extends ILayerProps {
  config: ISupplierSettingDetailCondition;
  datalink?: DatalinkIF;
}

const FeeGenerateConfig: React.FC<Props> = (props) => {
  const { config, datalink } = props;
  const [bus] = useState(Bus());

  useEffect(() => {
    bus.setValidateLevel(1);
  }, []);

  const handleClose = () => {
    props.layer.emitCancel();
  };

  const handleConfirm = async () => {
    const values: any = await bus.getValueWithValidate(0).catch((err) => {
      bus.setValidateLevel(1);
    });
    if (values) {
      const { filterDataConfig = {} } = values;
      delete values.filterDataConfig;
      const value = formatValues(values);
      showLoading('');
      await saveFeeGenerateConfig({ ...value, ...filterDataConfig, id: config?.id })
        .then((err) => {
          console.log(err);
          showMessage.success('保存成功');
          hideLoading();
          props.layer.emitOk(values);
        })
        .catch((e) => {
          showMessage.error(e.message);
        });
    }
  };
  const Btn: any = Button;

  return (
    <div className="fee-generate-config-wrapper">
      <div className="modal-header">
        <div className="flex title">
          <T name="费用生成配置" />
        </div>
        <Icon className="cross-icon" type="cross" onClick={handleClose} />
      </div>
      <div className="edit-fee-generate-rule">
        <DynamicWrapper
          value={
            config
              ? {
                  ...config,
                  filterDataConfig: {
                    dataLinkEntityId: config?.dataLinkEntityId,
                    supplierAccountId: config?.supplierAccountId,
                  },
                }
              : undefined
          }
          template={template()}
          tags={{
            relationField: { feeTypeId: config?.feeTypeId, dataLinkEntity: datalink },
            mappings: { dataLinkEntity: datalink },
            conditions: { dataLinkEntity: datalink },
          }}
          bus={bus}
          elements={[
            CostMatchCondition as any,
            CostMatchFeeType,
            CostMatchMappingRule,
            CostFilterCodition,
            RelationFieldComponent,
            RequisitionMapping,
            ApportionWay,
          ]}
        />
      </div>
      <div className="modal-footer">
        <Button className="mr-8" onClick={handleClose}>
          <T name={'取消'} />
        </Button>
        <Btn className="mr-8" type={'primary'} onClick={handleConfirm}>
          <T name={'确定'} />
        </Btn>
      </div>
    </div>
  );
};

const template = () => [
  {
    name: 'name',
    label: i18n.get('配置名称'),
    placeholder: i18n.get('请输入配置名称'),
    maxLength: 50,
    type: 'text',
    optional: false,
    editable: true,
  },
  {
    name: 'description',
    label: i18n.get('配置描述'),
    placeholder: i18n.get('请配置描述'),
    maxLength: 50,
    type: 'text',
    optional: true,
    editable: true,
  },
  {
    name: 'filterDataConfig',
    label: i18n.get('筛选数据'),
    placeholder: i18n.get('请选择筛选数据'),
    type: 'cost-filter-condition',
    optional: false,
    editable: true,
  },
  {
    name: 'conditions',
    label: i18n.get('消费与费用映射配置'),
    placeholder: i18n.get('请填写消费与费用映射配置'),
    type: 'cost-match-condition',
    optional: true,
    editable: true,
  },
  {
    name: 'feeTypeId',
    label: i18n.get('映射到'),
    placeholder: i18n.get('请选择费用类型'),
    type: 'cost-match-feeType',
    optional: false,
    editable: true,
  },
  {
    name: 'mappings',
    label: i18n.get('配置赋值规则'),
    placeholder: i18n.get('请配置赋值规则'),
    type: 'cost-match-mapping-rule',
    optional: false,
    editable: true,
  },
  {
    name: 'relationField',
    label: i18n.get('关联此数据到字段'),
    placeholder: i18n.get('请选择关联字段'),
    type: 'ref',
    optional: false,
    editable: true,
  },
  {
    name: 'requisitionMappings',
    label: i18n.get('映射到申请单与费用'),
    type: 'ref',
    optional: true,
    editable: true,
  },
  {
    name: 'apportionIds',
    label: i18n.get('分摊方式'),
    type: 'ref',
    placeholder: i18n.get('请选择对账结算使用的分摊方式，需要先选择映射到的费用类型'),
    optional: true,
    editable: true,
  },
];

const formatValues = (values: any) => {
  const result = { ...values };
  result.requisitionMappings = values.requisitionMappings
    ?.filter((item: Mapping) => !!item.specificationId && !!item.feeTypeId)
    .map(({ id, ...others }: Mapping) => {
      return others;
    });
  return result;
};

export default FeeGenerateConfig;
