.batch-modify-bill-field-value-wrapper {
  .modal-header {
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid var(--eui-line-divider-default);
    .title {
      font: var(--eui-font-head-b1);
      color: var(--eui-text-title);
    }
  }
  .modify-bill-content {
    height: 400px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    .field-row {
      margin-bottom: 8px;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--eui-decorative-neu-100);
      border-radius: 6px;
      .left-prefix {
        margin-right: 12px;
        color: var(--eui-text-caption);
        min-width: 24px;
        flex-shrink: 0;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
      .field-select {
        width: 100%;
        margin-right: 12px;
      }
      .right-action {
        margin-left: 12px;
        display: flex;
        align-items: center;
        cursor: pointer;
        .add-field {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
        }
        &:first-child {
          margin-right: 12px;
        }
      }
    }
  }
  .modal-footer {
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid var(--eui-line-divider-default);
    box-shadow: none;
  }
}
