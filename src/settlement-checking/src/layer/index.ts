/*
 * @Description:
 * @Creator: chencan<PERSON>han
 * @Date: 2021-06-18 19:28:31
 */
/**
 *  Created by pw on 2021/6/2 下午3:21.
 */
export default [
  {
    key: 'SupplierEditNew',
    getComponent: () => import('./SupplierEdit'),
    enhancer: 'drawer',
    enhancerOptions: { title: i18n.get('新建供应商信息'), bodyStyle: { padding: 0 } },
    width: 700,
    maskClosable: true,
  },
  {
    key: 'SupplierEdit',
    getComponent: () => import('./SupplierEdit'),
    enhancer: 'drawer',
    enhancerOptions: { title: i18n.get('编辑供应商信息'), bodyStyle: { padding: 0 } },
    width: 700,
    maskClosable: true,
  },
  {
    key: 'EditStatementRuleModal',
    getComponent: () => import('./EditStatementRuleModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    centered: true,
    width: 520,
    maskClosable: false,
  },
  {
    key: 'EditStatementSplitRuleModal',
    getComponent: () => import('./EditStatementSplitRuleModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 600,
    maskClosable: false,
  },
  {
    key: 'BillTitleDrawer',
    getComponent: () => import('./drawer/BillTitleDrawer'),
  },
  {
    key: 'HoseBillTitleDrawer',
    getComponent: () => import('./drawer/HoseBillTitleDrawer'),
  },
  {
    key: 'SupplierAccountDrawer',
    getComponent: () => import('./drawer/SupplierAccountDrawer'),
  },
  {
    key: 'BillTypeDrawer',
    getComponent: () => import('./drawer/BillTypeDrawer'),
  },
  {
    key: 'SyncSettlementHistoryDrawer',
    title: i18n.get('对账单同步记录'),
    getComponent: () => import('./drawer/SyncSettlementHistoryDrawer/index'),
  },
  {
    key: 'AddBillModal',
    getComponent: () => import('./modal/AddBillModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 540,
    maskClosable: false,
  },
  {
    key: 'SupplierAccountModal',
    getComponent: () => import('./modal/SupplierAccountModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 700,
    maskClosable: false,
  },
  {
    key: 'SettleBillConfigModal',
    getComponent: () => import('./modal/SettleBillConfigModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 600,
    maskClosable: false,
  },
  {
    key: 'OrderConfigModal',
    getComponent: () => import('./modal/OrderConfigModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 700,
    maskClosable: false,
  },
  {
    key: 'SettleBillMenuConfigModal',
    getComponent: () => import('./modal/SettleBillMenuConfigModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 600,
    maskClosable: false,
  },
  {
    key: 'OrderSelectModal',
    getComponent: () => import('./modal/OrderSelectModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 600,
    maskClosable: false,
  },
  {
    key: 'OrderMatchModel',
    getComponent: () => import('./modal/OrderMatchModel'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 800,
    maskClosable: false,
  },
  {
    key: 'OrderShowTaskModal',
    getComponent: () => import('./modal/OrderShowTaskModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 600,
    maskClosable: false,
  },
  {
    key: 'EditBillingPeriod',
    getComponent: () => import('./EditBillingPeriodModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 600,
    maskClosable: false,
  },
  {
    key: 'FeeGenerateConfig',
    getComponent: () => import('./FeeGenerateConfig'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 780,
    maskClosable: false,
  },
  {
    key: 'BatchModifyFeeInfo',
    getComponent: () => import('./BatchModifyFeeInfo'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 800,
    maskClosable: false,
  },
  {
    key: 'AddSettlementModal',
    getComponent: () => import('./AddSettlementModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 600,
    maskClosable: false,
  },
  {
    key: 'SubmitCheckingModal',
    getComponent: () => import('./modal/SubmitCheckingModal'),
    width: 700,
    maskClosable: false,
    wrapClassName: '',
  },
  {
    key: 'EditSupplierAccountCategory',
    getComponent: () => import('./drawer/EditSupplierAccountCategory'),
    enhancer: 'drawer',
    enhancerOptions: { title: i18n.get('编辑账单模板字段'), bodyStyle: { padding: 0 } },
    width: 800,
    maskClosable: true,
  },
  {
    key: 'CategoryFieldsMap',
    getComponent: () => import('./modal/CategoryFieldsMap'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 800,
    maskClosable: true,
  },
  {
    key: 'ApiSynchroConfigModal',
    getComponent: () => import('./ApiSynchroConfigModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 780,
    maskClosable: false,
  },
  {
    key: 'ApportionsModal',
    getComponent: () => import('./modal/ApportionsModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 700,
    maskClosable: false,
  },
  {
    key: 'ManuallySyncBillModal',
    getComponent: () => import('./modal/ManuallySyncBillModal'),
    enhancerOptions: { title: i18n.get('编辑账单模板字段'), bodyStyle: { padding: 0 } },
    width: 600,
    maskClosable: true,
  },
  {
    key: 'ReSyncSettlement',
    getComponent: () => import('./modal/ReSyncSettlement'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 600,
    maskClosable: false,
  },
  {
    key: 'ExportSelectAllModal',
    getComponent: () => import('./ExportSelectAllModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'custom-modal-layer' },
    width: 600,
    maskClosable: false,
  },
  {
    key: 'BatchModifyBillFieldValue',
    getComponent: () => import('./BatchModifyBillFieldValue'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'custom-modal-layer' },
    width: 600,
    maskClosable: false,
  },
  {
    key: 'BillAdjustLogsModal',
    getComponent: () => import('./drawer/BillAdjustLogsModal'),
    enhancer: 'drawer',
    enhancerOptions: {
      title: i18n.get('调整日志'),
      width: 1200,
      footer: []
    },
    maskClosable: true
  },
  {
    key: 'AdjustingBillsDrawer',
    getComponent: () => import('./drawer/AdjustingBillsDrawer'),
    enhancer: 'drawer',
    enhancerOptions: { title: i18n.get('账单调整'), bodyStyle: { padding: 0 } },
    width: 1200,
    maskClosable: true,
    className: 'adjustingBillsDrawer-modal'
  },
];
