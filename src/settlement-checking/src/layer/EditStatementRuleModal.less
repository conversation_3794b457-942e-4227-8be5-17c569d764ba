@import '~@eku<PERSON>bao/eui-styles/less/token.less';

.edit-statement-rule-wrapper {
  display: flex;
  flex-direction: column;

  .statement-rule-header {
    display: flex;
    height: 46px;
    padding: 16px;
    margin-bottom: -16px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;

    .title {
      color: rgba(29, 33, 41, 0.9);
      font: var(--eui-font-head-b1);
    }
  }

  .statement-rule-option{
    padding: 0 16px;

    .statement-rule-option-select{
      margin: 16px 0;
    }

    .statement-rule-option-radio{
      margin: 0;

      .option-radio{
        height: 20px;
      }
    }
  }

  .statement-rule-option-radio > div :nth-child(2) > div{
    height: 20px;
    min-height: 0;
  }

  .alert-statement-rule {
    margin-top: 12px;
    margin-bottom: -24px;
  }

  .effect-preview{
    margin: -8px 0 12px 0;
  }

  .edit-son-statement-name{
    height: 32px;
    display: flex;
  }

  .edit-son-statement-name > div{
    white-space: nowrap;
    line-height: 32px;
    margin-right: 8px;
  }

  .statement-rule-footer {
    display: flex;
    flex-direction: row;
    height: 64px;
    padding: 16px;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    margin: 0;
  }
}