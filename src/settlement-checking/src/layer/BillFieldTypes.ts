/**
 *  Created by pw on 2023/6/14 17:25.
 */
export interface IBillInfo {
  operateSource: string;
  operatorId: string;
  pipeline: number;
  grayver: string;
  id: string;
  version: number;
  active: boolean;
  createTime: number;
  updateTime: number;
  nameSpell: string;
  code: string;
  corporationId: string;
  sourceCorporationId: any;
  dataCorporationId: any;
  name: string;
  type: string;
  visibility: IVisibility;
  entityId: string;
  content: IContent;
}

export interface IVisibility {
  fullVisible: boolean;
  staffs: any[];
  roles: any[];
  departments: any[];
  departmentsIncludeChildren: boolean;
}

export interface IContent {
  type: string;
  syncFromParent: boolean;
  syncToChildren: ISyncToChildren;
  selectedFields: ISelectedField[];
  editableFields: any[];
  orders: any[];
  expansion: Expansion;
}

export interface ISyncToChildren {}

export interface ISelectedField {
  name: string;
  label: string;
  type: string;
  source: string;
  optional: boolean;
  defaultValue: any;
  formula: boolean;
  index: boolean;
  systemField: boolean;
  childrenOptional: IChildrenOptional;
  calculation: ICalculation;
  regex?: string;
  dataType: IDataType;
  entity?: string;
  withTime?: boolean;
  elemType?: IElemType;
}

export interface IChildrenOptional {}

export interface ICalculation {
  dependencies: any[];
  dependenciesBy: any[];
  order: number;
}

export interface IDataType {
  type: string;
  entity?: string;
}

export interface IElemType {
  type: string;
  entity: string;
}

export interface Expansion {
  selectedFields: ISelectedField[];
  editableFields: any[];
}
