import React, { useState, useEffect } from 'react';
import styles from './SubmitCheckingModal.module.less';
import { app } from '@ekuaibao/whispered';
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
import { Button, Icon, Progress } from 'antd';
import { confirmError } from '../../setttlement-checkin-action';
import { showMessage } from '@ekuaibao/show-util';
const Animation = app.require<any>('@elements/Animation/Animation');
const EKBIcon = app.require('@elements/ekbIcon');

export type ProgressMode = 'progress' | 'process_dot';

const INFO_MAP: any = (progressInfo: string = '') => ({
  CHECKING_SAVE: {
    SUCCESS: i18n.get('对账单拆分成功，请在「 对账概览」查看拆分结果'),
    FAILURE: i18n.get('对账单拆分失败'),
    PENDING: i18n.get(`子账单创建中...`),
  },
  CHECKING_PROGTESS: {
    SUCCESS: i18n.get(`对账单拆分成功{__k0}，请在「 对账概览」查看拆分结果`, { __k0: progressInfo }),
    FAILURE: i18n.get('对账单拆分失败'),
    PENDING: i18n.get(`子账单创建中{__k0}`, { __k0: progressInfo }),
  },
  CHECKING_FEE_PROGTESS: {
    SUCCESS: i18n.get(`重新生成费用成功{__k0}`, { __k0: progressInfo }),
    FAILURE: i18n.get('重新生成费用失败'),
    PENDING: i18n.get(`费用生成中{__k0}`, { __k0: progressInfo }),
  },
  CHECKING_SUBMIT: {
    SUCCESS: i18n.get('子对账单发起中，请稍后在对账概览中查看详情。'),
    FAILURE: i18n.get('对账单提交失败'),
    PENDING: i18n.get('对账单提交中...'),
  },
  SETTLE_SUBMIT: {
    SUCCESS: i18n.get('结算单提交成功'),
    FAILURE: i18n.get('结算单提交失败'),
    PENDING: i18n.get('结算单提交中...'),
  },
  SETTLE_SAVE: {
    SUCCESS: i18n.get('结算单创建成功'),
    FAILURE: i18n.get('结算单创建失败'),
    PENDING: i18n.get('结算单创建中...'),
  },
});
export default EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
  keyboard: false,
})(function SubmitCheckingModal(props: any) {
  const {
    layer,
    result,
    failureReasons,
    action,
    type,
    checkingBillId,
    value,
    progressMode = 'process_dot',
    closeText = '关闭',
  } = props;
  const [state, setState] = useState({ result, failureReasons, value });
  const [progress, setProgress] = useState({ total: 0, finish: 0, processing: 0 });
  async function onSuccessClick() {
    const { value } = state;
    if (type === 'CHECKING_FEE_PROGTESS') {
      layer.emitOk(value);
      return;
    }
    await confirmError({ checkingBillId, recordId: value?.id });
    layer.emitOk(value);
  }

  useEffect(() => {
    if (progressMode === 'progress') {
      return;
    }
    let timer = setInterval(async () => {
      const { result } = state;
      if (result === 'PENDING') {
        action &&
          action({ type, checkingBillId })
            .then((res: any) => {
              const resState = res?.value?.state;
              const failureReasons = res?.value?.failureReasons;
              setState({ result: resState, failureReasons, value: res?.value });
            })
            .catch((err) => {
              clearInterval(timer);
            });
      } else {
        clearInterval(timer);
      }
    }, 1000);
    return () => {
      clearInterval(timer);
    };
  }, []);

  useEffect(() => {
    let timerId: number;
    if (action && progressMode === 'progress') {
      timerId = setInterval(() => {
        action({ type, checkingBillId })
          .then(({ value }: any) => {
            setProgress(value);
            const { total, finish, processing, errorMessage } = value;
            if (finish === total || processing == 0) {
              if (errorMessage?.length) {
                setState({ result: 'FAILURE', failureReasons: errorMessage, value: '' });
              } else {
                setTimeout(() => {
                  setState({ result: 'SUCCESS', failureReasons: '', value: '' });
                }, 500);
              }
              clearInterval(timerId);
            }
          })
          .catch((err: any) => {
            clearInterval(timerId);
            showMessage.error(err?.errorMessage);
          });
      }, 1000);
    }
    return () => {
      if (timerId) {
        clearInterval(timerId);
      }
    };
  }, []);

  const fnGetProgressText = (): string => {
    if (progressMode !== 'progress') {
      return '';
    }
    if (type === 'CHECKING_PROGTESS') {
      return `，共拆分${progress?.total}条费用，已完成：${progress?.finish}条`;
    }
    if (type === 'CHECKING_FEE_PROGTESS') {
      return `，共生成${progress?.total}条费用，已完成：${progress?.finish}条`;
    }
    return '';
  };
  function renderResult() {
    const progressText = fnGetProgressText();
    const { result, failureReasons } = state;
    if (result === 'SUCCESS') {
      return (
        <div className={'success'}>
          <div>{INFO_MAP(progressText)[type]['SUCCESS']}</div>
          <Button onClick={onSuccessClick} className="success-btn" type={'primary'}>
            {type === 'CHECKING_SAVE' || type === 'CHECKING_PROGTESS'
              ? i18n.get('跳转至对账概览')
              : i18n.get(closeText)}
          </Button>
        </div>
      );
    }
    if (result === 'FAILURE') {
      return (
        <div className={'failure'}>
          <div className="title-content">
            <EKBIcon name="#EDico-plaint-circle" className="failure-icon" />
            <div className="failure-title">{INFO_MAP()[type]['FAILURE']}</div>
          </div>
          <div className="failure-content">
            {failureReasons.length && failureReasons.map((v) => <div className="failure-txt">{v}</div>)}
          </div>
          <Button onClick={onSuccessClick} className="failure-btn" type={'primary'}>
            {i18n.get('确定')}
          </Button>
        </div>
      );
    }
    return (
      <div className={'loading'}>
        <div className="animation">
          {progressMode === 'process_dot' ? (
            <Animation />
          ) : (
            <ProgressView total={progress?.total} finish={progress?.finish} processing={progress?.processing} />
          )}
        </div>
        <div className="text"> {INFO_MAP(progressText)[type]['PENDING']}</div>
      </div>
    );
  }
  return (
    <div className={styles['check-submitting']}>
      <Icon className="cross-icon" type="cross" onClick={() => layer.emitCancel()} />
      {renderResult()}
    </div>
  );
});

interface IProgressView {
  total: number;
  finish: number;
  processing: number;
}

function ProgressView(props: IProgressView) {
  let { finish, total = 1 } = props;
  total = total === 0 ? 1 : total;
  const percent = (finish * 100) / total;
  return (
    <div className="progress-animation">
      <Progress showInfo={false} percent={percent} strokeColor={'var(--brand-base)'} />
    </div>
  );
}
