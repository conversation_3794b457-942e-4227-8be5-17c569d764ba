import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import styles from './MapItem.module.less';
import { MapProps } from './CategoryFieldsMap';
const { Option } = Select;

interface Props {
  name: string; //品类名称
  sourceFields: MapProps[]; // 候选值，商城字段列表 { sourceField: 字段展示名字; targetField: 字段值 }
  maps?: MapProps[]; // 所有的映射列表
  line: any; // 品类的一个字段
  index: number;
  handleValueChange: (value?: MapProps) => void;
}
export const MapItem: React.FC<Props> = ({ line, sourceFields, name, maps, handleValueChange }) => {
  const { label, name: fieldName } = line;
  const map = maps?.find((item) => item.sourceField === fieldName);
  const [currentMap, setCurrentMap] = useState(map);
  useEffect(() => {
    if (!currentMap) {
      const map = maps?.find((item) => item.sourceField === fieldName);
      setCurrentMap(map);
    }
  }, [maps]);
  const handleSourceValueChange = (value: any) => {
    const v = {
      sourceField: fieldName,
      targetField: value,
    };
    setCurrentMap(v);
    handleValueChange(v);
  };
  const text = (
    <span>
      {i18n.get('映射到')}
      {i18n.get('「')}
      <span className="configName">{name}</span>
      {i18n.get('」')}
      {i18n.get('的')}
      {i18n.get('「')}
      {label}
      {i18n.get('」')}
    </span>
  );
  return (
    <div className={styles['conditional-item']}>
      <div className="configrule-container">
        <div className="configrule-source">{i18n.get('将合思商城订单API字段')}</div>
        <Select
          value={!currentMap?.targetField ? undefined : currentMap.targetField}
          style={{ width: 200 }}
          onChange={handleSourceValueChange}
          showSearch
          allowClear={true}
          placeholder={i18n.get('请选择')}
          optionFilterProp="children"
          dropdownClassName={styles['config-add']}
          filterOption={(input, option) => {
            return (
              typeof option.props.children === 'string' &&
              option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            );
          }}
        >
          {sourceFields.map((item, kIndex) => (
            <Option key={kIndex} value={item.targetField}>
              {item.sourceField}
            </Option>
          ))}
        </Select>
        <div className="configrule-target">{text}</div>
      </div>
    </div>
  );
};
export default MapItem;
