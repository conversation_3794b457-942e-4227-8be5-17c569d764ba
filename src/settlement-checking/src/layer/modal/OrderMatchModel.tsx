import React, { useCallback, useState } from 'react';
import { I<PERSON>, Button } from 'antd';
import { T } from '@ekuaibao/i18n';
import styles from './Modal.module.less';
import OrderDataLinkTable from '../../supplier-bill/OrderMatchDetail/OrderDataLinkTable';
import { manualMatchTask } from '../../setttlement-checkin-action';
import MessageCenter from '@ekuaibao/messagecenter';

interface IPorps {
  layer?: any;
  data?: any;
  line?: any;
  [key: string]: any;
}

const OrderMatchModel: React.FC<IPorps> = (props: IPorps) => {
  const { data, line } = props;
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const onSelectedChange = (selectedRowKeys: any) => {
    setSelectedRowKeys(selectedRowKeys);
  };
  const renderAction = (bus: MessageCenter, length: number) => {
    return {
      title: i18n.get('操作'),
      dataIndex: 'action',
      key: 'action',
      width: 30,
      label: i18n.get('操作'),
      value: 'action',
      fixed: length > 3 ? 'right' : false,
      render(text, line) {
        return (
          <div className={styles['action-btn']} onClick={() => onBind(line)}>
            绑定
          </div>
        );
      },
    };
  };
  const handleSubmit = async () => {
    await manualMatchTask({
      tripOrderId: selectedRowKeys,
      checkingDetailId: [line?.dataLink?.id],
    });
    props?.layer?.emitOk();
  };
  const onBind = async (orderline: any) => {
    await manualMatchTask({ tripOrderId: [orderline?.dataLink?.id], checkingDetailId: [line?.dataLink?.id] });
    props?.layer?.emitOk();
  };
  const handleClose = useCallback(() => {
    props.layer.emitCancel();
  }, []);
  return (
    <div className={styles['add_bill_modal']}>
      <div className="modal_header">
        <div className="flex title">
          <T name="匹配订单" />
        </div>
        <Icon className="cross_icon" type="cross" onClick={handleClose} />
      </div>
      <div className="modal_content">
        <OrderDataLinkTable
          selectedRowKeys={selectedRowKeys}
          onSelectedChange={onSelectedChange}
          renderAction={renderAction}
          data={data}
        />
      </div>
      <div className="modal-footer">
        <Button className="btn-ml mr-8" onClick={handleClose}>
          {i18n.get('取消')}
        </Button>
        <Button type="primary" className="btn-ml mr-8" onClick={handleSubmit}>
          {i18n.get('匹配')}
        </Button>
      </div>
    </div>
  );
};
export default OrderMatchModel;
