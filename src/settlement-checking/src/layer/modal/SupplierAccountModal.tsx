/*
 * @Description: 供应商账户编辑弹层
 * @Creator: chencan<PERSON>han
 * @Date: 2021-06-23 18:33:03
 */
import React, { PureComponent } from 'react';
import { app, app as api } from '@ekuaibao/whispered';
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
import { List, Form, Input, Select, Button, InputNumber, Icon } from 'antd';
const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;
import { T } from '@ekuaibao/i18n';
import { showMessage } from '@ekuaibao/show-util';
import { get } from 'lodash';
import styles from './Modal.module.less';
const { standardValueMoney } = api.require('@lib/misc');
import { getSupplierArchiveList } from './../../setttlement-checkin-action';
import { SelectValue } from 'antd/lib/select';

@EnhanceFormCreate()
export default class SupplierAccountDrawer extends PureComponent {
  state = {
    supplierArchiveType: '',
    archiveList: [],
    billPeriodType: '',
  };
  private data = [
    {
      name: i18n.get('周'),
      value: 'WEEK',
    },
    {
      name: i18n.get('双周'),
      value: 'BIWEEKLY',
    },
    {
      name: i18n.get('月'),
      value: 'MONTH',
    },
    {
      name: i18n.get('季度'),
      value: 'SEASON',
    },
    {
      name: i18n.get('半年'),
      value: 'HALFYEAR',
    },
    {
      name: i18n.get('年'),
      value: 'YEAR',
    },
    {
      name: i18n.get('不定期'),
      value: 'IRREGULAR',
    },
  ];
  private data2 = [
    {
      name: i18n.get('是'),
      value: true,
    },
    {
      name: i18n.get('否'),
      value: false,
    },
  ];
  private save = () => {
    const { form } = this.props;
    form?.validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }
      if (!values?.billPeriod?.type) {
        //如果没有type说明这个对象就没有值，直接删除就行
        delete values.billPeriod;
      }
      const creditAmount = standardValueMoney(get(values, 'creditAmount', 0));
      const balanceAmount = standardValueMoney(get(values, 'balanceAmount', 0));
      this.props?.layer?.emitOk({
        ...values,
        creditAmount,
        balanceAmount,
        supplierArchiveType: this.state?.supplierArchiveType,
      });
    });
  };

  fnGenerateDays = (type: string): IDay[] => {
    const { billPeriodType } = this.state;
    if (!billPeriodType?.length) {
      return [];
    }
    if (billPeriodType === 'NATURAL_MONTH') {
      return type === 'start' ? [{ label: '当月1日', value: '1' }] : [{ label: '当月最后一天', value: '31' }];
    }
    const startIndex = type === 'start' ? 2 : 1;
    const endIndex = type === 'start' ? 31 : 30;
    const prefix = type === 'start' ? '当月' : '次月';
    const days = [];
    for (let index = startIndex; index <= endIndex; index++) {
      days.push({ label: `${prefix}${index}日`, value: index + '' });
    }
    return days;
  };

  componentDidMount() {
    getSupplierArchiveList({
      select: 'id,name,supplierArchiveType,active',
      orderBy: [{ value: 'name', order: 'DESC' }],
      filterBy: '(active==true)',
    })
      .then((res) => {
        // HOSE_TRIP
        const archiveList = get(res, 'items', []);
        this.setState({ archiveList });
      })
      .catch((err) => {
        showMessage.error(err?.message || err?.errMessage);
      });
    const { data } = this.props;
    this.setState({ billPeriodType: get(data, 'billPeriod.type', '') });
  }

  handleBillPeriodTypeChange = (value: SelectValue) => {
    this.setState({ billPeriodType: value });
    const { form } = this.props;
    if (!(value as string)?.length) {
      form.setFieldsValue({ ['billPeriod.startTime']: undefined, ['billPeriod.endTime']: undefined });
      return;
    }
    if (value === 'NATURAL_MONTH') {
      form.setFieldsValue({ ['billPeriod.startTime']: '1', ['billPeriod.endTime']: '31' });
    } else {
      form.setFieldsValue({ ['billPeriod.startTime']: '2', ['billPeriod.endTime']: '1' });
    }
  };

  handleBillPeriodStartTimeChange = (value: SelectValue) => {
    const { billPeriodType } = this.state;
    if (billPeriodType === 'ACROSS_MONTH') {
      const { form } = this.props;
      let end = Number(value) - 1;
      if (!end) {
        end = 31;
      }
      form.setFieldsValue({ ['billPeriod.endTime']: end + '' });
    }
  };

  handleBillPeriodEndTimeChange = (value: SelectValue) => {
    const { billPeriodType } = this.state;
    if (billPeriodType === 'ACROSS_MONTH') {
      const { form } = this.props;
      let start = Number(value) + 1;
      if (start > 31) {
        start = 1;
      }
      form.setFieldsValue({ ['billPeriod.startTime']: start + '' });
    }
  };

  render() {
    const formItemLayout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
    };
    const {
      form: { getFieldDecorator },
      data,
    } = this.props;
    const { archiveList = [] } = this.state;

    return (
      <div className={styles['add_bill_modal']}>
        <div className="modal_header">
          <div className="flex title">
            <T name={this.props?.title} />
          </div>
          <Icon
            className="cross_icon"
            type="cross"
            onClick={() => {
              this.props?.layer?.emitCancel();
            }}
          />
        </div>
        <div className="modal_content">
          <List>
            <Form>
              <FormItem {...formItemLayout} className={'form_item'} label="账户名称">
                {getFieldDecorator('accountName', {
                  initialValue: get(data, 'name') || '',
                  rules: [{ required: true, message: i18n.get('请输入账户名称') }],
                })(
                  <Input
                    disabled={data?.supplierArchiveType === 'HOSE_TRIP'}
                    placeholder={i18n.get('请输入账户名称')}
                  />,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="供应商">
                {getFieldDecorator('supplierArchiveId', {
                  initialValue: this.props?.supplierArchiveId
                    ? this.props?.supplierArchiveId
                    : get(data, 'supplierArchiveId.id') || '',
                  rules: [{ required: true, message: i18n.get('请选择供应商') }],
                })(
                  <Select
                    showArrow={true}
                    onChange={(id) => {
                      for (const it of archiveList) {
                        if (it?.id === id) {
                          this.setState({ supplierArchiveType: it?.supplierArchiveType });
                          break;
                        }
                      }
                    }}
                    disabled={!!data || !!this.props?.supplierArchiveId}
                    allowClear
                    placeholder={i18n.get('请选择供应商')}
                  >
                    {archiveList?.map((it) => {
                      return (
                        <Option
                          key={it?.id}
                          value={it?.id}
                          disabled={it?.supplierArchiveType === 'HOSE_TRIP' ? true : !it?.active}
                        >
                          {it?.name}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="账户类型">
                {getFieldDecorator('settlementType', {
                  initialValue: get(data, 'settlementType') === 'OTHER' ? '' : get(data, 'settlementType'),
                  rules: [{ required: true, message: i18n.get('请选择账户类型') }],
                })(
                  <Select
                    showArrow={true}
                    disabled={get(data, 'settlementType') === 'OTHER' ? false : !!data}
                    allowClear
                    placeholder={i18n.get('请选择账户类型')}
                  >
                    <Option value="CREDIT">{i18n.get('授信')}</Option>
                    <Option value="PRIECHARGE">{i18n.get('预存')}</Option>
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="结算周期">
                {getFieldDecorator('period', {
                  initialValue: get(data, 'period') || undefined,
                })(
                  <Select showArrow={true} allowClear placeholder={i18n.get('请选择结算周期')}>
                    {this.data?.map((it) => {
                      return (
                        <Option key={it?.value} value={it?.value}>
                          {it?.name}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="总金额">
                {getFieldDecorator('creditAmount', {
                  initialValue: get(data, 'creditAmount.standard') || '',
                })(
                  <InputNumber
                    disabled={data?.supplierArchiveType === 'HOSE_TRIP'}
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                  />,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="剩余可用金额">
                {getFieldDecorator('balanceAmount', {
                  initialValue: get(data, 'balanceAmount.standard') || '',
                })(
                  <InputNumber
                    disabled={data?.supplierArchiveType === 'HOSE_TRIP'}
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                  />,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="账单日">
                {getFieldDecorator('billingDay', {
                  initialValue: get(data, 'billingDay') || '',
                })(
                  <Select showArrow={true} allowClear placeholder={i18n.get('请选择账单日')}>
                    {[...Array(28).keys()].map((it) => {
                      return (
                        <Option key={it} value={String(it + 1)}>
                          {it + 1}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="还款日">
                {getFieldDecorator('repaymentDay', {
                  initialValue: get(data, 'repaymentDay') || '',
                })(
                  <Select showArrow={true} allowClear placeholder={i18n.get('请选择还款日')}>
                    {[...Array(28).keys()].map((it) => {
                      return (
                        <Option key={it} value={String(it + 1)}>
                          {it + 1}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="是否对账">
                {getFieldDecorator('isReconciliation', {
                  initialValue: get(data, 'isReconciliation', undefined),
                })(
                  <Select showArrow={true} allowClear placeholder={i18n.get('请选择是否对账')}>
                    {this.data2.map((it) => {
                      return (
                        <Option key={it?.value} value={it?.value}>
                          {it?.name}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="是否结算">
                {getFieldDecorator('isSettlement', {
                  initialValue: get(data, 'isSettlement', undefined),
                })(
                  <Select showArrow={true} allowClear placeholder={i18n.get('请选择是否结算')}>
                    {this.data2.map((it) => {
                      return (
                        <Option key={it?.value} value={it?.value}>
                          {it?.name}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="账单导入方式">
                {getFieldDecorator('importMethod', {
                  initialValue: get(data, 'importMethod', 'excel'),
                })(
                  <Select showArrow={true} placeholder={i18n.get('账单导入方式')}>
                    <Option key={'excel'} value={'excel'}>
                      手动导入
                    </Option>
                    <Option key={'api'} value={'api'}>
                      API同步
                    </Option>
                    <Option key='directly' value='directly'>
                      API写入
                    </Option>
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="供应商账单期间配置">
                {getFieldDecorator('billPeriod.type', {
                  initialValue: get(data, 'billPeriod.type'),
                })(
                  <Select
                    showArrow={true}
                    placeholder={i18n.get('请选择供应商账单期间配置')}
                    disabled={data?.supplierArchiveType === 'HOSE_TRIP'}
                    allowClear
                    onChange={this.handleBillPeriodTypeChange}
                  >
                    <Option key={'NATURAL_MONTH'} value={'NATURAL_MONTH'}>
                      自然月
                    </Option>
                    <Option key={'ACROSS_MONTH'} value={'ACROSS_MONTH'}>
                      跨月
                    </Option>
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="开始时间">
                {getFieldDecorator('billPeriod.startTime', {
                  initialValue: get(data, 'billPeriod.startTime'),
                })(
                  <Select
                    showArrow={true}
                    placeholder={i18n.get('请选择开始时间')}
                    disabled={data?.supplierArchiveType === 'HOSE_TRIP'}
                    onChange={this.handleBillPeriodStartTimeChange}
                  >
                    {this.fnGenerateDays('start').map((day) => {
                      return (
                        <Option key={day.value} value={day.value}>
                          {day.label}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="结束时间">
                {getFieldDecorator('billPeriod.endTime', {
                  initialValue: get(data, 'billPeriod.endTime'),
                })(
                  <Select
                    showArrow={true}
                    placeholder={i18n.get('请选择结束时间')}
                    disabled={data?.supplierArchiveType === 'HOSE_TRIP'}
                    onChange={this.handleBillPeriodEndTimeChange}
                  >
                    {this.fnGenerateDays('end').map((day) => {
                      return (
                        <Option key={day.value} value={day.value}>
                          {day.label}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="描述">
                {getFieldDecorator('description', {
                  initialValue: get(data, 'description') || '',
                })(<TextArea rows={4} placeholder={i18n.get('请输入描述文字')} />)}
              </FormItem>
            </Form>
          </List>
        </div>
        <div className="modal_footer">
          <Button
            className="btn-ml mr-8"
            onClick={() => {
              this.props?.layer?.emitCancel();
            }}
          >
            {i18n.get('取消')}
          </Button>
          <Button type="primary" className="btn-ml mr-8" onClick={this.save}>
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    );
  }
}

interface IDay {
  label: string;
  value: string;
}
