import React, { PureComponent } from 'react';
import { app } from '@ekuaibao/whispered';
import { QuerySelect } from 'ekbc-query-builder';
const EnhanceFormCreate: any = app.require('@elements/enhance/enhance-form-create');
import { SpecificationGroup, SpecificationIF } from '@ekuaibao/ekuaibao_types';
import { List, Form, Input, Select, Button, Icon, Radio, Checkbox } from 'antd';
import { T } from '@ekuaibao/i18n';
const FormItem = Form.Item;
const { Option } = Select;
import { get } from 'lodash';
import { showMessage } from '@ekuaibao/show-util';
import { getSupplierAccountList } from './../../setttlement-checkin-action';
import styles from './Modal.module.less';
import MessageCenter from '@ekuaibao/messagecenter';
import { EnhanceConnect } from '@ekuaibao/store';
interface IProps {
  [key: string]: any;
}
interface IState {
  [key: string]: any;
}
const settlementDimensionCheck = (data: any) => data?.settlementDimension?.type;

@EnhanceConnect((state: any) => ({
  globalFieldsMap: state['@common'].globalFields.baseDataPropertiesMap,
}))
@EnhanceFormCreate()
export default class SettleBillConfigModal extends PureComponent<IProps, IState> {
  bus = new MessageCenter();
  constructor(props: IProps) {
    super(props);
    this.state = {
      accountList: [],
      paymentList: [],
      customList: [],
      textArr: [],
      PayeeInfoArr: [],
      moneyArr: [],
      basedataArr: [],
      basedataDimension: [],
      department: [],
      billList: [],
      settlementType: '',
      supplierArchiveId: null,
    };
  }
  handleClose = () => {
    this.props?.layer?.emitCancel();
  };
  private save = () => {
    const { form, data = {} } = this.props;
    const { textArr, PayeeInfoArr, moneyArr, basedataArr, settlementType, basedataDimension, department } = this.state;
    let shouldReset = false;
    form?.validateFieldsAndScroll((err: any, values: any) => {
      if (err) {
        return;
      }
      if (
        values.supplierAccountId !== data.supplierAccountId ||
        values.specificationId !== data.specificationId ||
        values.settlementDimension?.type !== settlementDimensionCheck(this.props?.data)
      ) {
        shouldReset = true;
      }
      this.props?.layer?.emitOk({
        data: {
          rules: shouldReset ? [] : data.rules,
          mappings: shouldReset ? [] : data.mappings,
          ...values,
          active: this.props?.data?.active,
          id: this.props?.data?.id,
        },
        assignRules: { textArr, PayeeInfoArr, moneyArr, basedataArr, settlementType, basedataDimension, department },
      });
    });
  };
  async componentDidMount() {
    await this.fetchOptions(null);
    this.getSupplierAccountList();
  }
  fetchOptions = async (settlementType) => {
    const specificationGroupsList = await app.invokeService(
      '@custom-specification:get:specificationGroups:withSpecificationVersioned',
    );
    const specifications: SpecificationGroup[] = (specificationGroupsList && specificationGroupsList.items) || [];
    let paymentList: SpecificationIF[] = [];
    let customList: SpecificationIF[] = [];
    specifications.forEach((e) => {
      e?.specifications.forEach((oo: SpecificationIF) => {
        if (oo.type === 'payment') {
          paymentList.push(oo);
        }
        if (oo.type === 'custom') {
          customList.push(oo);
        }
      });
    });
    if (settlementType) {
      this.setArr(settlementType, paymentList, customList);
    }
    this.setState({ paymentList, customList });
  };
  setArr = (settlementType: any, paymentList: any, customList: any) => {
    const billList = settlementType === 'CREDIT' ? paymentList : customList;
    const id = this.props?.data?.specificationId;
    this.filterData(billList, id);
  };
  filterData = (billList: Array<any>, id: any) => {
    const { data = {} } = this.props;
    const __type = settlementDimensionCheck(data)
    for (const it of billList) {
      if (it?.originalId === id) {
        const components = it?.components?.map((it) => this.props?.globalFieldsMap[it?.field]);
        const textArr: any = [],
          PayeeInfoArr: any = [],
          moneyArr: any = [],
          basedataArr: any = [],
          basedataDimension: any = [],
          department: any = [];
        it?.components?.forEach((it) => {
          if (it?.type === 'text') {
            return textArr?.push({ ...it, name: it?.field });
          }
          if (it?.type === 'money') {
            return moneyArr?.push({ ...it, name: it?.field });
          }
        });
        components?.forEach((it) => {
          if (it?.dataType?.type === 'ref') {
            if (it?.dataType?.entity === 'pay.PayeeInfo') {
              return PayeeInfoArr?.push(it);
            }
            if (it?.dataType?.entity === 'basedata.Dimension.法人实体'&&__type!=='statementField') {
              return basedataArr?.push(it);
            }
            if (it?.dataType?.entity.startsWith('basedata.Dimension')) {
              return basedataDimension?.push(it);
            }
            if (it?.dataType?.entity === 'organization.Department') {
              return department?.push(it);
            }
          }
        });
        this.setState({ textArr, PayeeInfoArr, moneyArr, basedataArr, basedataDimension, department });
        break;
      }
    }
  };
  async getSupplierAccountList() {
    const query = new QuerySelect();
    query.select('id,name,settlementType');
    query.filterBy('active==true');
    getSupplierAccountList(query.value())
      .then((res) => {
        const accountList = get(res, 'items', []);
        this.setState({ accountList }, () => {
          if (this.props?.data) {
            const id = this.props?.data?.supplierAccountId;
            for (const it of accountList) {
              if (it?.id === id) {
                this.setState({ settlementType: it?.settlementType }, () => {
                  const { paymentList, customList } = this.state;
                  if (paymentList?.length > 0 || customList?.length > 0) {
                    this.setArr(it?.settlementType, paymentList, customList);
                  } else {
                    this.fetchOptions(it?.settlementType);
                  }
                });
                break;
              }
            }
          }
        });
      })
      .catch((err) => {
        showMessage.error(err?.message || err?.errMessage);
      });
  }
  render() {
    const {
      form: { getFieldDecorator, getFieldValue },
    } = this.props;
    const { accountList = [], paymentList = [], customList = [], billList = [], settlementType } = this.state;

    return (
      <div className={styles['add_bill_modal']}>
        <div className="modal_header">
          <div className="flex title">
            <T name="结算单配置" />
          </div>
          <Icon className="cross_icon" type="cross" onClick={this.handleClose} />
        </div>
        <div className="modal_content">
          <List>
            <Form layout="vertical">
              <FormItem className={'form_item'} label="配置名称">
                {getFieldDecorator('name', {
                  initialValue: get(this.props?.data, 'name') || '',
                  rules: [{ required: true, message: i18n.get('请输入配置名称') }],
                })(<Input placeholder={i18n.get('请输入配置名称')} />)}
              </FormItem>
              <FormItem className={'form_item'} label="配置描述">
                {getFieldDecorator('description', {
                  initialValue: get(this.props?.data, 'description') || '',
                  rules: [],
                })(<Input placeholder={i18n.get('请输入配置描述')} />)}
              </FormItem>
              <div className="sipp"></div>
              <FormItem className={'form_item'} label="供应商账户">
                {getFieldDecorator('supplierAccountId', {
                  initialValue: get(this.props?.data, 'supplierAccountId') || '',
                  rules: [{ required: true, message: i18n.get('请选择供应商账户') }],
                })(
                  <Select
                    placeholder={i18n.get('请选择供应商账户')}
                    onChange={(id) => {
                      for (const it of accountList) {
                        if (it?.id === id) {
                          const List = it?.settlementType === 'CREDIT' ? paymentList : customList;
                          this.setState({ settlementType: it?.settlementType, billList: List }, () => {
                            this.props?.form?.setFieldsValue({ specificationId: '', components: [] });
                            this?.bus?.emit('assignRule:resetValue');
                          });
                          break;
                        }
                      }
                    }}
                  >
                    {accountList.map(
                      (it: {
                        id: React.Key | null | undefined;
                        name: boolean | React.ReactChild | React.ReactFragment | React.ReactPortal | null | undefined;
                      }) => {
                        return (
                          <Option key={it?.id} value={it?.id}>
                            {it?.name}
                          </Option>
                        );
                      },
                    )}
                  </Select>,
                )}
              </FormItem>
              <FormItem className={'form_item'} label="单据模板">
                {getFieldDecorator('specificationId', {
                  initialValue: get(this.props?.data, 'specificationId') || '',
                  rules: [{ required: true, message: i18n.get('请选择单据模板') }],
                })(
                  <Select
                    placeholder={i18n.get('请选择单据模板')}
                    onChange={(id) => {
                      this.filterData(settlementType === 'CREDIT' ? paymentList : customList, id);
                    }}
                  >
                    {(settlementType === 'CREDIT' ? paymentList : customList).map(
                      (it: {
                        originalId: React.Key | null | undefined;
                        name: boolean | React.ReactChild | React.ReactFragment | React.ReactPortal | null | undefined;
                      }) => {
                        return (
                          <Option key={it?.originalId} value={it?.originalId}>
                            {it?.name}
                          </Option>
                        );
                      },
                    )}
                  </Select>,
                )}
              </FormItem>
              <FormItem className={'form_item'} label={i18n.get('结算维度')}>
                {getFieldDecorator('settlementDimension.type', {
                  initialValue: get(this.props?.data, 'settlementDimension.type') ?? 'legalEntity',
                })(
                  <Radio.Group onChange={(id) => {
                    this.filterData(settlementType === 'CREDIT' ? paymentList : customList, id);
                  }}>
                    <Radio value={'legalEntity'}>{i18n.get('法人实体')}</Radio>
                    <Radio value={'statementField'}>{i18n.get('对账单字段')}</Radio>
                  </Radio.Group>,
                )}
              </FormItem>
              {getFieldValue('settlementDimension.type') === 'legalEntity' && (
                <FormItem className={'form_item'} style={{ paddingLeft: 24 }}>
                  {getFieldDecorator('settlementDimension.details', {
                    initialValue: get(this.props?.data, 'settlementDimension.details') || true,
                  })(
                    <Radio.Group>
                      <Radio value={true}>{i18n.get('费用明细')}</Radio>
                    </Radio.Group>,
                  )}
                </FormItem>
              )}
              {getFieldValue('settlementDimension.type') === 'legalEntity' && (
                <FormItem className={'form_item'} style={{ paddingLeft: 48 }}>
                  {getFieldDecorator('settlementDimension.apportions', {
                    initialValue: get(this.props?.data, 'settlementDimension.apportions') || '',
                    valuePropName: 'checked',
                  })(<Checkbox>{i18n.get('包含分摊明细')}</Checkbox>)}
                </FormItem>
              )}
            </Form>
          </List>
        </div>
        <div className="modal_footer">
          <Button
            className="btn-ml mr-8"
            onClick={() => {
              this.props?.layer?.emitCancel();
            }}
          >
            {i18n.get('取消')}
          </Button>
          <Button type="primary" className="btn-ml mr-8" onClick={this.save}>
            {i18n.get('下一步')}
          </Button>
        </div>
      </div>
    );
  }
}
