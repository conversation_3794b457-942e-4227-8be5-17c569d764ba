import React, { useEffect, useState } from 'react';
import styles from './CategoryFieldsMap.module.less';
import { Button, Icon } from 'antd';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
import MapItem from './MapItem';
import { cloneDeep, findIndex } from 'lodash';
import { getFieldsMapping, saveFieldsMapping } from '../../setttlement-checkin-action';
import { showMessage } from '@ekuaibao/show-util';

interface Props extends ILayerProps {
  account: any; // 账户
  category: any; // 品类
}

export interface MapProps {
  sourceField: string; // 品类的字段名；name
  targetField: string; // 商城测的字段名
}

export const CategoryFieldsMap: React.FC<Props> = (props) => {
  const { account, category, layer } = props;
  const [sourceFields, setSourceFields] = useState([]); // 候选值
  const [maps, setMaps] = useState<MapProps[]>([]); // 字段取值规则
  useEffect(() => {
    getMapping();
  }, []);
  const getMapping = async () => {
    const params = {
      entityId: category.id,
    };
    const response = await getFieldsMapping(params);
    const data = response?.items;
    const sourceFS = data?.find((item: { id: string }) => item.id === 'default').mappings;
    const mappings = data?.find((item: { id: string }) => item.id === category.id).mappings;
    setSourceFields(sourceFS);
    setMaps(mappings);
  };
  const handleValueChange = (value?: MapProps) => {
    if (!value) return;
    const index = findIndex(maps, (item) => item.sourceField === value?.sourceField);
    const result = cloneDeep(maps);
    if (index > -1) {
      result.splice(index, 1, value);
    } else {
      result.push(value);
    }
    setMaps(result);
  };
  const renderItem = () => {
    return category?.fields?.map((line: any, index: any) => {
      return (
        <MapItem
          key={line.name}
          line={line}
          index={index}
          sourceFields={sourceFields}
          handleValueChange={handleValueChange}
          name={category?.name?.replace(`${account?.name}-`, '')}
          maps={maps}
        />
      );
    });
  };
  const handleClose = () => {
    layer?.emitCancel();
  };
  const handleConfirm = async () => {
    try {
      const params = {
        entityId: category.id,
        supplierAccountId: account.id,
        mappings: maps,
      };
      await saveFieldsMapping(params);
      showMessage.success(i18n.get('保存成功'));
      layer?.emitOk({});
    } catch (e) {
      showMessage(e.message);
    }
  };
  return (
    <div className={styles['config-rules-wrapper']}>
      <div className="config-rules-header">
        <div className="flex">{i18n.get('字段映射规则确认')}</div>
        <Icon className="cross-icon" type="cross" onClick={handleClose} />
      </div>
      <div className="config-rules-content">{renderItem()}</div>
      <div className="modal-footer">
        <Button className="config-btn" onClick={handleClose}>
          {i18n.get('取  消')}
        </Button>
        <Button type="primary" className="btn-ml" onClick={handleConfirm}>
          {i18n.get('确  定')}
        </Button>
      </div>
    </div>
  );
};
export default CategoryFieldsMap;
