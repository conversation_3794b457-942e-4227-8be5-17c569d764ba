/*
 * @Author: <PERSON>
 * @Date: 2022-04-19 14:12:15
 * @LastEditTime: 2022-06-21 16:14:45
 * @LastEditors: Hunter
 * @Description:
 * @FilePath: \plugin-web-settlement-checking\src\layer\modal\SettleBillMenuConfigModal.tsx
 * 可以输入预定的版权声明、个性签名、空行等
 */
import React, { PureComponent } from 'react';
import { app } from '@ekuaibao/whispered';
const EnhanceFormCreate: any = app.require('@elements/enhance/enhance-form-create');
import { List, Form, Button, Icon } from 'antd';
import { T } from '@ekuaibao/i18n';
const FormItem = Form.Item;
import { get } from 'lodash';
import { showMessage } from '@ekuaibao/show-util';
import { setConfigSettlement, putConfigSettlement, getFeeConfigList } from './../../setttlement-checkin-action';
import styles from './Modal.module.less';
import ZY_AssignRule from '../../components/ZY_AssignRule';
import AssignRuleOthers from '../../components/AssignRuleOthers';
import MessageCenter from '@ekuaibao/messagecenter';
import { EnhanceConnect } from '@ekuaibao/store';
interface IProps {
  [key: string]: any;
}
interface IState {
  [key: string]: any;
}
const isSettlementDimension = (data: any) => {
  return data?.settlementDimension?.type === 'statementField';
};
const checkFieldType = (type: 'department' | 'dimension' | string, fieldVal: any) => {
  switch (type) {
    case 'department':
      return fieldVal?.dataType?.entity === 'organization.Department' ?? false;
    case 'dimension':
      return fieldVal?.dataType?.entity?.startsWith('basedata.Dimension') ?? false;
    default:
      return false;
  }
};
const formatRule = (items: Array<any>, globalFieldsMap: any) => {
  let ruleList: Array<any> = [],
    duplicate: any = {}
  items.forEach((item: any) => {
    item.mappings?.forEach((item: any, i: number) => {
      const fieldKey = item?.sourceField ?? i ?? '';
      const fieldVal = globalFieldsMap[fieldKey];
      const dimension = checkFieldType('dimension', fieldVal);
      const department = checkFieldType('department', fieldVal);
      if (!duplicate[fieldKey] && (dimension || department)) {
        const _fieldVal = { ...fieldVal, fieldType: fieldVal?.dataType?.entity };
        duplicate[fieldKey] = _fieldVal;
        ruleList.push(_fieldVal);
      }
    });
  });
  return { ruleList, duplicate };
};
@EnhanceConnect((state: any) => ({
  globalFieldsMap: state['@common'].globalFields.baseDataPropertiesMap,
}))
@EnhanceFormCreate()
export default class SettleBillMenuConfigModal extends PureComponent<IProps, IState> {
  bus = new MessageCenter();
  constructor(props: {} | Readonly<{}>) {
    super(props);
    this.state = {
      ruleList: [''],
      rules: [],
    };
  }
  componentDidMount = () => {
    const { data = {} } = this.props;
    if (isSettlementDimension(data)) {
      this.initRuleSelect(data);
    }
  };
  initRuleSelect = async (data: any) => {
    const { form, globalFieldsMap } = this.props;
    const { items } = await getFeeConfigList();
    const { ruleList } = formatRule(
      items.filter((item: any) => item.supplierAccountId?.id === data?.supplierAccountId),
      globalFieldsMap,
    );
    form.setFieldsValue({ rules: data?.rules ?? [] });
    this.setState({ ruleList, rules: data?.rules ?? [] });
  };

  handleClose = () => {
    this.props?.layer?.emitCancel();
  };
  private lastStep = async () => {
    const { form, data = {} } = this.props;
    const value = await form?.getFieldsValue();
    this.props?.layer?.emitOk({ lastStep: true, data: { ...data, ...value } });
  };
  private save = () => {
    const { form, data = {} } = this.props;
    form?.validateFieldsAndScroll((err: any, values: any) => {
      if (err) {
        return;
      }
      if (
        values.mappings.length === 0 ||
        (values?.mappings?.length > 0 && values?.mappings?.find((item: any) => item.targetField === ''))
      ) {
        return showMessage.error('请选择赋值规则');
      }
      if (
        values?.rules?.length === 0 ||
        (values?.rules?.length > 0 && values?.rules?.find((item: string) => item === ''))
      ) {
        return showMessage.error('请选择对账单字段');
      }
      if (
        values?.mappings?.length > 0 &&
        values?.rules?.length > 0 &&
        values?.mappings?.filter((item: any) => values?.rules.includes(item.sourceField))?.length !==
          values?.rules?.length
      ) {
        return showMessage.error('请选择赋值规则');
      }
      if (this.props?.data?.id) {
        putConfigSettlement({ ...data, ...values, active: data?.active, id: data?.id })
          .then((res) => {
            this.props?.layer?.emitOk({});
          })
          .catch((err) => {
            showMessage.error(err.message);
          });
      } else {
        setConfigSettlement({ ...data, ...values })
          .then((res) => {
            this.props?.layer?.emitOk({});
          })
          .catch((err) => {
            showMessage.error(err.message);
          });
      }
    });
  };
  onChangeAssignRuleOthers = (rules: any) => {
    const { form } = this.props;
    form.setFieldsValue({ rules });
    this.setState({ rules });
  };
  checkPrice = (_: any, value: any, callback: (str?: string) => void) => {
    if (value?.length > 0) {
      for (let i = 0; i < value?.length; i++) {
        const it = value[i];
        if (!it?.targetField) {
          return callback('请填写规则');
        }
      }
    }
    callback();
  };
  render() {
    const {
      form: { getFieldDecorator },
      assignRules = {},
      data = {},
    } = this.props;
    const { ruleList, rules } = this.state;
    const {
      settlementType,
      textArr = [],
      PayeeInfoArr = [],
      moneyArr = [],
      basedataArr = [],
      basedataDimension = [],
      department = [],
    } = assignRules;
    return (
      <div className={styles['add_bill_modal']}>
        <div className="modal_header">
          <div className="flex title">
            <T name="结算单配置" />
          </div>
          <Icon className="cross_icon" type="cross" onClick={this.handleClose} />
        </div>
        <div className="modal_content">
          <List>
            <Form layout="vertical">
              {isSettlementDimension(data) && (
                <FormItem className={'form_item'} label="选择对账单字段">
                  {getFieldDecorator('rules', {
                    initialValue: get(data, 'rules', [ruleList?.[0]?.label ?? '']),
                    rules: [{ required: true, message: '请选择对账单字段' }],
                  })(<AssignRuleOthers ruleList={ruleList} onChange={this.onChangeAssignRuleOthers} bus={this.bus} />)}
                </FormItem>
              )}
              <FormItem className={'form_item'} label="配置赋值规则">
                {getFieldDecorator('mappings', {
                  initialValue: get(data, 'mappings', []) || [],
                  rules: [{ required: true, validator: this.checkPrice }],
                })(
                  <ZY_AssignRule
                    isSettlementDimension={isSettlementDimension(data)}
                    bus={this.bus}
                    optionsList={{ textArr, PayeeInfoArr, moneyArr, basedataArr, basedataDimension, department }}
                    show={settlementType === 'CREDIT'}
                    rules={rules}
                    ruleList={ruleList}
                  />,
                )}
              </FormItem>
            </Form>
          </List>
        </div>
        <div className="modal_footer">
          <Button className="btn-ml mr-8" onClick={this.lastStep}>
            {i18n.get('上一步')}
          </Button>
          <Button type="primary" className="btn-ml mr-8" onClick={this.save}>
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    );
  }
}
