.conditional-item {
  width: 100%;
  height: 48px;
  border-radius: 4px;
  background-color: #fafafa;
  margin-bottom: 4px;
  padding: 0 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  :global {
    .configrule-container {
      display: flex;
      align-items: center;
      .configrule-source {
        display: flex;
        height: 22px;
        font-size: 14px;
        line-height: 22px;
        text-align: justify;
        color: #262626;
        margin-right: 8px;
        .configName {
          max-width: 100px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .configrule-target {
        height: 22px;
        font-size: 14px;
        line-height: 22px;
        text-align: justify;
        color: #262626;
        margin: 0 8px;
      }
    }
  }
}
