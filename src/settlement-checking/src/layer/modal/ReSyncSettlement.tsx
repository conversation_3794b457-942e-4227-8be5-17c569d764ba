/*
 * @Author: <PERSON>
 * @Date: 2022-11-08 17:26:30
 * @LastEditTime: 2022-11-16 16:15:34
 * @LastEditors: Hunter
 * @Description:
 * @FilePath: \plugin-web-settlement-checking\src\layer\modal\ReSyncSettlement.tsx
 * 可以输入预定的版权声明、个性签名、空行等
 */
import React, { useCallback } from 'react';
import { Button, Icon, Form } from 'antd';
import { T } from '@ekuaibao/i18n';
import styles from './Modal.module.less';
import { Selects } from '../../components/Select';
const FormItem = Form.Item;
interface IPorps {
  layer?: any;
  value: any;
  form: any;
  settlementVm: any;
  [key: string]: any;
}

const ReSyncSettlement: React.FC<IPorps> = (props: IPorps) => {
  const { value, form, settlementVm } = props;
  const { getFieldDecorator } = form;
  const { getDepParams, getDatasource } = settlementVm;
  const depParams = getDepParams()
  const handleClose = useCallback(() => {
    props?.layer?.emitCancel();
  }, []);
  const handleSubmit = useCallback(() => {
    form.validateFields((err, fieldsValue) => {
      if (!err) {
        props?.layer?.emitOk(fieldsValue);
      }
    });
  }, []);

  return (
    <div className={styles['add_bill_modal']}>
      <div className="modal_header">
        <div className="flex title">
          <T name="重新同步账单" />
        </div>
        <Icon className="cross_icon" type="cross" onClick={handleClose} />
      </div>
      <div className="modal_content">
        <Form>
          <FormItem label={'请选择部门'}>
            {getFieldDecorator('departmentId', {
              initialValue: [],
              rules: [{ required: true, message: i18n.get('请选择部门') }],
            })(
              <Selects
                options={depParams?.expenseDepartment ? getDatasource?.() ?? [] : []}
                mode="multiple"
                labelKey="name"
                valueKey="id"
              ></Selects>,
            )}
          </FormItem>
          <FormItem label={'行程类型'}>
            {getFieldDecorator('categoryIds', {
              initialValue: [],
            })(
              <Selects
                options={value?.supplierAccountId?.categoryIds ?? []}
                labelKey="name"
                mode="multiple"
                valueKey="id"
              ></Selects>,
            )}
          </FormItem>
        </Form>
      </div>
      <div className="modal-footer">
        <Button className="btn-ml mr-8" onClick={handleClose}>
          {i18n.get('取消')}
        </Button>
        <Button type="primary" className="btn-ml mr-8" onClick={handleSubmit}>
          {i18n.get('确定')}
        </Button>
      </div>
    </div>
  );
};

export default Form.create()(ReSyncSettlement);
