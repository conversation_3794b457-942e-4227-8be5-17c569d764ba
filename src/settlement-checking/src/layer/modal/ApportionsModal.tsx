import React, { PureComponent } from 'react';
import { Icon, Table } from 'antd';
import { T } from '@ekuaibao/i18n';
import styles from './Modal.module.less';

interface Props {
  dataSource: any[];
  columns: any[];
  layer?: any;
}

export default class ApportionsModal extends PureComponent<Props> {
  handleClose = () => {
    this.props?.layer?.emitCancel();
  };

  render() {
    const { dataSource, columns } = this.props;
    const columnsData = columns.slice()
    columnsData.forEach((el, index) => {
      if (index !== columnsData.length - 1) {
        el.width = 200
      }
    })
    const scrollX = columnsData.length * 200
    return (
      <div className={styles['add_bill_modal']}>
        <div className="modal_header">
          <div className="flex title">
            <T name="分摊明细" />
          </div>
          <Icon className="cross_icon" type="cross" onClick={this.handleClose} />
        </div>
        <div className="modal_content">
          <Table
            bordered={true}
            columns={columnsData}
            dataSource={dataSource}
            pagination={false}
            scroll={{x: scrollX , y: 500}}
          />
        </div>
      </div>
    );
  }
}
