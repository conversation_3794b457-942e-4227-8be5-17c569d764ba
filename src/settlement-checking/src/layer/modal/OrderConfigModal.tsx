/*
 * @Author: <PERSON>
 * @Date: 2022-10-08 16:50:24
 * @LastEditTime: 2022-10-19 10:06:38
 * @LastEditors: Hunter
 * @Description:
 * @FilePath: \plugin-web-settlement-checking\src\layer\modal\OrderConfigModal.tsx
 * 可以输入预定的版权声明、个性签名、空行等
 */
import MessageCenter from '@ekuaibao/messagecenter';
import { app } from '@ekuaibao/whispered';
import React, { useCallback } from 'react';
import { Button, Icon, Form } from 'antd';
import { T } from '@ekuaibao/i18n';
import styles from './Modal.module.less';
import { showMessage } from '@ekuaibao/show-util';
import { Dynamic } from '@ekuaibao/template';
import { addOrderMatchConfig } from '../../setttlement-checkin-action';
const { editable } = app.require('@components/index.editable');
import {
  orderConfigSupplierType,
  orderConfigName,
  orderConfigDescription,
  orderConfigSelect,
  orderConfigRules,
} from '../../supplier-dimension/utils/form';
interface IPorps {
  form: any;
  layer?: any;
  data?: any;
  [key: string]: any;
}
const form = (T: any) => {
  return Form.create({
    onValuesChange(props: any, values) {
      if (!values) return;
      props.onValuesChange(values);
    },
  })(T);
};
const OrderConfigModal: React.FC<IPorps> = (props: IPorps) => {
  const bus = props.bus ?? new MessageCenter();
  const { data } = props;
  const handleClose = useCallback(() => {
    props?.layer?.emitCancel();
  }, []);
  const handleSubmit = useCallback(async () => {
    try {
      await bus.getValueWithValidate(0);
      const params = await bus.getFieldsValue();
      if (!props?.data) await addOrderMatchConfig(params);
      props?.layer?.emitOk({ ...params, id: data?.id });
    } catch (error: any) {
      if (error.errorMessage ?? error.message) {
        showMessage.error(error.errorMessage ?? error.message);
      }
    }
  }, []);
  const valueChange = useCallback((values) => {
    if (Reflect.has(values, 'supplierArchiveType')) {
      bus.setFieldsValue({ mappings: [{}] });
      bus.emit('supplierArchiveType', values?.supplierArchiveType);
    }
  }, []);
  return (
    <div className={styles['add_bill_modal']}>
      <div className="modal_header">
        <div className="flex title">
          <T name="订单匹配配置" />
        </div>
        <Icon className="cross_icon" type="cross" onClick={handleClose} />
      </div>
      <div className="modal_content">
        <Dynamic
          className="template"
          value={props?.data}
          elements={editable}
          template={[
            orderConfigName(true),
            orderConfigDescription(true),
            orderConfigSupplierType(true, ['XC_TRIP', 'GEELY_TRIP', 'TRAVEL_ONE']),
            orderConfigSelect(true),
            orderConfigRules(true),
          ]}
          onValuesChange={valueChange}
          bus={bus}
          create={form}
          sourceData={props?.data}
        />
      </div>
      <div className="modal-footer">
        <Button className="btn-ml mr-8" onClick={handleClose}>
          {i18n.get('取消')}
        </Button>
        <Button type="primary" className="btn-ml mr-8" onClick={handleSubmit}>
          {i18n.get('确定')}
        </Button>
      </div>
    </div>
  );
};
export default OrderConfigModal;
