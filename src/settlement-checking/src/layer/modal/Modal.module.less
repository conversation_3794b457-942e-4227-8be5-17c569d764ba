@import '~@ekuaibao/web-theme-variables/styles/default.less';
@import '~@ekuaibao/web-theme-variables/styles/colors.less';
@import '~@ekuaibao/eui-styles/less/token.less';
.action-btn {
  color: var(--brand-base);
}
.add_bill_modal {
  display: flex;
  flex-direction: column;

  :global {
    .modal_header {
      height: 50px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;

      .title {
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        max-width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .cross_icon {
        font-size: 20px;
        cursor: pointer;
      }
    }

    .modal_content {
      padding: 24px;
      height: 466px;
      overflow: auto;
      .form_item {
        margin-bottom: @space-5;
      }
      .upload_view {
        height: 200px;
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        opacity: 0.76;
        font-size: 14px;
        color: #142234;
        .link-button {
          color: #22b2cc;
          margin-left: 20px;
        }
      }
      .sipp {
        height: 1px;
        opacity: 1;
        background: #e6e6e6;
        width: 100%;
        margin-bottom: @space-5;
      }
      .invoice-wrapper {
        height: 240px;
        background: rgba(39, 46, 59, 0.02);
        border: 1px dashed rgba(39, 46, 59, 0.06);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        .ekb-files-input {
          width: 100% !important;
        }
        .afresh-select {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
        }
        .excel-name {
          margin: 20px 0 24px;
        }
        .upload-wrapper {
          .select-file {
            color: rgba(39, 46, 59, 0.88);
            margin: 18px 0 4px;
            font-size: 14px;
          }
          .astrict-file {
            margin-bottom: 18px;
            font-size: 12px;
            color: #86909c;
          }
        }
      }
    }

    .modal-footer {
      height: 64px;
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding: 0 24px;
      .ant-btn {
        border-radius: 4px !important;
      }
    }

    .modal_footer {
      height: 64px;
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding: 0 24px;
      .ant-btn {
        border-radius: 4px !important;
      }
    }
  }
}
