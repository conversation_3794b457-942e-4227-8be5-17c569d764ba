import React, { useCallback } from 'react';
import { Button, Icon, Alert } from 'antd';
import { T } from '@ekuaibao/i18n';
import styles from './Modal.module.less';

interface IPorps {
  form: any;
  layer?: any;
  data?: any;
  [key: string]: any;
}

const OrderShowTaskModal: React.FC<IPorps> = (props: IPorps) => {
  const handleClose = useCallback(() => {
    props?.layer?.emitOk();
  }, []);
  return (
    <div className={styles['add_bill_modal']}>
      <div className="modal_header">
        <div className="flex title">
          <T name="执行匹配任务" />
        </div>
        <Icon className="cross_icon" type="cross" onClick={handleClose} />
      </div>
      <div className="modal_content">
        <Alert
          style={{ fontSize: '14px' }}
          message={i18n.get('当前匹配任务信息过多。建议点击确认后关闭此框，系统会在后台处理此操作。')}
          type="info"
          showIcon
        />
        <div style={{ marginTop: '30px', fontSize: '14px' }}>
          {i18n.get(
            '系统正在为您自动匹配订单，请稍作等候。您可关闭此窗口，稍后在[供应商]，找到对应账单，点击已匹配条数，查看匹配结果。',
          )}
        </div>
      </div>
      <div className="modal_footer">
        <Button type="primary" className="btn-ml mr-8" onClick={handleClose}>
          {i18n.get('确定')}
        </Button>
      </div>
    </div>
  );
};
export default OrderShowTaskModal;
