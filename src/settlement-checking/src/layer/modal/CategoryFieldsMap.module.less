.config-rules-wrapper {
  max-height: 500px;
  display: flex;
  flex-direction: column;
  :global {
    .config-rules-header {
      padding: 24px;
      display: flex;
      align-items: center;
      height: 64px;
      flex-shrink: 0;
      font-size: 20px;
      font-weight: 500;
      color: #1d2b3d;
      border-bottom: none;
    }
    .config-rules-content {
      width: 100%;
      min-height: 300px;
      padding: 16px 24px;
      flex: 1;
      overflow: auto;
    }
    .config-rules-footer {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 56px;
      padding: 0 16px;
      justify-content: flex-end;
      flex-shrink: 0;
      .config-btn {
        margin-right: 8px;
        border: none;
        font-size: 14px;
        color: #262626;
        width: 70px;
        height: 32px;
      }
      .btn-ml {
        font-size: 14px;
        color: #ffffff;
        width: 70px;
        height: 32px;
      }
    }
  }
}
