/*
 * @Author: <PERSON>
 * @Date: 2022-01-07 11:06:53
 * @LastEditTime: 2022-05-15 10:18:19
 * @LastEditors: Hunter
 * @Description:
 * @FilePath: \plugin-web-settlement-checking\src\layer\modal\ManuallySyncBillModal.tsx
 * 可以输入预定的版权声明、个性签名、空行等
 */

import React, { Component } from 'react';
import { app } from '@ekuaibao/whispered';
import { Icon, Button, Form, Select, Radio } from 'antd';
import { T } from '@ekuaibao/i18n';
import moment from 'moment';
import { showMessage } from '@ekuaibao/show-util';
import { get } from 'lodash';
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
import { QuerySelect } from 'ekbc-query-builder';
import {
  getSupplierArchiveList,
  getSupplierAccountList,
  getBillingPeriodList,
  manuallySyncBill
} from './../../setttlement-checkin-action';
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
import styles from './Modal.module.less';
const { Option } = Select;
const FormItem = Form.Item;
interface State {
  [key: string]: any;
}
interface Props {
  [key: string]: any;
}
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 14 },
};
@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer',
  width: 600,
})
@EnhanceFormCreate()
export default class ManuallySyncBillModal extends Component<State, Props> {
  constructor(props: Props) {
    super(props);
    this.state = {
      archiveList: [],
      billPeriodInfo: [],
    };
  }
  componentDidMount() {
    this.getSupplierArchiveList();
    this.getBillingPeriodInfo();
  }
  getSupplierArchiveList = async () => {
    try {
      const res = await getSupplierArchiveList({
        orderBy: [{ value: 'name', order: 'DESC' }],
        filterBy: '(active==true)',
      });
      let archiveList = get(res, 'items', []);
      const types = ['XC_TRIP', 'GEELY_TRIP', 'TC_TRIP', 'TRAVEL_ONE', 'HOSE_TRIP']
      archiveList = archiveList.filter((val: any) => (types.includes(val?.supplierArchiveType)))
      this.setState({ archiveList });
    } catch (error) {
      showMessage.error(error?.message || error?.errMessage);
    }
  };
  getSupplierAccountList = async (supplierId: string) => {
    try {
      const query = new QuerySelect();
      query.select('id,name,active,importMethod');
      query.filterBy('active==true');
      query.filterBy(`supplierArchiveId=="${supplierId}"`);
      const res = await getSupplierAccountList(query.value());
      this.setState({ accountList: get(res, 'items', []) });
    } catch (error) {
      showMessage.error(error?.message || error?.errMessage);
    }
  };
  handleArchiveChange = (supplierId: string) => {
    const {
      form: { setFieldsValue },
    } = this.props;
    setFieldsValue({ supplierAccountId: '', platformType: '' });
    this.getSupplierAccountList(supplierId);
  };
  getBillingPeriodInfo = () => {
    getBillingPeriodList({})
      .then((res) => {
        this.setState({ billPeriodInfo: get(res, 'items', []) || [] });
      })
      .catch((err) => {
        showMessage.error(err?.errorMessage || err?.message);
      });
  };
  handleModalClose() {
    this.props.layer.emitCancel();
  }
  handleConfirm() {
    const { form } = this.props;
    form?.validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }
      const res = { ...this.formatDay(values.dataRange, values) }
      if (values.platformType === 'HOSE_TRIP') {
        manuallySyncBill(res).then(res => {
          this.props?.layer?.emitOk({ key: 'isHose', value: res?.value });
        }).catch(error => {
          const errorText = error?.errorMessage || error?.message
          this.setState({
            errorText
          })
        })
      } else {
        this.props?.layer?.emitOk({ ...this.formatDay(values.dataRange, values) });
      }

    });
  }
  formatDay(date: any, target: any) {
    if (date) {
      target.startDate = moment(this.formatTime(date[0], '0', '0', '0', '0')).valueOf();
      target.endDate = moment(this.formatTime(date[1], '23', '59', '59', '999')).valueOf();
      delete target.dataRange;
      return target;
    } else {
      return target;
    }
  }
  formatTime(date: any, h: string, m: string, s: string, ms: string) {
    date.hour(h);
    date.minute(m);
    date.second(s);
    date.millisecond(ms);
    return date;
  }
  render() {
    const { state, props } = this;
    const { archiveList = [], accountList = [], billPeriodInfo = [], errorText } = state;
    const {
      form: { getFieldDecorator, getFieldValue },
      apiSyncCtrip,
      apiSyncGeely,
      apiSyncTC,
      apiSyncTO,
      syncHoseTrip
    } = props;
    const supplierArchiveType = getFieldValue('supplierArchiveId');
    const supplierArchiveTypeName =
      archiveList.find((i: any) => i?.id === supplierArchiveType)?.supplierArchiveType ?? '';
    const supplierAccountId = getFieldValue('supplierAccountId');
    const importMethod = accountList.find(v => v.id === supplierAccountId)?.importMethod
    let formItemProps: any = {}
    if (errorText) {
      formItemProps.validateStatus = 'error'
      formItemProps.help = errorText
    }
    return (
      <div className={styles['add_bill_modal']}>
        <div className="modal-header">
          <div className="flex">{i18n.get('手动同步账单')}</div>
          <Icon className="close-icon" type="close" onClick={this.handleModalClose.bind(this)} />
        </div>
        <div className="modal_content">
          <div className="content-wrapper mg-24 pd-16">
            <Form>
              <FormItem {...formItemLayout} className={'form_item'} label="供应商">
                {getFieldDecorator('supplierArchiveId', {
                  rules: [{ required: true, message: i18n.get('请选择供应商') }],
                })(
                  <Select
                    allowClear
                    placeholder={i18n.get('请选择供应商')}
                    onChange={(value) => this.handleArchiveChange(value as string)}
                  >
                    {archiveList?.map((it) => {
                      return (
                        <Option key={it.id} value={it.id}>
                          {it.name}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="账户">
                {getFieldDecorator('supplierAccountId', {
                  rules: [{ required: true, message: i18n.get('请选择供应商账户') }],
                })(
                  <Select allowClear placeholder={i18n.get('请选择供应商账户')}>
                    {accountList?.map((it) => {
                      return (
                        <Option key={it.id} value={it.id}>
                          {it.name}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </FormItem>
              <FormItem {...formItemLayout} className={'form_item'} label="同步平台">
                {getFieldDecorator('platformType', {
                  // initialValue: 'CTRIP',
                  rules: [{ required: true, message: i18n.get('请选择同步平台') }],
                })(
                  <Radio.Group>
                    <Radio value={'HOSE_TRIP'} disabled={!syncHoseTrip || supplierArchiveTypeName !== 'HOSE_TRIP' || importMethod !== 'api'}>{i18n.get('合思商城')}</Radio>
                    <Radio value={'CTRIP'} disabled={!apiSyncCtrip || supplierArchiveTypeName != 'XC_TRIP'}>{i18n.get('携程商旅')}</Radio>
                    <Radio value={'GEELY_TRIP'} disabled={!apiSyncGeely || supplierArchiveTypeName != 'GEELY_TRIP'}>{i18n.get('吉利商旅')}</Radio>
                    <Radio value={'TC_TRIP'} disabled={!apiSyncTC || supplierArchiveTypeName != 'TC_TRIP'}>{i18n.get('同程商旅')}</Radio>
                    <Radio value={'TRAVEL_ONE'} disabled={!apiSyncTO || supplierArchiveTypeName != 'TRAVEL_ONE'}>{i18n.get('差旅壹号')}</Radio>
                  </Radio.Group>
                )}
              </FormItem>
              <FormItem {...formItemLayout} label="账单期间" {...formItemProps}>
                {getFieldDecorator('billPeriod', {
                  rules: [{ required: true, message: i18n.get('请选择账单期间') }],
                })(
                  <Select allowClear placeholder={i18n.get('请选择账单期间')}>
                    {billPeriodInfo?.map((it) => {
                      return (
                        <Option key={it.id} value={it.id}>
                          {it.name}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </FormItem>
            </Form>
          </div>
        </div>
        <div className="modal-footer">
          <Button className="mr-8" onClick={this.handleModalClose.bind(this)}>
            <T name={'取消'} />
          </Button>
          <Button className="mr-8" type={'primary'} onClick={this.handleConfirm.bind(this)}>
            <T name={'立即同步'} />
          </Button>
        </div>
      </div>
    );
  }
}
