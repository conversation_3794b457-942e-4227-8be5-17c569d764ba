import MessageCenter from '@ekuaibao/messagecenter';
import React, { useCallback } from 'react';
import { Button, Icon, Form } from 'antd';
import { T } from '@ekuaibao/i18n';
import styles from './Modal.module.less';
import DynamicWrapper from '../../components/DynamicWrapper';
import { showMessage } from '@ekuaibao/show-util';
import { orderSelectTime } from '../../supplier-dimension/utils/form';
import OrderSelectTime from '../../components/OrderSelectTime';
interface IPorps {
  layer?: any;
  [key: string]: any;
}
const form = (T: any) => {
  return Form.create({
    onValuesChange(props: any, values) {
      if (!values) return;
      props.onValuesChange(values);
    },
  })(T);
};
const OrderSelectModal: React.FC<IPorps> = (props: IPorps) => {
  const bus = props.bus ?? new MessageCenter();
  const handleClose = useCallback(() => {
    props?.layer?.emitCancel();
  }, []);
  const handleSubmit = useCallback(async () => {
    try {
      await bus.getValueWithValidate(0);
      const params = await bus.getFieldsValue();
      props?.layer?.emitOk(params);
    } catch (error: any) {
      error.errorMessage && showMessage.error(error.errorMessage);
    }
  }, []);
  const valueChange = useCallback((value) => {}, []);
  return (
    <div className={styles['add_bill_modal']}>
      <div className="modal_header">
        <div className="flex title">
          <T name="选择订单" />
        </div>
        <Icon className="cross_icon" type="cross" onClick={handleClose} />
      </div>
      <div className="modal_content">
        <T name="将根据您选择的时间范围，为您过滤出行程日期在此范围内的订单" />
        <DynamicWrapper
          className="template"
          value={props?.data}
          elements={[OrderSelectTime] as any}
          template={[orderSelectTime(true)]}
          onValuesChange={valueChange}
          bus={bus}
          create={form}
          orderData={props?.data}
        />
      </div>
      <div className="modal-footer">
        <Button className="btn-ml mr-8" onClick={handleClose}>
          {i18n.get('取消')}
        </Button>
        <Button type="primary" className="btn-ml mr-8" onClick={handleSubmit}>
          {i18n.get('确定')}
        </Button>
      </div>
    </div>
  );
};
export default OrderSelectModal;
