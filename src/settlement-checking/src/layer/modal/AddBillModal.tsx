/*
 * @Description: 新增供应商账单
 * @Creator: chencan<PERSON>han
 * @Date: 2021-06-07 14:04:09
 */
import React, {
  PureComponent,
  useState,
  createRef,
  forwardRef,
  useImperativeHandle,
  useEffect
} from 'react';
import { app } from '@ekuaibao/whispered';
import { QuerySelect } from 'ekbc-query-builder';
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
import { List, Form, Select, Button, Icon, Tooltip } from 'antd';
import { T } from '@ekuaibao/i18n';
const FormItem = Form.Item;
const { Option } = Select;
import { get } from 'lodash';
import { showMessage } from '@ekuaibao/show-util';
import { getSupplierAccountList, getBillingPeriodList } from './../../setttlement-checkin-action';
import { EnhanceConnect } from '@ekuaibao/store';
import { FilesUploader, fixData, generateFileName } from '@ekuaibao/uploader'
import { Fetch } from '@ekuaibao/fetch'
import styles from './Modal.module.less';
import excel from '../../images/excel.svg';
import uploadIcon from '../../images/upload-icon.svg';
import close_svg from "../../images/close-icon.svg"
import SVGIcon from "../../components/SVGIcon";
interface Props {
  [key: string]: any;
}
@EnhanceConnect((state: any) => ({
  KA_ZY_Reconciliation_Settlement: state['@common'].powers.KA_ZY_Reconciliation_Settlement,
  uploadServiceUrl: state['@common'].uploadServiceUrl
}))
@EnhanceFormCreate()
export default class AddBillModal extends PureComponent<Props> {
  constructor(props: {} | Readonly<{}>) {
    super(props);
    this.state = {
      accountList: [],
      billPeriod: [],
      supplierArchiveId: null,
    };
  }
  childRef: any = createRef()
  handleClose = () => {
    this.props?.layer?.emitCancel();
  };
  private save = () => {
    const { current } = this.childRef
    const { form, isDetail } = this.props;
    if(isDetail) {
      if(!current?.excelName) {
        showMessage.warning(i18n.get('请上传Excel文件'));
        return
      }
      return this.props?.layer?.emitOk({ supplierArchiveId: this.state?.supplierArchiveId, ...current });
    }
    form?.validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }
      if(!current?.excelName) {
        showMessage.warning(i18n.get('请上传Excel文件'));
        return
      }
      this.props?.layer?.emitOk({ ...values, supplierArchiveId: this.state?.supplierArchiveId, ...current });
    });
  };
  componentDidMount() {
    this.getSupplierAccountList();
    this.getBillingPeriod();
  }
  async getSupplierAccountList() {
    const query = new QuerySelect();
    query.select('id,name,active,importMethod');
    query.filterBy('active==true')
      .filterBy('importMethod=="excel"||importMethod=="directly"')
    getSupplierAccountList(query.value())
      .then((res) => {
        this.setState({ accountList: get(res, 'items', []) });
      })
      .catch((err) => {
        showMessage.error(err?.message || err?.errMessage);
      });
  }
  async getBillingPeriod() {
    const query = new QuerySelect();
    query.filterBy('active==true');
    getBillingPeriodList(query.value())
      .then((res) => {
        this.setState({ billPeriod: get(res, 'items', []) || [] });
      })
      .catch((err) => {
        showMessage.error(err?.message || err?.errMessage);
      });
  }

  render() {
    const {
      form: { getFieldDecorator },
      KA_ZY_Reconciliation_Settlement,
    } = this.props;
    const { accountList = [], billPeriod = [] } = this.state;

    return (
      <div className={styles['add_bill_modal']}>
        <div className="modal_header">
          <div className="flex title">
            <T name="导入账单" />
            {KA_ZY_Reconciliation_Settlement && (
              <Tooltip
                arrowPointAtCenter
                placement="topRight"
                title={i18n.get('请在【部门/科室对账】中查看部门/科室对账详情')}
              >
                <Icon type="question-circle" style={{ fontSize: '14px', color: '#999', marginLeft: 8 }} />
              </Tooltip>
            )}
          </div>
          <img className="cross_icon" src={close_svg} onClick={this.handleClose}/>
        </div>
        <div className="modal_content">
          {
            !this.props?.isDetail &&
            <List>
              <Form layout="vertical">
                <FormItem className={'form_item'} label="供应商账户">
                  {getFieldDecorator('supplierAccountId', {
                    rules: [{ required: true, message: i18n.get('请选择供应商账户') }],
                  })(
                    <Select
                      allowClear
                      onChange={(id) => {
                        for (let i = 0; i < accountList?.length; i++) {
                          const it = accountList[i];
                          if (it?.id === id) {
                            this.setState({ supplierArchiveId: it?.supplierArchiveId });
                            break;
                          }
                        }
                      }}
                      placeholder={i18n.get('请选择供应商账户')}
                    >
                      {accountList.map((it) => {
                        return (
                          <Option key={it?.id} value={it?.id}>
                            {it?.name}
                          </Option>
                        );
                      })}
                    </Select>,
                  )}
                </FormItem>
                <FormItem className={'form_item'} label="账单期间">
                  {getFieldDecorator('billPeriod', {
                    rules: [{ required: true, message: i18n.get('请选择账单期间') }],
                  })(
                    <Select allowClear placeholder={i18n.get('请选择账单期间')}>
                      {billPeriod.map((it) => {
                        return (
                          <Option key={it?.id} value={it?.id}>
                            {it?.name}
                          </Option>
                        );
                      })}
                    </Select>,
                  )}
                </FormItem>
              </Form>
            </List>
          }
          <div className="invoice-wrapper">
            <UploadExcel ref={this.childRef} {...this.props} />
          </div>
        </div>
        <div className="modal-footer">
          <Button
            className="btn-ml"
            onClick={() => {
              this.props?.layer?.emitCancel();
            }}
          >
            {i18n.get('取消')}
          </Button>
          <Button type="primary" className="btn-ml" onClick={this.save}>
            {i18n.get('开始导入')}
          </Button>
        </div>
      </div>
    );
  }
}

type IProps = {
  uploadServiceUrl: {
    uploadUrl: string
  }
}

const UploadExcel: React.FC<IProps> = forwardRef((props, ref) => {
  useImperativeHandle(ref, () => ({
    excelName,
    file,
    token
  }))

  const [excelName, setExcelname] = useState<string>('')
  const [token, setToken] = useState<any>()
  const [file, setFile] = useState({})
  // @ts-ignore
  const IS_STANDALONE = window?.IS_STANDALONE
  const { uploadServiceUrl } = props
  const uploadUrl = uploadServiceUrl && uploadServiceUrl?.uploadUrl

  useEffect(() => {
    initializeToken()
  }, [])

  const initializeToken = async () => {
    const token = await getToken()
    setToken(token)
  }
  const tokenErrorAction = () => {
    initializeToken()
    return showMessage.warning(i18n.get('获取上传验证码失败,请稍候重试'))
  }
  const getUploadUrl = (file: any, generateFileName: string) => {
    return Fetch.GET(`/api/v1/attachment/attachments/presign/$${generateFileName}`)
  }
  const handleChange = (info: any) => {
    console.log(info, 'infoinfo')
  }
  const handleOnDone = (uploaderFileList: any) => {
    let keys: any = []
    let fileNames: any = {}
    uploaderFileList.forEach((file: any) => {
      if (file.status === 'error') {
        const {  name = '' } = file
        showMessage.error(i18n.get(`「{__k0}」{__k1}!`, { __k0: name, __k1: i18n.get('上传失败') }))
      } else {
        let { response, name } = file
        let key = response.key
        keys.push({ key })
        fileNames[key] = response['x:originalname']
        setExcelname(name)
      }
    })

    getFileId(keys).then(result => {
      const { items } = result
      if (items && items.length) {
        // @ts-ignore
        let file = {}
        items.forEach((item: any) => {
          const { id, url, key, thumbUrl } = item
          const f = {
            fileId: {
              id,
              thumbUrl,
              url
            },
            key,
            fileName: fileNames[key]
          }
          file = f
        })
        setFile(file)
      }
    })
  }
  const fnFixData = (file: any) => {
    // @ts-ignore
    const fileServiceType = uploadServiceUrl && uploadServiceUrl?.fileServiceType
    return fixData(file, fileServiceType, token, tokenErrorAction)
  }
  const buildData = (file: any, tokenData: any) => {
    let { name } = file
    return { key: generateFileName(name), 'x:originalname': name, token: tokenData && tokenData.token }
  }
  const getToken = () =>{
    return Fetch.GET('/api/v1/attachment/attachments/token').then(result => {
      const { value } = result
      return value
    })
  }
  const getFileId = (keys: any) => {
    return Fetch.POST(
      '/api/v1/attachment/attachments',
      {},
      {
        body: {
          keys: keys
        }
      }
    )
  }
  const params = {
    name: 'file',
    action: IS_STANDALONE ? getUploadUrl : uploadUrl,
    type: IS_STANDALONE,
    onChange: handleChange,
    onDone: handleOnDone,
    multiple: false,
    data: IS_STANDALONE ? (file: any, tokenData: any) => buildData(file, tokenData) : fnFixData,
    accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    responseType: 'arraybuffer',
    maxSize: 10
  }
  const EFilesUploader: any = FilesUploader
  return (
    <>
      {
        excelName ?
          <EFilesUploader {...params}>
            <div className="afresh-select">
              <SVGIcon src={excel} />
              <div className="excel-name">{excelName}</div>
              <Button>{i18n.get('重新选择')}</Button>
            </div>
          </EFilesUploader>
           :
          <EFilesUploader {...params}>
            <div className="afresh-select upload-wrapper">
              <SVGIcon src={uploadIcon} />
              <div className="select-file">{i18n.get('选择或拖拽 Excel 文件到此处')}</div>
              <div className="astrict-file">{i18n.get('仅支持xls、xlsx文件格式，文件大小不超过10M')}</div>
              <Button>{i18n.get('选择文件')}</Button>
            </div>
          </EFilesUploader>
      }
    </>
  )
})
