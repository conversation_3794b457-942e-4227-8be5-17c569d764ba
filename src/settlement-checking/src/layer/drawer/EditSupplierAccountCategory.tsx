/**
 *  Created by pw on 2021/7/22 上午11:25.
 */
import React, { useEffect, useState } from 'react';
import './EditSupplierAccountCategory.less';
import CategoryComponent from '../../components/CategoryComponent';
import DynamicWrapper from '../../components/DynamicWrapper';
import { Bus } from '@ekuaibao/template';
import DatalinkFields from '../../components/DatalinkFields';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
import { DatalinkIF, ICategoryId, ISupplierAccountField } from '@ekuaibao/ekuaibao_types';
import { Button } from 'antd';
import { T } from '@ekuaibao/i18n';
import { app } from '@ekuaibao/whispered';
import { listSystemBillTemplates, removeBillTemplate, saveUserBillTemplate } from '../../setttlement-checkin-action';
import { showMessage, showModal } from '@ekuaibao/show-util';
import { DeleteButton, formatCategoryName, template } from './SupplierAccountCategoryHelper';
let __DEFAULTBILLTEMPLATES: ICategoryId[] = [];

interface Props extends ILayerProps {
  supplierAccountId: string;
  entityInfo?: ICategoryId;
  allEntityList: DatalinkIF[];
  accountName: string;
  account: any;
}

const EditSupplierAccountCategory: React.FC<Props> = (props) => {
  const { account, entityInfo, layer, allEntityList, supplierAccountId, accountName } = props;
  const [currentEntityInfo, setCurrentInfo] = useState<ICategoryId>({} as ICategoryId);
  const [categories, setCategory] = useState([]);
  const [bus] = useState(Bus());

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    if (!entityInfo) {
      const { items } = __DEFAULTBILLTEMPLATES?.length
        ? { items: __DEFAULTBILLTEMPLATES }
        : await listSystemBillTemplates();
      __DEFAULTBILLTEMPLATES = items;
      setCategory(items);
    }
  };

  const handleQuickCreateField = (entityInfo: ICategoryId) => {
    const fnUpdataData = () => {
      bus.setFieldsValue({ name: `${entityInfo.name}`, datalinkFields: entityInfo.fields });
      setCurrentInfo(entityInfo);
    };

    if (currentEntityInfo?.id === undefined) {
      fnUpdataData();
      return;
    }
    if (currentEntityInfo?.id !== entityInfo?.id) {
      showModal.confirm({
        title: i18n.get(`确认使用「${entityInfo?.name}」模板吗？`),
        onOk: () => {
          fnUpdataData();
        },
      });
    }
  };

  const handleSave = async () => {
    const formValue: any = await bus.getValueWithValidate(0);
    delete formValue.datalinkFields;
    const [newFields]: any[] = await bus.invoke('field:get:result');
    const removedFields: ISupplierAccountField[] =
      entityInfo?.fields.filter((item) => {
        return newFields.filter((newItem: ISupplierAccountField) => newItem.name === item.name).length === 0;
      }) || [];
    if (removedFields?.length > 0) {
      await app.open('@third-party-manage:FieldConfigEditConfirmRemoveModal', {
        allEntityList: allEntityList || [],
        removedFields,
        type: 0,
      });
    }
    const entityData = entityInfo?.id ? currentEntityInfo : {};
    try {
      await saveUserBillTemplate({
        ...entityData,
        ...formValue,
        fields: newFields,
        id: entityInfo?.id,
        supplierAccountId,
        name: entityInfo?.id ? entityInfo?.name : formValue?.name,
      }).then((res) => {
        account?.importMethod !== 'api' && showMessage.success(i18n.get('保存成功'));
        layer.emitOk({action: 'save', value: res.value});
      });
    } catch (err) {
      showMessage.error(err.msg);
    }
  };

  const handleCancel = () => {
    layer.emitCancel();
  };

  const handleDel = () => {
    showModal.confirm({
      title: i18n.get('确认删除？'),
      content: i18n.get(`确认删除${formatCategoryName(accountName, entityInfo!.name)}`),
      onOk: () => {
        removeBillTemplate({ supplierAccountId, dataLinkEntityId: entityInfo?.id })
          .then((res) => {
            showMessage.success(i18n.get('删除成功'));
            layer.emitOk({});
          })
          .catch((err) => {
            showMessage.error(err.msg);
          });
      },
    });
  };

  const Btn: any = Button;
  return (
    <div className="edit-supplier-account-category-wrapper">
      {!entityInfo && (
        <div className="edit-supplier-account-default-category">
          <CategoryComponent
            className={'ovr-x-h'}
            title={'快捷模板'}
            categories={categories}
            currentEntityInfo={currentEntityInfo}
            onClick={handleQuickCreateField}
          />
        </div>
      )}
      <div className="supplier-account-category-content">
        <div className="supplier-account-datalink-wrpper">
          <DynamicWrapper
            template={template({ editable: !entityInfo?.id })}
            value={
              entityInfo
                ? {
                    ...entityInfo,
                    name: formatCategoryName(accountName, entityInfo?.name),
                    datalinkFields: entityInfo?.fields,
                  }
                : undefined
            }
            bus={bus}
            elements={[DatalinkFields as any]}
            isEdit={!!entityInfo?.id}
            tags={{
              datalinkFields: { fields: entityInfo ? entityInfo?.fields : currentEntityInfo?.fields, allEntityList },
            }}
          />
        </div>
        <div className="edit-supplier-account-category-footer">
          <div className="account-category-footer-left">
            <Btn size={'large'} type={'primary'} onClick={handleSave}>
              <T name={account?.importMethod === 'api' ? '下一步' : '保存'} />
            </Btn>
            <Button className="ml-8" onClick={handleCancel}>
              <T name={'取消'} />
            </Button>
          </div>
          {entityInfo && <DeleteButton button={{ label: i18n.get('删除此品类'), onClick: handleDel }} />}
        </div>
      </div>
    </div>
  );
};

export default EditSupplierAccountCategory;
