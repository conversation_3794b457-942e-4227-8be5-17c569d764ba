import React, { useEffect, useState } from 'react';
import './AdjustingBillsDrawer.less';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
import { Button } from '@hose/eui';
import { T } from '@ekuaibao/i18n';
import { app } from '@ekuaibao/whispered';
import { showMessage } from '@ekuaibao/show-util';
const EditTableWrapper = app.require<any>('@components/dynamic/EditTableWrapper');
import { adjustmentBill } from '../../setttlement-checkin-action';

interface IProps extends ILayerProps {
  columns: any;
  dataSource: any;
  checkingBillId?: string;
  entityId?: string;
  components?: any[];
  bus: any
}

const AdjustingBillsDrawer: React.FC<IProps> = (props) => {
  const [dataSource, setDataSource] = useState([]);
  const [checkingDetails, setCheckingDetails] = useState<any[]>([]);
  useEffect(() => {
    setDataSource(props.dataSource)
  }, []);

  const handleClose = () => {
    props.layer.emitCancel();
  };


  const filterDataScoure = (changeData: any) => {
    const origData = props.dataSource
    let cList = checkingDetails.slice(0)
    for (let i = 0; i < origData.length; i++) {
      const dataOri = origData[i]
      const dataChange = changeData[i]
      const dataOriKeys = Object.keys(dataOri)
      const dataChangeKeys = Object.keys(dataChange)
      const tempData = dataOriKeys?.length >= dataChangeKeys?.length ? dataOri : dataChange
      let itemObj: any = {}
      for (let m in tempData) {
        let currentV: any = dataOri[m]
        let changeV: any = dataChange[m]
        if (Array.isArray(changeV)) {
          changeV = changeV?.filter(v => v)?.map(v => ({ id: v?.id, name: v?.name }))
          currentV = currentV?.filter(v => v)?.map(v => ({ id: v?.id, name: v?.name }))
          changeV = changeV?.length ? changeV : []
          currentV = currentV?.length ? currentV : []
        } else if (changeV?.id) {
          changeV = { id: changeV?.id, name: changeV?.name }
          currentV = { id: currentV?.id, name: currentV?.name }
        }
        if (JSON.stringify(changeV) !== JSON.stringify(currentV)) {
          itemObj['id'] = dataChange['id']
          if (m === 'E_system_checking_accountPeriod') {
            itemObj['belongPeriod'] = dataChange['E_system_checking_accountPeriod']?.value
          } else {
            itemObj[m] = changeV ?? null
          }
        }
      }

      const idx = cList.findIndex(item => item.id === itemObj['id'])
      if (idx > -1) {
        cList.splice(idx, 1, itemObj)
      } else {
        itemObj['id'] && cList.push(itemObj)
      }
    }
    setCheckingDetails(cList)
    setDataSource(changeData)
  }

  const onGetData = (data: any) => {
    filterDataScoure(data)
  };

  const handleAdjustment = async () => {
    if (checkingDetails.length) {
      try {
        const res = await adjustmentBill({
          checkingBillId: props?.checkingBillId,
          entityId: props?.entityId,
          checkingDetails: checkingDetails,
        });
        if (res?.value?.success) {
          props.isApi ? showMessage.success(i18n.get('已发起调整, 请稍后查看'), 5) : showMessage.success(i18n.get('调整成功'), 5)
          let { bus } = props;
          bus?.invoke('refresh:data', { ex: false, isChangeScene: true })
          setCheckingDetails([])
          props?.onOk?.();
        } else {
          showMessage.error(res?.value?.msg, 5)
        }
      } catch (err: any) {
        if (err) {
          showMessage.error(err?.errorMessage, 5)
        }
      }
    } else {
      showMessage.warning(i18n.get('本次操作未做修改'))
    }
  };

  return (
    <div className="adjusting-wrapper">
      <div className="modal-adjusting-wrapper">
        <div className="adjusting-operate">
          <div className="adjusting-count">{i18n.get(`共${props?.dataSource?.length}条`)}</div>
        </div>
        <div>
          <EditTableWrapper
            {...props}
            dataSource={dataSource}
            onGetData={onGetData}
            type={'settlementChecking'}
          />
        </div>
      </div>
      <div className="modal-footer">
        <Button className="mr-8" size="small" category="secondary" onClick={handleClose}>
          <T name={'取消'} />
        </Button>
        <Button size="small" category="primary" onClick={handleAdjustment}>
          <T name={'申请调整'} />
        </Button>
      </div>
    </div>
  );
};

export default AdjustingBillsDrawer;
