@import '~@ekuaibao/web-theme-variables/styles/default.less';
@import '~@ekuaibao/web-theme-variables/styles/colors.less';
@import '~@ekuaibao/eui-styles/less/token.less';

.bill_drawer {
  width: 100%;
  margin-bottom: 56px;
  overflow: auto;

  :global {
    .form_item {
      margin-top: 24px;
    }
  }
}

.modal-footer {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  box-shadow: 0px 4px 24px 0px rgba(29, 43, 61, 0.2);
  flex-shrink: 0;
  height: 56px;
  padding: 0 @space-7;
  background: #fff;
  z-index: 200;

  :global {
    .ant-btn {
      font-size: 14px;
      border-radius: 4px;
      border: none;
      font-weight: 400;
      background-color: rgba(29, 43, 61, 0.06);
      color: #1d2b3d;

      &:active,
      &:hover {
        color: var(--brand-base);
      }

      &[disabled] {
        color: #cbcbcb;
        background-color: #f7f7f7;
      }
    }

    .ant-btn-primary {
      color: rgba(255, 255, 255, 1);

      &:active,
      &:hover {
        color: rgba(255, 255, 255, 1);
      }

      &[disabled] {
        color: #cbcbcb;
        background-color: #f7f7f7;
      }
    }
  }
}