/**
 *  Created by pw on 2021/7/30 下午1:25.
 */
import React from 'react';
import { app } from '@ekuaibao/whispered';
import { cloneDeep } from 'lodash';

const EKBIcon = app.require<any>('@elements/ekbIcon');
export interface DeleteButtonProps {
  button: IDeleteButton;
}

export interface IDeleteButton {
  label: string;
  onClick: () => void;
}

export const DeleteButton: React.FC<DeleteButtonProps> = (props) => {
  const { button } = props;
  if (!button) {
    return null;
  }
  const { label, onClick } = button;
  return (
    <div className="delete_button_wrapper" onClick={onClick}>
      <EKBIcon name="#EDico-delete" className="icon-delete" />
      {label}
    </div>
  );
};

interface TemplateProps {
  editable?: boolean;
}

export const template = (props: TemplateProps) => [
  {
    name: 'name',
    label: i18n.get('品类名称'),
    placeholder: i18n.get('请输入品类名称'),
    maxLength: 30,
    type: 'text',
    optional: false,
    labelSize: 'large',
    editable: props?.editable,
  },
  {
    name: 'datalinkFields',
    label: i18n.get('字段设置'),
    placeholder: i18n.get('请填写字段设置'),
    type: 'complex',
    optional: false,
    editable: true,
    labelSize: 'large',
    toolTip: i18n.get('如该品类包含飞机、火车票数据，且后续有计划使用「统一开票」发票复核功能，请保证账单中包含以下字段：机票票号、火车票车次、火车票票面价、火车票乘车人、火车票发车时间'),
    toolTipPlacement: 'right'
  },
];

export const formatFields = (fields: any = {}, isEdit = false) => {
  if (!isEdit) {
    let formFields = cloneDeep(fields);
    formFields = formFields.map((field: any, index: number) => {
      if (index > 1) {
        delete field.name;
      }
      return field;
    });
    return formFields;
  }
  return fields;
};

export const formatCategoryName = (accountName: string, entityName: string): string => {
  return entityName?.replace(`${accountName}-`, '');
};
