import React from 'react';
import moment from 'moment';

export const stateType = {
    SYNC_TODO: "未完成",
    SYNC_FAILED: "失败",
    SYNC_DOING: "未完成",
    SYNC_DONE: "已完成",
}
type states =
    'SYNC_TODO' |
    'SYNC_FAILED' |
    'SYNC_DOING' |
    'SYNC_DONE'

const getDefaultVal = (value: any) => value ?? "-"
const defaultDom = (value: any) => <span>{getDefaultVal(value?.name)}</span>;
const defaultTime = (value: any) => <span>{moment(value).format('YYYY-MM-DD HH:mm')}</span>;
export const createCol = () => {
    return [
        {
            title: i18n.get('操作人'),
            dataIndex: 'staffId',
            key: "staffId",
            render: defaultDom,
        },
        {
            title: i18n.get('部门'),
            dataIndex: 'departmentId',
            key: "departmentId",
            render: defaultDom,
        },
        {
            title: i18n.get('账期'),
            dataIndex: 'billPeriod',
            key: "billPeriod",
            render: defaultDom,
        },
        {
            title: i18n.get('发起时间'),
            dataIndex: 'createTime',
            key: "createTime",
            render: defaultTime,
        },
        {
            title: i18n.get('完成时间'),
            dataIndex: 'updateTime',
            key: "updateTime",
            render: (value: states, line: any) => {
                return line['syncState'] === 'SYNC_DONE' ? defaultTime(value) : ''
            },
        },
        {
            title: i18n.get('状态'),
            dataIndex: 'syncState',
            key: "syncState",
            render: (value: states) => {
                return <span>{stateType[value]}</span>;
            },
        },
    ]
}