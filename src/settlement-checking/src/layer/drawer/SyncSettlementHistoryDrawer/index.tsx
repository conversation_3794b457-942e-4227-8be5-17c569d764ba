/*
 * @Description: 发票方式 新增修改
 * @Creator: chencan<PERSON><PERSON>
 * @Date: 2021-05-31 15:16:33
 */
import React, { FC, useEffect, useState } from 'react';
import { QuerySelect } from 'ekbc-query-builder';
import { EnhanceDrawer } from '@ekuaibao/enhance-layer-manager';
import styles from '../Drawer.module.less';
import { Table } from "antd"
import { depSyncSettlementHistory } from '../../../setttlement-checkin-action';
import { createCol } from "./colUtils"
interface ISyncSettlementHistoryDrawerProps {
    checkingBillId: string;
}

const SyncSettlementHistoryDrawer: FC<ISyncSettlementHistoryDrawerProps> = (props) => {
    const { checkingBillId } = props;
    const [dataSource, setDataSource] = useState([]);
    const getHistory = async () => {
        const query = new QuerySelect()
            .select(
                'staffId(id,name,code), departmentId(id,name,code), billPeriod(id,name,code), categoryIds(id, name, code), `...`',
            )
            .filterBy(`(checkingBillId == \"${checkingBillId}\")`)
            .limit(0, 3000);
        const { items = [] } = await depSyncSettlementHistory({ ...query.value() });
        setDataSource(items)
    };
    useEffect(() => {
        getHistory();
    }, []);
    return (
        <div className={styles['bill_drawer']}>
            <div>
                <Table dataSource={dataSource} columns={createCol()} ></Table>
            </div>
            <div className={styles['modal-footer']}></div>
        </div>
    );
};

export default EnhanceDrawer({
    closeable: true,
})(SyncSettlementHistoryDrawer);
