@import '~@ekuaibao/eui-styles/less/token.less';

.edit-supplier-account-category-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  .edit-supplier-account-default-category {
    padding: @space-6 @space-7;
    display: flex;
  }
  .supplier-account-category-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: space-between;
    overflow: auto;
    .supplier-account-datalink-wrpper {
      padding: @space-6 @space-7;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      .eui-icon-OutlinedTipsInfo{
        margin: 0 8px
      }
    }
    .edit-supplier-account-category-footer {
      height: @space-10;
      padding: 0 @space-7;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;
      border-top: 1px solid @color-line-1;
      .delete_button_wrapper {
        .font-size-2;
        .font-weight-2;
        display: flex;
        align-items: center;
        color: #f4526b;
        cursor: pointer;
        .icon-delete {
          width: @space-6;
          height: @space-6;
          margin-right: @space-4;
        }
      }
    }
  }
}
