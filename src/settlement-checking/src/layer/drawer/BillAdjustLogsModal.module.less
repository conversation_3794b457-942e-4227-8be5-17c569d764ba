@import '~@ekuaibao/web-theme-variables/styles/default';

.billAdjust-modal-wrapper {
	width: 100%;
	height: 100%;
	padding: 16px;
	:global {
		.billAdjust-tab-wrapper {
			.ant-tabs-content {
				height: calc(100% - 40px);
			}
		}
		.billAdjust-table-wrapper {
			display: flex;
			flex-direction: column;
			height: 100%;
			.billAdjust-table {
				overflow: auto;
				flex: 1;
				.table-wrapper {
					height: 100%;
				}
			}
			
			.billAdjust-footer {
				display: flex;
				flex-direction: row;
				align-items: center;
				height: 56px;
				padding: 0 24px;
				box-shadow: 0px 4px 24px 0px rgba(29, 43, 61, 0.2);
				justify-content: flex-end;
				border-top: none;
			}
		}
		
		
		.history-wrapper {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      overflow-y: auto;

      .line-wrapper {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        height: 48px;
        border-bottom: solid 1px #dcdcdc;
        color: @text-color;
        .icon {
          margin: 0 8px 0 16px;
          color: #9c9c9c;
        }
      }
    }
	}
}