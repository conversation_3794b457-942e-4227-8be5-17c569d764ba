import React, { useEffect } from 'react'
import { ILayerProps } from '@ekuaibao/enhance-layer-manager'
import { app, app as api } from '@ekuaibao/whispered'
import { provider, useInstance } from '@ekuaibao/react-ioc'
import * as DataGrid from '@ekuaibao/datagrid'
import { Map } from '@ekuaibao/datagrid/lib/types/utils'
import { PageMode, PaginationConfig } from '@ekuaibao/datagrid/esm/types/pagination'
import { useObserver } from 'mobx-react-lite'
import { BillAdjustLogsVM } from '../../supplier-bill/vms/BillAdjustLogs.vm'
const withLoader = app.require<any>('@elements/data-grid-v2/withLoader')
import styles from './BillAdjustLogsModal.module.less'

interface Props extends ILayerProps {
  bus?: any
  checkingBillId?: string
  entityId?:string
}
export const BillAdjustLogsModal: React.FC<Props> = (props: Props) => {
  const { checkingBillId,entityId } = props

  return (
    <div className={styles['billAdjust-modal-wrapper']}>
       <BudgetLogsTableWrapper checkingBillId={checkingBillId} entityId={entityId} />
    </div>
  )
}

const BudgetLogsTable: React.FC<Props> = (props) => {
  const { checkingBillId,entityId, ...rest } = props
  const vm = useInstance<BillAdjustLogsVM>(BillAdjustLogsVM.NAME)

  useEffect(() => {
    vm.init({ checkingBillId,entityId })
  }, [])

  const handlePageChange = (pagination: PaginationConfig, pageMode: PageMode) => {
    vm?.handlePageChange(pagination, pageMode)
  }

  const handleSorterChange = (sorter: Map<'ascend' | 'descend'>) => {
    vm.sorters = sorter
  }

  const handleFilterChange = (filter: Map<any>) => {
    vm.handleFilterChange(filter)
  }

  const handleSelectedChange = (selectedKeys: string[]) => {
    vm.selectedKeys = selectedKeys
  }

  return useObserver(() => {
    return (
      <div className="billAdjust-table-wrapper">
        <div className="billAdjust-table">
          <DataGrid.TableWrapper
            className="table-wrapper"
            dataSource={vm.dataSource}
            columns={vm.columns}
            selectedRowKeys={vm.selectedKeys}
            sorters={vm.sorters}
            filters={vm.filters}
            scrolling={vm.scrolling}
            isMultiSelect={false}
            allowColumnReordering
            allowColumnResizing
            {...rest}
            pageSize={vm.pageSize}
            onSorterChange={handleSorterChange}
            onFilterChange={handleFilterChange}
            onSelectedChange={handleSelectedChange}
          />
        </div>
        <div className="billAdjust-footer">
          <DataGrid.Pagination
            totalLength={vm.dataTotal}
            pagination={{
              current: vm.currentPage,
              size: vm.pageSize,
            }}
            onChange={handlePageChange}
            pageMode={vm.pageMode}
          />
        </div>
      </div>
    )
  })
}

const BudgetLogsTableWrapper = withLoader(() =>
  Promise.resolve({
    default: provider([BillAdjustLogsVM.NAME, BillAdjustLogsVM])(BudgetLogsTable as any),
  }),
)

export default BillAdjustLogsModal
