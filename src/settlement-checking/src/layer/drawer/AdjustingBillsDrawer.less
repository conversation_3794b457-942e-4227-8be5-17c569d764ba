@import '~@ekuaibao/eui-styles/less/token.less';

.adjusting-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  .modal-adjusting-wrapper {
    padding: 16px;
    .adjusting-operate {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
      .adjusting-count {
        font: var(--eui-font-body-r1);
        color: var(--eui-text-caption);
      }
    }
  }
  .modal-footer {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: right;
    font-size: 14px;
    height: 56px;
    padding: 0 16px;
    border-top: 1px solid var(--eui-line-divider-default);
    background-color: #fff;
  }
}
