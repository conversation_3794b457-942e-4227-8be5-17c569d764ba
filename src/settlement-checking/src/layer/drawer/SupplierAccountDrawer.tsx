/*
 * @Description: 供应商账户编辑弹层
 * @Creator: chencan<PERSON>han
 * @Date: 2021-06-23 18:33:03
 */
import React, { PureComponent } from 'react';
import { app, app as api } from '@ekuaibao/whispered';
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
import { List, Form, Input, Select, Button, InputNumber } from 'antd';
const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;
import { showMessage } from '@ekuaibao/show-util';
import { EnhanceDrawer } from '@ekuaibao/enhance-layer-manager';
import { get } from 'lodash';
import styles from './Drawer.module.less';
const { standardValueMoney } = api.require('@lib/misc');
import { getSupplierArchiveList } from './../../setttlement-checkin-action';

@EnhanceFormCreate()
@EnhanceDrawer({
  closeable: true,
})
export default class SupplierAccountDrawer extends PureComponent {
  state = {
    supplierArchiveType: '',
    archiveList: [],
  };
  private data = [
    {
      name: i18n.get('周'),
      value: 'WEEK',
    },
    {
      name: i18n.get('双周'),
      value: 'BIWEEKLY',
    },
    {
      name: i18n.get('月'),
      value: 'MONTH',
    },
    {
      name: i18n.get('季度'),
      value: 'SEASON',
    },
    {
      name: i18n.get('半年'),
      value: 'HALFYEAR',
    },
    {
      name: i18n.get('年'),
      value: 'YEAR',
    },
    {
      name: i18n.get('不定期'),
      value: 'IRREGULAR',
    },
  ];
  private data2 = [
    {
      name: i18n.get('是'),
      value: true,
    },
    {
      name: i18n.get('否'),
      value: false,
    },
  ];
  private save = () => {
    const { form } = this.props;
    form?.validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }
      const creditAmount = standardValueMoney(get(values, 'creditAmount', 0));
      const balanceAmount = standardValueMoney(get(values, 'balanceAmount', 0));
      this.props?.layer?.emitOk({
        ...values,
        creditAmount,
        balanceAmount,
        supplierArchiveType: this.state?.supplierArchiveType,
      });
    });
  };
  componentDidMount() {
    getSupplierArchiveList({
      select: 'id,name,supplierArchiveType,active',
      orderBy: [{ value: 'name', order: 'DESC' }],
      filterBy: '(active==true)',
    })
      .then((res) => {
        // HOSE_TRIP
        const archiveList = get(res, 'items', []);
        this.setState({ archiveList });
      })
      .catch((err) => {
        showMessage.error(err?.message || err?.errMessage);
      });
  }

  render() {
    const formItemLayout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
    };
    const {
      form: { getFieldDecorator },
      data,
    } = this.props;
    const { archiveList = [] } = this.state;

    return (
      <div className={styles['bill_drawer']}>
        <List>
          <Form>
            <FormItem {...formItemLayout} className={'form_item'} label="账户名称">
              {getFieldDecorator('accountName', {
                initialValue: get(data, 'name') || '',
                rules: [{ required: true, message: i18n.get('请输入账户名称') }],
              })(
                <Input disabled={data?.supplierArchiveType === 'HOSE_TRIP'} placeholder={i18n.get('请输入账户名称')} />,
              )}
            </FormItem>
            <FormItem {...formItemLayout} className={'form_item'} label="供应商">
              {getFieldDecorator('supplierArchiveId', {
                initialValue: get(data, 'supplierArchiveId.id') || '',
                rules: [{ required: true, message: i18n.get('请选择供应商') }],
              })(
                <Select
                  showArrow={true}
                  onChange={(id) => {
                    for (let it of archiveList) {
                      if (it?.id === id) {
                        this.setState({ supplierArchiveType: it?.supplierArchiveType });
                        break;
                      }
                    }
                  }}
                  disabled={!!data}
                  allowClear
                  placeholder={i18n.get('请选择供应商')}
                >
                  {archiveList?.map((it) => {
                    return (
                      <Option
                        key={it?.id}
                        value={it?.id}
                        disabled={it?.supplierArchiveType === 'HOSE_TRIP' ? true : !it?.active}
                      >
                        {it?.name}
                      </Option>
                    );
                  })}
                </Select>,
              )}
            </FormItem>
            <FormItem {...formItemLayout} className={'form_item'} label="账户类型">
              {getFieldDecorator('settlementType', {
                initialValue: get(data, 'settlementType') || '',
                rules: [{ required: true, message: i18n.get('请选择账户类型') }],
              })(
                <Select showArrow={true} disabled={!!data} allowClear placeholder={i18n.get('请选择账户类型')}>
                  <Option value="CREDIT">{i18n.get('授信')}</Option>
                  <Option value="PRIECHARGE">{i18n.get('预存')}</Option>
                </Select>,
              )}
            </FormItem>
            <FormItem {...formItemLayout} className={'form_item'} label="结算周期">
              {getFieldDecorator('period', {
                initialValue: get(data, 'period') || undefined,
              })(
                <Select showArrow={true} allowClear placeholder={i18n.get('请选择结算周期')}>
                  {this.data?.map((it) => {
                    return (
                      <Option key={it?.value} value={it?.value}>
                        {it?.name}
                      </Option>
                    );
                  })}
                </Select>,
              )}
            </FormItem>
            <FormItem {...formItemLayout} className={'form_item'} label="总金额">
              {getFieldDecorator('creditAmount', {
                initialValue: get(data, 'creditAmount.standard') || '',
              })(
                <InputNumber
                  disabled={data?.supplierArchiveType === 'HOSE_TRIP'}
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                />,
              )}
            </FormItem>
            <FormItem {...formItemLayout} className={'form_item'} label="剩余可用金额">
              {getFieldDecorator('balanceAmount', {
                initialValue: get(data, 'balanceAmount.standard') || '',
              })(
                <InputNumber
                  disabled={data?.supplierArchiveType === 'HOSE_TRIP'}
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                />,
              )}
            </FormItem>
            <FormItem {...formItemLayout} className={'form_item'} label="账单日">
              {getFieldDecorator('billingDay', {
                initialValue: get(data, 'billingDay') || '',
              })(
                <Select showArrow={true} allowClear placeholder={i18n.get('请选择账单日')}>
                  {[...Array(28).keys()].map((it) => {
                    return (
                      <Option key={it} value={String(it + 1)}>
                        {it + 1}
                      </Option>
                    );
                  })}
                </Select>,
              )}
            </FormItem>
            <FormItem {...formItemLayout} className={'form_item'} label="还款日">
              {getFieldDecorator('repaymentDay', {
                initialValue: get(data, 'repaymentDay') || '',
              })(
                <Select showArrow={true} allowClear placeholder={i18n.get('请选择还款日')}>
                  {[...Array(28).keys()].map((it) => {
                    return (
                      <Option key={it} value={String(it + 1)}>
                        {it + 1}
                      </Option>
                    );
                  })}
                </Select>,
              )}
            </FormItem>
            <FormItem {...formItemLayout} className={'form_item'} label="是否对账">
              {getFieldDecorator('isReconciliation', {
                initialValue: get(data, 'isReconciliation', undefined),
              })(
                <Select showArrow={true} allowClear placeholder={i18n.get('请选择是否对账')}>
                  {this.data2.map((it) => {
                    return (
                      <Option key={it?.value} value={it?.value}>
                        {it?.name}
                      </Option>
                    );
                  })}
                </Select>,
              )}
            </FormItem>
            <FormItem {...formItemLayout} className={'form_item'} label="是否结算">
              {getFieldDecorator('isSettlement', {
                initialValue: get(data, 'isSettlement', undefined),
              })(
                <Select showArrow={true} allowClear placeholder={i18n.get('请选择是否结算')}>
                  {this.data2.map((it) => {
                    return (
                      <Option key={it?.value} value={it?.value}>
                        {it?.name}
                      </Option>
                    );
                  })}
                </Select>,
              )}
            </FormItem>
            <FormItem {...formItemLayout} className={'form_item'} label="账单导入方式">
              {getFieldDecorator('importMethod', {
                initialValue: get(data, 'importMethod', 'excel'),
              })(
                <Select showArrow={true} placeholder={i18n.get('账单导入方式')}>
                  <Option key={'excel'} value={'excel'}>
                    手动导入
                  </Option>
                  <Option key={'api'} value={'api'}>
                    API同步
                  </Option>
                  <Option key='directly' value='directly'>
                    API写入
                  </Option>
                </Select>,
              )}
            </FormItem>
            <FormItem {...formItemLayout} className={'form_item'} label="描述">
              {getFieldDecorator('description', {
                initialValue: get(data, 'description') || '',
              })(<TextArea rows={4} placeholder={i18n.get('请输入描述文字')} />)}
            </FormItem>
          </Form>
        </List>

        <div className={styles['modal-footer']}>
          <Button type="primary" className="btn-ml mr-8" onClick={this.save}>
            {i18n.get('保存')}
          </Button>
          <Button
            className="btn-ml mr-8"
            onClick={() => {
              this.props?.layer?.emitCancel();
            }}
          >
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    );
  }
}
