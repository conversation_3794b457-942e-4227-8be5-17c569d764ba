/*
 * @Description: 抬头 新增修改
 * @Creator: chencan<PERSON>han
 * @Date: 2021-05-31 15:12:38
 */
import React, { PureComponent } from 'react';
import { app } from '@ekuaibao/whispered';
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
import { Form, Input, Select, Button } from 'antd';
import { showMessage } from '@ekuaibao/show-util';
import { EnhanceConnect } from '@ekuaibao/store';
const FormItem = Form.Item;
const { Option } = Select;
const TreeSelectSingle = app.require<any>('@elements/puppet/TreeSelectSingle');
import { getPayerInfo } from '../../setttlement-checkin-action';
import { get } from 'lodash';
import { EnhanceDrawer } from '@ekuaibao/enhance-layer-manager';
import styles from './Drawer.module.less';

@EnhanceFormCreate()
@EnhanceDrawer({
  closeable: true,
})
@EnhanceConnect((state: any) => ({
  baseDataProperties: state['@common'].globalFields.data,
}))
export default class HoseBillTitleDrawer extends PureComponent {
  state = {
    initLoading: true,
    loading: false,
    entityFieldLabel: '',
    data: [],
    list: [],
    treeNodeData: [],
    legalEntityId: '',
    entityFieldSelectData: []
  };

  componentDidMount() {
    app.dataLoader('@common.globalFields').load();
    this.getPayerInfo();
    this.getData()
  }

  getData() {
    const name = 'basedata.Dimension.法人实体'
    app.invokeService('@common:get:staff:dimension', { name }).then((res:any) => {
      this.setState({treeNodeData: res?.items || []})
    })
  }
  private getPayerInfo() {
    getPayerInfo().then((res) => {
      const { oldData,entityField,legalEntityId } = this.props;
      let items = get(res, 'items', []) || [];
      if (oldData) {
        const idArr = oldData.map((it) => it?.id);
        items = items.map((it) => {
          if (idArr?.includes(it.id)) {
            return { ...it, disabled: true };
          }
          return it;
        });
      }
      const baseDataPropertiesFilter = this.props?.baseDataProperties?.filter((it) => {
        return it?.dataType?.entity === 'basedata.Dimension.法人实体' && it?.name != '法人实体';
      });
      if(entityField) {
        for (let i = 0; i < baseDataPropertiesFilter?.length; i++) {
          const it = baseDataPropertiesFilter[i];
          if (it?.name === entityField) {
            this.setState({ entityFieldLabel: it.label });
            break;
          }
        }
      }
      this.setState({ data: items,legalEntityId:legalEntityId,entityFieldSelectData:baseDataPropertiesFilter });
    });
  }
  private save = () => {
    const { form, oldData } = this.props;
    form?.validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }
      const obj = {
        ...values,
        entityFieldLabel: this.state.entityFieldLabel,
        // address: '',
      };
      if (oldData) {
        const index = oldData?.findIndex((it) => it.id === values?.corpPayerInfoId);
        if (index > -1) {
          return showMessage.error(i18n.get('已存在该抬头名称'));
        }
      }
      this.props?.layer?.emitOk(obj);
    });
  };

  private handleLegalEntityIdChange = (valueObj:any) => {
    const {
      form: { setFieldsValue },
    } = this.props;
    setFieldsValue && setFieldsValue({ legalEntityId: valueObj?.id || '' });
    this.setState({ legalEntityId: valueObj?.id || '' })
  }

  render() {
    const formItemLayout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
    };
    const {
      form: { getFieldDecorator, setFieldsValue },
      id,
      entityField,
      payerNo,
      mailingAddress,
      receiverName,
      tel,
      email,
    } = this.props;
    const { legalEntityId,treeNodeData,entityFieldSelectData } = this.state
    return (
      <div className={styles['bill_drawer']}>
        <Form>
          <FormItem {...formItemLayout} className={'form_item'} label="抬头名称">
            {getFieldDecorator('corpPayerInfoId', {
              initialValue: id || '',
              rules: [{ required: true, message: i18n.get('请选择抬头名称') }],
            })(
              <Select
                showArrow={true}
                allowClear
                disabled={!!id}
                onChange={(id) => {
                  for (let i = 0; i < this.state?.data?.length; i++) {
                    const it = this.state.data[i];
                    if (it?.id === id) {
                      setFieldsValue && setFieldsValue({ payerNo: it?.payerNo || '' });
                      break;
                    }
                  }
                }}
                placeholder={i18n.get('请选择发票类型')}
              >
                {this.state?.data.map((it) => {
                  return (
                    <Option key={it?.id} value={it?.id} disabled={it?.disabled}>
                      {it?.name}
                    </Option>
                  );
                })}
              </Select>,
            )}
          </FormItem>
          <FormItem {...formItemLayout} className={'form_item'} label="抬头税号">
            {getFieldDecorator('payerNo', {
              initialValue: payerNo || '',
            })(<Input disabled placeholder={i18n.get('请输入抬头税号')} />)}
          </FormItem>
          <FormItem {...formItemLayout} className={'form_item'} label="法人实体字段">
            {getFieldDecorator('entityField', {
              initialValue: entityField,
              rules: [{ required: true, message: i18n.get('请选择法人实体字段') }],
            })(
              <Select
                showArrow={true}
                allowClear
                placeholder={i18n.get('请选择法人实体字段')}
                onChange={(name) => {
                  for (let i = 0; i < entityFieldSelectData?.length; i++) {
                    const it = entityFieldSelectData[i];
                    if (it?.name === name) {
                      this.setState({ entityFieldLabel: it.label });
                      break;
                    }
                  }
                }}
              >
                {entityFieldSelectData?.map((it) => {
                  return (
                    <Option key={it?.name} value={it?.name}>
                      {it?.label}
                    </Option>
                  );
                })}
              </Select>,
            )}
          </FormItem>
          <FormItem {...formItemLayout} className={'form_item'} label="法人实体">
            {getFieldDecorator('legalEntityId', {
              initialValue: legalEntityId || '',
              rules: [{ required: true, message: i18n.get('请选择法人实体') }],
            })(<TreeSelectSingle
              dropdownStyle={{ maxHeight: 280 }}
              data={{
                placeholder: i18n.get('请选择法人实体'),
                id: legalEntityId,
                multiple: false,
                treeNodeData: treeNodeData,
                onChange: this.handleLegalEntityIdChange,
              }}
            />)}
          </FormItem>
          <FormItem {...formItemLayout} className={'form_item'} label="邮寄地址">
            {getFieldDecorator('mailingAddress', {
              initialValue: mailingAddress,
              rules: [{ required: true, message: i18n.get('请输入邮寄地址(1-50字符)'), max: 50 }],
            })(<Input placeholder={i18n.get('请输入邮寄地址(1-50字符)')} />)}
          </FormItem>
          <FormItem {...formItemLayout} className={'form_item'} label="接收人姓名">
            {getFieldDecorator('receiverName', {
              initialValue: receiverName,
              rules: [{ required: true, message: i18n.get('请输入接收人姓名(1-20字符)'), max: 20 }],
            })(<Input placeholder={i18n.get('请输入接收人姓名(1-20字符)')} />)}
          </FormItem>
          <FormItem {...formItemLayout} className={'form_item'} label="接收人电话">
            {getFieldDecorator('tel', {
              initialValue: tel,
              rules: [{ required: true, message: i18n.get('请输入接收人电话(1-11字符)'), max: 11 }],
            })(<Input placeholder={i18n.get('请输入接收人电话(1-11字符)')} />)}
          </FormItem>
          <FormItem {...formItemLayout} className={'form_item'} label="接收人邮箱">
            {getFieldDecorator('email', {
              initialValue: email,
              rules: [{ required: true, message: i18n.get('请输入接收人邮箱(1-50字符)'), max: 50 }],
            })(<Input placeholder={i18n.get('请输入接收人邮箱(1-50字符)')} />)}
          </FormItem>
        </Form>
        <div className={styles['modal-footer']}>
          <Button type="primary" className="btn-ml mr-8" onClick={this.save}>
            {i18n.get('保存')}
          </Button>
          <Button
            className="btn-ml mr-8"
            onClick={() => {
              this.props?.layer?.emitCancel();
            }}
          >
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    );
  }
}
