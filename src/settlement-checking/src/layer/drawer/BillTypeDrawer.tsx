/*
 * @Description: 发票方式 新增修改
 * @Creator: chencan<PERSON>han
 * @Date: 2021-05-31 15:16:33
 */
import React, { PureComponent } from 'react';
import { app } from '@ekuaibao/whispered';
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
import { List, Form, Select, Button } from 'antd';
const FormItem = Form.Item;
const { Option } = Select;
import { showMessage } from '@ekuaibao/show-util';
const FeeTypeSelect = app.require('@elements/feeType-tree-select');
import { EnhanceConnect } from '@ekuaibao/store';
import { EnhanceDrawer } from '@ekuaibao/enhance-layer-manager';
import styles from './Drawer.module.less';

@EnhanceFormCreate()
@EnhanceDrawer({
  closeable: true,
})
@EnhanceConnect((state) => {
  return {
    feeTypes: state['@common'].feetypes.data,
    feeTypeMap: state['@common'].feetypes.map,
  };
})
export default class BillTypeDrawer extends PureComponent {
  state = {
    initLoading: true,
    loading: false,
    data: [],
    list: [],
  };
  private data = [
    {
      name: i18n.get('出租车发票'),
      value: 'INVOICE_TAXI',
    },
    {
      name: i18n.get('增值税发票'),
      value: 'INVOICE',
    },
    {
      name: i18n.get('其他'),
      value: 'INVOICE_OTHER',
    },
    {
      name: i18n.get('铁路客票'),
      value: 'INVOICE_TRAIN',
    },
    {
      name: i18n.get('客运汽车发票'),
      value: 'INVOICE_PASSENGER_CAR',
    },
    {
      name: i18n.get('航空运输电子客票行程单'),
      value: 'INVOICE_AIRCRAFT',
    },
    {
      name: i18n.get('过路费发票'),
      value: 'INVOICE_ROAD_TOLL',
    },
    {
      name: i18n.get('定额发票'),
      value: 'INVOICE_QUOTA',
    },
    {
      name: i18n.get('机打发票'),
      value: 'INVOICE_MACHINE_PRINT',
    },
  ];
  private save = () => {
    const { form, oldData } = this.props;
    form?.validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }
      if (oldData) {
        const index = oldData?.findIndex((it) => it.id === values?.feeTypeId);
        if (index > -1) {
          return showMessage.error(i18n.get('已存在该费用类型'));
        }
      }
      this.props?.layer?.emitOk(values);
    });
  };
  componentDidMount() {
    app.dataLoader('@common.feetypes').load();
  }

  render() {
    const formItemLayout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
    };
    const {
      feeTypes = [],
      form: { getFieldDecorator },
      feeTypeId,
      invoiceCategorys,
    } = this.props;

    return (
      <div className={styles['bill_drawer']}>
        <List>
          <Form>
            <FormItem {...formItemLayout} className={'form_item'} label="费用类型">
              {getFieldDecorator('feeTypeId', {
                initialValue: feeTypeId || '',
                rules: [{ required: true, message: i18n.get('请选择费用类型') }],
              })(
                <FeeTypeSelect
                  useTreeSelectRC
                  showFeeTypeCode
                  filterOnSearch
                  allowClear={false}
                  className="fee-type-select"
                  size="large"
                  feeTypes={feeTypes}
                  value={feeTypeId}
                  disabled={!!feeTypeId}
                  checkedKeys={feeTypeId}
                  disabledCheckedFather
                />,
              )}
            </FormItem>
            <FormItem {...formItemLayout} className={'form_item'} label="发票类型">
              {getFieldDecorator('invoiceCategorys', {
                initialValue: invoiceCategorys || [],
                rules: [{ required: true, message: i18n.get('请选择类型') }],
              })(
                <Select mode="multiple" showArrow={true} allowClear placeholder={i18n.get('请选择发票类型')}>
                  {this.data.map((it) => {
                    return (
                      <Option key={it?.value} value={it?.value}>
                        {it?.name}
                      </Option>
                    );
                  })}
                </Select>,
              )}
            </FormItem>
          </Form>
        </List>

        <div className={styles['modal-footer']}>
          <Button type="primary" className="btn-ml mr-8" onClick={this.save}>
            {i18n.get('保存')}
          </Button>
          <Button
            className="btn-ml mr-8"
            onClick={() => {
              this.props?.layer?.emitCancel();
            }}
          >
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    );
  }
}
