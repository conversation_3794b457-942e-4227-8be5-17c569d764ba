import React from 'react';
import styles from './SupplierEdit.module.less';
import { inject, provider } from '@ekuaibao/react-ioc';
import { app } from '@ekuaibao/whispered';
import { SupplierEditVm } from '../supplier-dimension/vms';
import { observer } from 'mobx-react';
import { Dynamic } from '@ekuaibao/template';
import { Form, Button } from 'antd';
import ItemWrapper from '../components/ItemWrapper';
import Note from '../components/Note';
import Attachments from '../components/Attachments';
import PayeeList from '../components/PayeeList';
import AccountList from '../components/AccountList';
import { showMessage } from '@ekuaibao/show-util';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
import { ISupplier } from '@ekuaibao/ekuaibao_types';

const { editable } = app.require('@components/index.editable');

function create(T: any) {
  return Form.create<>({
    onValuesChange(props, values) {
      props.onValuesChange(values);
    },
  })(T);
}
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 19 },
};

interface SupplierEditProps extends ILayerProps {
  supplier?: ISupplier;
}
interface SupplierEditState {}
@provider([SupplierEditVm.NAME, SupplierEditVm])
@observer
export default class SupplierEdit extends React.Component<SupplierEditProps, SupplierEditState> {
  @inject(SupplierEditVm.NAME) vm: SupplierEditVm;
  async componentDidMount() {
    await this.vm.init(this.props.supplier);
  }

  handleSave = async () => {
    try {
      await this.vm.save();
      showMessage.success(i18n.get('保存成功'), 0.5, () => {
        this.props.layer.emitOk({});
      });
    } catch (e) {
      !!e?.message && showMessage.error(e.message);
    }
  };

  handleCancel = () => {
    console.log('handleCancel');
    this.props.layer.emitCancel();
  };

  valueChange = () => {
    console.log('valueChange');
  };

  render() {
    return (
      <div className={styles['supplier-edit-wrapper']}>
        <div className="content">
          <Dynamic
            className="template"
            value={this.vm.templateValue}
            elements={editable}
            template={this.vm.template}
            onValuesChange={this.valueChange}
            bus={this.vm.bus}
            layout={layout}
            create={create}
          />
          <ItemWrapper name={i18n.get('收款信息')}>
            <PayeeList />
          </ItemWrapper>
          {this.vm.showAccount && (
            <ItemWrapper name={i18n.get('账户信息')}>
              <AccountList
                onDetail={(it) => {
                  this.props?.keel?.open('SupplierDetailView', {
                    data: { id: it?.id },
                    title: it?.name || i18n.get('供应商账户详情'),
                  });
                  this.props.layer.emitCancel();
                }}
              />
            </ItemWrapper>
          )}
          <ItemWrapper name={i18n.get('备注')}>
            <Note />
          </ItemWrapper>
          <ItemWrapper name={i18n.get('附件资料')}>
            <Attachments />
          </ItemWrapper>
        </div>
        <div className="footer-action">
          <Button onClick={this.handleCancel}>{i18n.get('取消')}</Button>
          <Button className="btn-ml" type="primary" onClick={this.handleSave}>
            {i18n.get('保存')}
          </Button>
        </div>
      </div>
    );
  }
}
