@import '~@ekuaibao/eui-styles/less/token';

.add-settlement-wrapper {
  display: flex;
  flex-direction: column;
  .add-settlement-content {
    padding: @space-7 @space-6 @space-11 @space-6;
    .radio-row {
      margin-top: @space-7;
      display: flex;
      align-items: center;
      &:first-child {
        margin-top: 0;
      }
      .radio-wrapper {
        width: 500px;
        height: 94px;
        margin-left: @space-6;
        padding: @space-6 @space-7;
        background: #f7f7f7;
        border-radius: 5px;
        .title {
          .font-size-2;
          .font-weight-2;
          color: rgba(0, 0, 0, 0.6);
          max-width: 70%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .content {
          .font-size-1;
          .font-weight-2;
          margin-top: @space-4;
          color: rgba(0, 0, 0, 0.6);
        }
      }
    }
  }
}
