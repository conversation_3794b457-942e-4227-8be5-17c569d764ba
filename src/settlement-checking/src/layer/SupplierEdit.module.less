@import "~@ekuaibao/eui-styles/less/token";

.supplier-edit-wrapper {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 60px;
  bottom: 0;
  left: 0;
  right: 0;

  :global {
    .content {
      flex: 1;
      overflow: auto;
    }

    .template {
      padding: @space-7 0 0 @space-7;
      border-bottom: 1px solid #e6e6e6;
    }

    .footer-action {
      padding: @space-6;
      border-top: 1px solid @color-line-1;
      display: flex;
      justify-content: flex-end;

      .btn-ml {
        margin-left: @space-4;
      }
    }

    .col-bottom-16 {
      margin-bottom: @space-6;
    }
    .error {
      color: #ff7c7c;
    }

    .error-border {
      border-color: #ff7c7c;
    }

    .item-wrapper {
      padding: @space-7;
      border-bottom: 1px solid #e6e6e6;
      .name {
        .font-size-3;
        //.font-weight-2;
        font-weight: 500;
        margin-bottom: @space-6;
      }
    }
  }
}
