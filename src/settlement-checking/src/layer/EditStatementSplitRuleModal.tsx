/**
 *  Created by pw on 2021/6/2 下午9:34.
 */
import React, { useState } from 'react';
import './EditStatementSplitRuleModal.less';
import DynamicWrapper from '../components/DynamicWrapper';
import { Button, Icon } from 'antd';
import { T } from '@ekuaibao/i18n';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
import SplitRule from '../components/SplitRule';

import MessageCenter from '@ekuaibao/messagecenter';
import { IExtendBus } from '@ekuaibao/template';
import { saveSplitConfig } from '../setttlement-checkin-action';
import { showMessage } from '@ekuaibao/show-util';
import { hideLoading, showLoading } from '@ekuaibao/lib/lib/lib-util';
import { SplitIF } from '@ekuaibao/ekuaibao_types';

interface Props extends ILayerProps {
  rule?: SplitIF;
}

const EditStatementSplitRuleModal: React.FC<Props> = (props) => {
  const { rule } = props;
  const [bus] = useState<IExtendBus>(new MessageCenter() as IExtendBus);

  const handleClose = () => {
    props.layer.emitCancel();
  };

  const handleConfirm = async () => {
    const values = await bus.getValueWithValidate(0);
    const validateValues = await bus.getValueWithValidate(1);
    showLoading('');
    await saveSplitConfig({ ...values, ...validateValues, id: rule?.id })
      .then(() => {
        showMessage.success('保存成功');
        hideLoading();
        props.layer.emitOk({});
      })
      .catch((e) => {
        hideLoading();
        showMessage.error(e.message);
      });
  };

  const Btn: any = Button;

  return (
    <div className="edit-statement-splite-rule-wrapper">
      <div className="modal-header">
        <div className="flex title">
          <T name="对账拆分配置" />
        </div>
        <Icon className="cross-icon" type="cross" onClick={handleClose} />
      </div>
      <div className="header">当对账单引用此拆分规则时，系统将在单据生成提交时，自动拆分成多个子对账单</div>
      <div className="edit-statement-splite">
        <DynamicWrapper value={rule} template={template()} bus={bus} elements={[SplitRule as any]} />
      </div>
      <div className="modal-footer">
        <Button className="mr-8" onClick={handleClose}>
          <T name={'取消'} />
        </Button>
        <Btn className="mr-8" type={'primary'} onClick={handleConfirm}>
          <T name={'确定'} />
        </Btn>
      </div>
    </div>
  );
};

const template = () => [
  {
    name: 'name',
    label: i18n.get('规则名称'),
    placeholder: i18n.get('请输入规则名称'),
    maxLength: 50,
    type: 'text',
    optional: false,
    editable: true,
  },
  {
    name: 'description',
    label: i18n.get('描述'),
    placeholder: i18n.get('请输入描述'),
    maxLength: 50,
    type: 'text',
    optional: true,
    editable: true,
  },
  {
    name: 'rules',
    label: i18n.get('分拆维度规则'),
    placeholder: i18n.get('请添加拆分规则'),
    maxLength: 50,
    type: 'composite',
    optional: false,
    editable: true,
  },
];

export default EditStatementSplitRuleModal;
