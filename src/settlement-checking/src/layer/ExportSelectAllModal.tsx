import React  from 'react'
import { I<PERSON>, But<PERSON> } from 'antd'
// @ts-ignore
import styles from './ExportSelectAllModal.module.less'

interface Props {
  count: number
  amount: string
  layer: any
  vm: any
  onDelete: () => void
  onLaunchChecking: () => void
  onRemind: () => void
}

const Btn: any = Button

const ExportSelectAllModal: React.FC<Props> = (props) => {
  const { vm, layer, onDelete, onLaunchChecking, onRemind } = props

  const handleModalClose = () => {
    vm.isSelectAll = false
    layer.emitCancel()
  }

  const handleDelete = () => {
    onDelete()
    handleModalClose()
  }

  const handleLaunchChecking = () => {
    onLaunchChecking()
    handleModalClose()
  }

  const handleRemind = () => {
    onRemind()
    handleModalClose()
  }
  return (
    <div className={styles['select-modal-wrapper']}>
      <div className="modal-header">
        <span className="flex"></span>
        <Icon className="cross-icon" type="cross" onClick={handleModalClose} />
      </div>
      <div className="select-modal-wrapper-content">
        <div className="info">
          <span className="title">
            {i18n.get('已选择全部')}
            <span className="title_bold">{vm.allFlowIds?.length || 0}</span>
            {i18n.get('条单据，请选择相应操作')}
          </span>
        </div>
        <div className="action-box">
          <Btn type="primary" className='btn' onClick={handleLaunchChecking}>{i18n.get('发起对账')}</Btn>
          <Btn ghost type="danger" className='btn' onClick={handleDelete}>{i18n.get('删除')}</Btn>
          <Btn onClick={handleRemind}>{i18n.get('催办')}</Btn>
        </div>
      </div>
    </div>
  )
}

export default ExportSelectAllModal
