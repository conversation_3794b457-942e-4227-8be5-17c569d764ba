/**
 *  Created by pw on 2021/6/18 下午3:34.
 */
import React, { useState } from 'react';
import { T } from '@ekuaibao/i18n';
import { Button, Icon, Radio } from 'antd';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
import './AddSettlementModal.less';
import { app } from '@ekuaibao/whispered';
import { RadioChangeEvent } from 'antd/es/radio';
import { showMessage } from '@ekuaibao/show-util';
import { generateSettlement } from '../setttlement-checkin-action';
const RefView = app.require<any>('@elements/puppet/Ref');

interface Props extends ILayerProps {
  checkingBillId: string;
  isSettlementField?: boolean;
}

const AddSettlementModal: React.FC<Props> = (props) => {
  const { checkingBillId, isSettlementField = false } = props;
  const entity = `basedata.Dimension.法人实体`;
  const [entityId, setEntityId] = useState('');
  const [settlementType, setSettlementType] = useState<RadioEntity>(
    isSettlementField ? RadioEntity.DefaultEntity : RadioEntity.AssignEntity,
  );
  const handleClose = () => {
    props.layer.emitCancel();
  };

  const handleEntityChange = (value: any) => {
    setEntityId(value?.id);
  };

  const handleRadioChange = (e: RadioChangeEvent) => {
    setSettlementType(e.target.value);
  };

  const handleConfirm = async () => {
    if (settlementType === RadioEntity.AssignEntity && !entityId?.length) {
      showMessage.error('请选择法人实体');
      return;
    }
    const data = { checkingBillId, entityId: settlementType === RadioEntity.DefaultEntity ? undefined : entityId };
    await generateSettlement(data)
      .then((res) => {
        props.layer.emitOk({});
      })
      .catch((err) => {
        showMessage.error(err.message);
      });
  };

  const Btn: any = Button;
  return (
    <div className="add-settlement-wrapper">
      <div className="modal-header">
        <div className="flex title">
          <T name="发起结算" />
        </div>
        <Icon className="cross-icon" type="cross" onClick={handleClose} />
      </div>
      {isSettlementField ? (
        <div className="add-settlement-content">
          <div className={'radio-row'}>
            <div className="radio-wrapper">
              <div className="title">按照账单明细字段维度结算</div>
              <div className="content">将按照对账单中明细上的字段维度为您生成结算单</div>
            </div>
          </div>
        </div>
      ) : (
        <div className="add-settlement-content">
          <Radio.Group defaultValue={RadioEntity.AssignEntity} onChange={handleRadioChange}>
            <Radio className={'radio-row'} value={RadioEntity.AssignEntity}>
              <div className="radio-wrapper">
                <div className="title">请选择法人实体：</div>
                <div className="content">
                  <RefView
                    onlyLeafCanBeSelected={false}
                    param={{ name: entity, withVisibility: true }}
                    entity={entity}
                    data={{
                      id: entityId,
                      placeholder: '请选择法人实体',
                      disabled: settlementType === RadioEntity.DefaultEntity,
                      onChange: handleEntityChange,
                    }}
                  />
                </div>
              </div>
            </Radio>
            <Radio className={'radio-row'} value={RadioEntity.DefaultEntity}>
              <div className="radio-wrapper">
                <div className="title">按照法人实体维度结算</div>
                <div className="content">选择「按照法人实体维度结算」后，会按照对账单的法人实体个数生成结算单</div>
              </div>
            </Radio>
          </Radio.Group>
        </div>
      )}
      <div className="modal-footer">
        <Button className="mr-8" onClick={handleClose}>
          <T name={'取消'} />
        </Button>
        <Btn className="mr-8" type={'primary'} onClick={handleConfirm}>
          <T name={'确定'} />
        </Btn>
      </div>
    </div>
  );
};

enum RadioEntity {
  AssignEntity = 'AssignEntity',
  DefaultEntity = 'DefaultEntity',
}

export default AddSettlementModal;
