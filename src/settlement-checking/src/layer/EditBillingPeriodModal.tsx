import React from 'react';
import DynamicWrapper from '../components/DynamicWrapper';
import './EditBillingPeriodModal.less';
import { Bus } from '@ekuaibao/template';
import { Button, Icon } from 'antd';
import { T } from '@ekuaibao/i18n';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
import { hideLoading, showLoading } from '@ekuaibao/lib/lib/lib-util';
import { saveBillingPeriod } from '../setttlement-checkin-action';
import { showMessage } from '@ekuaibao/show-util';

interface EditBillingPeriodModalProps extends ILayerProps {
  value: any;
}

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 19 },
};

export const EditBillingPeriodModal: React.FC<EditBillingPeriodModalProps> = ({ value, layer }) => {
  const bus = Bus();
  const handleClose = () => {
    layer.emitCancel();
  };
  const handleConfirm = async () => {
    const values = await bus.getValueWithValidate(0);
    const value = formatValue(values);
    try {
      showLoading('');
      await saveBillingPeriod(value);
      showMessage.success('保存成功');
      hideLoading();
      layer.emitOk({});
    } catch (e) {
      hideLoading();
      showMessage.error(e.message);
    }
  };
  const Btn: any = Button;
  return (
    <div className="edit-billing-period-wrapper">
      <div className="modal-header">
        <div className="flex title">
          <T name="对账单期间配置" />
        </div>
        <Icon className="cross-icon" type="cross" onClick={handleClose} />
      </div>
      <div className="edit-bill-period">
        <DynamicWrapper value={value} layout={layout} template={template()} bus={bus} />
      </div>
      <div className="modal-footer">
        <Button className="mr-8" onClick={handleClose}>
          <T name={'取消'} />
        </Button>
        <Btn className="mr-8" type={'primary'} onClick={handleConfirm}>
          <T name={'确定'} />
        </Btn>
      </div>
    </div>
  );
};

const template = () => [
  {
    name: 'name',
    label: i18n.get('账单周期'),
    placeholder: i18n.get('请选择账单周期'),
    type: 'date-month',
    optional: false,
    editable: true,
  },
  {
    name: 'startTime',
    label: i18n.get('开始时间'),
    placeholder: i18n.get('请选择开始时间'),
    type: 'date',
    defaultValue: Date.now(),
    optional: false,
    editable: true,
  },
  {
    name: 'endTime',
    label: i18n.get('结束时间'),
    placeholder: i18n.get('请选择结束时间'),
    type: 'date',
    defaultValue: Date.now(),
    optional: false,
    editable: true,
  },
];

const formatValue = (value) => {
  const result = { name: value.name, startTime: 0, endTime: 0 };
  if (value.startTime) {
    result.startTime = new Date(new Date(value.startTime).format('YYYY-MM-DD 00:00:00')).getTime();
  }
  if (value.endTime) {
    result.endTime = new Date(new Date(value.endTime).format('YYYY-MM-DD 23:59:59')).getTime();
  }
  return result;
};

export default EditBillingPeriodModal;
