/**
 *  Created by pw on 2023/6/14 15:59.
 */
import React, { useEffect, useState } from 'react';
import { Icon, Tooltip } from 'antd';
import { T } from '@ekuaibao/i18n';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
import { ISelectedField } from './BillFieldTypes';
import { Button, Select, Input, message } from '@hose/eui';
import { OutlinedTipsAdd, OutlinedTipsReduce } from '@hose/eui-icons';
import './BatchModifyBillFieldValue.less';
import { uuid } from '@ekuaibao/helpers';
import FixedValueSelect from '../components/FixedValueSelect';
import { setTimeout } from 'timers';

interface Props extends ILayerProps {
  changeFields: ISelectedField[];
}

interface IDataItem {
  id: string;
  field: string;
  value: any;
  fieldData?: ISelectedField;
  options: ISelectedField[];
}

const BatchModifyBillFieldValue: React.FC<Props> = (props) => {
  const { layer, changeFields } = props;
  const [data, setData] = useState<IDataItem[]>([{ id: uuid(8), field: '', value: '', options: changeFields }]);
  const [fieldMap, setFieldMap] = useState<Record<string, ISelectedField>>({});
  const [leftChange, setLeftChange] = useState(undefined)
  useEffect(() => {
    const map = changeFields.reduce((result, field) => {
      result[field.name] = field;
      return result;
    }, {} as any);
    setFieldMap(map);
  }, []);

  const fnFilterLeftFieldOptions = (data: IDataItem[] = []): IDataItem[] => {
    const allAddFieldMap = data.reduce((result, item) => {
      result[item.field] = item;
      return result;
    }, {} as any);
    data.forEach((item) => {
      if (!item.field.length) {
        return;
      }
      item.options = changeFields.filter((field) => {
        return field.name === item.field || !allAddFieldMap[field.name];
      });
    });
    return data;
  };

  const handleModalClose = () => {
    layer?.emitCancel();
  };

  const handleConfirm = () => {
    let errorMsg = '';
    data.forEach((item) => {
      if (item.value === undefined || !item.field?.length) {
        errorMsg = `「${item.fieldData?.label}」填写不完整`;
        return;
      }
    });
    if (errorMsg.length) {
      message.warn(errorMsg);
      return;
    }
    console.log('=====start--log=====');
    const formatValue = data.map((item) => {
      return { field: item.field, value: item.value };
    });
    console.log(formatValue);
    console.log('=====end--log=====');
    layer.emitOk(formatValue);
  };

  const handleAdd = () => {
    const allAddFieldMap = data.reduce((result, item) => {
      result[item.field] = item;
      return result;
    }, {} as any);
    const remainingFields = changeFields.filter((field) => !allAddFieldMap[field.name]);
    if (remainingFields.length) {
      data.push({ id: uuid(8), field: '', value: '', options: remainingFields });
      const changeAfterData = fnFilterLeftFieldOptions(data);
      setData(changeAfterData.slice());
    }
  };

  const handleMinus = (field: string) => {
    const filterData = data.filter((item) => item.field !== field);
    const changeAfterData = fnFilterLeftFieldOptions(filterData);
    setData(changeAfterData.slice());
  };

  const handleLeftSourceDataChange = (changeData: IDataItem, changeField: string) => {
    const changeFieldIndex = data.findIndex((item) => item.id === changeData.id);
    changeData.field = changeField;
    changeData.value = null;
    changeData.fieldData = fieldMap[changeField];
    data[changeFieldIndex] = changeData;
    const changeAfterData = fnFilterLeftFieldOptions(data);
    setLeftChange({ [changeFieldIndex]: true })
    setData(changeAfterData.slice());
    setTimeout(() => {
      setLeftChange(undefined)
    }, 10)
  };

  const handleRightValueChange = (dataItem: IDataItem, value: any) => {
    const changeFieldIndex = data.findIndex((item) => item.id === dataItem.id);
    dataItem.value = value;
    data[changeFieldIndex] = dataItem;
    setData(data.slice());
  };

  return (
    <div className={'batch-modify-bill-field-value-wrapper'}>
      <div className="modal-header">
        <span className="flex">{i18n.get('批量修改')}</span>
        <Icon className="cross-icon" type="cross" onClick={handleModalClose} />
      </div>
      <div className="modify-bill-content">
        {data.map((dataItem, idx) => {
          return (
            <div key={dataItem.id} className="field-row">
              <div className="left-prefix">
                <T name={'将'} />
              </div>
              <Select
                className="field-select"
                value={dataItem.field}
                showSearch={true}
                placeholder={'请选择要修改的字段'}
                optionFilterProp="label"
                onChange={(value) => handleLeftSourceDataChange(dataItem, value)}
              >
                {dataItem.options.map((item) => {
                  return (
                    <Select.Option key={item.name} value={item.name} label={item.label}>
                      {item.label}
                    </Select.Option>
                  );
                })}
              </Select>
              <div className="left-prefix">
                <T name={'改为'} />
              </div>
              <RightComponent
                idx={idx}
                leftChange={leftChange}
                field={dataItem.fieldData}
                value={dataItem.value}
                onChange={(value) => handleRightValueChange(dataItem, value)}
              />
              <div className="right-action">
                {data.length === changeFields.length ? (
                  <Tooltip title={i18n.get('暂无添加字段')}>
                    <OutlinedTipsAdd className="add-field" />
                  </Tooltip>
                ) : (
                  <OutlinedTipsAdd className="add-field" onClick={handleAdd} />
                )}
                {data.length === 1 ? (
                  <Tooltip title={i18n.get('至少保留一个')}>
                    <OutlinedTipsReduce className="add-field" />
                  </Tooltip>
                ) : (
                  <OutlinedTipsReduce className="add-field" onClick={() => handleMinus(dataItem.field)} />
                )}
              </div>
            </div>
          );
        })}
      </div>
      <div className="modal-footer">
        <Button className="mr-8" category={'secondary'} onClick={handleModalClose}>
          <T name={'取消'} />
        </Button>
        <Button category={'primary'} onClick={handleConfirm}>
          <T name={'确定'} />
        </Button>
      </div>
    </div>
  );
};

interface RightComponentProps {
  field?: ISelectedField;
  value?: any;
  onChange: (value: any) => void;
}

const RightComponent: React.FC<RightComponentProps> = (props) => {
  const { field, value, onChange, leftChange, idx } = props;

  const handleChange = (value: any) => {
    onChange(value);
  };
  // if (leftChange) {
  //   return null
  // }

  if (!field) {
    return <Input value={value} onChange={(e) => handleChange(e.target.value)} />;
  }

  let sourceType = field.type;
  let datalinkId;
  let multiple = false;
  let entity = field.entity;
  if (field.type === 'ref') {
    sourceType = field.entity!;
    if (sourceType.startsWith('datalink.DataLinkEntity')) {
      sourceType = 'datalink.DataLinkEntity';
      entity = sourceType;
      datalinkId = field.entity?.split('.')[2];
    }
    if (sourceType.startsWith('basedata.Dimension')) {
      sourceType = 'basedata.Dimension';
    }
  }
  if (field.type === 'list') {
    multiple = true;
    sourceType = field.elemType?.entity!;
    entity = field.elemType?.entity!;
    if (sourceType.startsWith('basedata.Dimension')) {
      sourceType = 'basedata.Dimension';
    }
  }

  return (
    <FixedValueSelect
      idx={idx}
      value={value}
      type={sourceType}
      field={{ ...field, dataType: { entity } }}
      style={{ width: '100%' }}
      needFullData={true}
      multiple={multiple}
      datalinkId={datalinkId}
      useTreeSelectRC={true}
      onFixedChange={handleChange}
      leftChange={leftChange}
    />
  );
};

export default BatchModifyBillFieldValue;
