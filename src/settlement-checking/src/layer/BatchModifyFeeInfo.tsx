import React from 'react';
import { inject, provider, useInstance } from '@ekuaibao/react-ioc';
import { BatchModifyFeeTypeVm, IModifyRule } from '../settlement-checking/vms/batch-modify-feeType.vm';
import { T } from '@ekuaibao/i18n';
import { app } from '@ekuaibao/whispered';
import { Button, Icon, Select } from 'antd';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
import { useObserver } from 'mobx-react-lite';
import { observer } from 'mobx-react';
import styles from './BatchModifyFeeInfo.module.less';
import { toJS } from 'mobx';
import { showMessage } from '@ekuaibao/show-util';
const TreeSelectSingle = app.require<any>('@elements/puppet/TreeSelectSingle');
const TagSelector = app.require<any>('@elements/tag-selector')
const SVG_DELETE = require('../images/delete.svg');
const SVG_ADD = require('../images/add.svg');
const Btn: any = Button;

interface BatchModifyFeeInfoProps extends ILayerProps {
  selectedKeys: string[];
  selectedData: any[]
}
interface BatchModifyFeeInfoState { }
@provider([BatchModifyFeeTypeVm.NAME, BatchModifyFeeTypeVm])
@observer
export default class BatchModifyFeeInfo extends React.Component<BatchModifyFeeInfoProps, BatchModifyFeeInfoState> {
  @inject(BatchModifyFeeTypeVm.NAME) vm: BatchModifyFeeTypeVm;
  componentDidMount() {
    this.vm.initValue(this.props.selectedKeys, this.props.selectedData);
  }

  handleConfirm = async () => {
    try {
      await this.vm.saveValue();
      showMessage.success(i18n.get('修改成功'));
      this.props.layer.emitOk({});
    } catch (e) {
      showMessage.error(e.message);
    }
  };

  handleClose = () => {
    this.props.layer.emitCancel();
  };

  render() {
    return (
      <div className={styles['batch-modify-wrapper']}>
        <div className="modal-header">
          <div className="flex title">
            <T name="批量修改" />
            <Icon type="question-circle" style={{ color: '#3491FA', fontSize: '14px', margin: '0 2px 0 8px' }} />
            <span style={{ fontSize: '12px', color: '#333', fontWeight: 400 }}>请注意：费用类型若设置了联动关系，会导致依赖字段发生变动</span>
          </div>
          <Icon className="cross-icon" type="cross" onClick={this.handleClose} />
        </div>
        <div className="content">
          <div className="content-wrapper  mg-24 pd-16">
            {this.vm.modifyRules.map((item, index) => {
              return <RuleItem key={`${item.key}${index}`} value={item} index={index} />;
            })}
          </div>
        </div>
        <div className="modal-footer">
          <Button className="mr-8" onClick={this.handleClose}>
            <T name={'取消'} />
          </Button>
          <Btn className="mr-8" type={'primary'} onClick={this.handleConfirm}>
            <T name={'确定'} />
          </Btn>
        </div>
      </div>
    );
  }
}

interface IRuleItemProps {
  value: IModifyRule;
  index: number;
}

const RuleItem: React.FC<IRuleItemProps> = ({ value, index }) => {
  console.log(value)
  const vm = useInstance<BatchModifyFeeTypeVm>(BatchModifyFeeTypeVm.NAME);

  const handleAdd = () => {
    vm.addRule(index);
  };
  const handleDel = () => {
    vm.delRule(index);
  };

  const handleKeyChange = async (v) => {
    if (v !== value.key) {
      value.key = v;
      value.value = undefined;
      vm.ruleChange(value, index);
    }
  };
  const handleValueChange = (v) => {
    value.value = v.id;
  };


  const handleSelectStaff = async () => {
    let checkedList = [
      {
        type: 'department-member',
        checkIds: value.options === 'isMultiStaff' ? value.value : [value.value]
      }
    ]
    const params = {
      data: checkedList,
      multiple: value.options === 'isMultiStaff',
      required: true,
      fetchDataSourceAction: {
        staff: app.invokeService('@organizationManagement:get:visibility:staffs')
      },
      notFollowExternalChargeRules: false
    }
    const checkedData = await app.open('@organizationManagement:SelectStaff', { ...params })
    value.value = value.options === 'isMultiStaff' ? checkedData?.[0]?.checkIds : checkedData?.[0].checkIds?.[0];
    vm.checkedStaff.set(index, checkedData?.[0]?.checkList)
  }

  const handleTagChange = (deleteItem) => {
    const list = Array.isArray(value.value) ? value.value : [value.value]
    const newValue = list?.filter(id => id !== deleteItem.id)
    value.value = value.options === 'isMultiStaff' ? newValue : newValue?.[0]
    const s = vm.checkedStaff.get(index)
    const newData = s.filter(s => s.id !== deleteItem.id)
    vm.checkedStaff.set(index, newData)

  }
  return useObserver(() => {
    return (
      <div className="dp-f jc-c ai-c m-b-8">
        <div className="m-r-8 dp-f ai-c">
          <span className="m-r-8">{i18n.get('将')}</span>
          <Select
            className="m-r-8"
            size="large"
            value={value?.key}
            style={{ width: 200 }}
            placeholder={i18n.get('请选择要修改的字段')}
            onSelect={handleKeyChange}
          >
            {vm.globalFields.map((line: any) => {
              return (
                <Select.Option key={line.name} value={line.name}>
                  {line.label}
                </Select.Option>
              );
            })}
          </Select>
          <span className="m-r-8">{i18n.get('更改为')}</span>
          <div style={{ width: 400 }}>
            {value.options === 'isStaff' || value.options === 'isMultiStaff' ? <TagSelector
              className="selectPerson"
              value={vm.checkedStaff?.get(index)}
              editable={true}
              onClick={() => handleSelectStaff()}
              onChange={(data, deleteItem) => handleTagChange(deleteItem)}
              placeholder={i18n.get('请选择人员')}
            /> : <TreeSelectSingle
              dropdownStyle={{ maxHeight: 280 }}
              data={{
                placeholder: i18n.get('请选择对应的值'),
                id: value.value,
                editDept: true,
                treeNodeData: toJS(value.options),
                onChange: handleValueChange,
              }}
            />}
          </div>
        </div>
        <div>
          <img className="oper mr-8" src={SVG_ADD} onClick={handleAdd} />
          {vm.modifyRules.length !== 1 && <img className="oper" src={SVG_DELETE} onClick={handleDel} />}
        </div>
      </div>
    );
  });
};
