 import React, { useEffect, useState } from 'react';
 import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
 import { T } from '@ekuaibao/i18n';
 import { Button, Icon } from 'antd';
 import DynamicWrapper from '../components/DynamicWrapper';
 import { hideLoading, showLoading } from '@ekuaibao/lib/lib/lib-util';
 import { saveApiSyncConfig } from '../setttlement-checkin-action';
 import { showMessage } from '@ekuaibao/show-util';
 import { Bus } from '@ekuaibao/template';
 import './FeeGenerateConfig.less';
 import ApiFilterCodition from '../components/ApiFilterCodition';
 import AssignmentRuleMapping from '../components/AssignmentRuleMapping';
 import SupplierType from '../components/SupplierType';
 import { DatalinkIF, SupplierSettingDetailConfigIF } from '@ekuaibao/ekuaibao_types';
 
 interface ISupplierSettingDetailCondition extends SupplierSettingDetailConfigIF {}
 
 interface Props extends ILayerProps {
   config: ISupplierSettingDetailCondition;
   datalink?: DatalinkIF;
 }
 
 const ApiSynchroConfigModal: React.FC<Props> = (props) => {
   const { config, datalink } = props;
   const [bus] = useState(Bus());
 
   useEffect(() => {
     bus.setValidateLevel(1);
   }, []);
 
   const handleClose = () => {
     props.layer.emitCancel();
   };
 
   const handleConfirm = async () => {
     const values: any = await bus.getValueWithValidate(0).catch((err) => {
       bus.setValidateLevel(1);
     });
     if (values) {
       const { filterDataConfig = {} } = values;
       delete values.filterDataConfig;
       showLoading('');
       await saveApiSyncConfig({ ...values, ...filterDataConfig, id: config?.id })
         .then((err) => {
           console.log(err);
           showMessage.success('保存成功');
           hideLoading();
           props.layer.emitOk(values);
         })
         .catch((e) => {
           showMessage.error(e.message);
         });
     }
   };
   const Btn: any = Button;
 
   return (
     <div className="fee-generate-config-wrapper">
       <div className="modal-header">
         <div className="flex title">
           <T name="对账单字段取值配置" />
         </div>
         <Icon className="cross-icon" type="cross" onClick={handleClose} />
       </div>
       <div className="edit-fee-generate-rule">
         <DynamicWrapper
           value={
             config
               ? {
                   ...config,
                   filterDataConfig: {
                     categoryId: config?.categoryId,
                     supplierAccountId: config?.supplierAccountId,
                     billOrderType: config?.billOrderType
                   },
                 }
               : undefined
           }
           editData={config}
           template={template()}
           tags={{
             relationField: { feeTypeId: config?.feeTypeId, dataLinkEntity: datalink },
             mappings: { dataLinkEntity: datalink },
             conditions: { dataLinkEntity: datalink },
           }}
           bus={bus}
           elements={[
             ApiFilterCodition,
             AssignmentRuleMapping,
             SupplierType as any,
           ]}
         />
       </div>
       <div className="modal-footer">
         <Button className="mr-8" onClick={handleClose}>
           <T name={'取消'} />
         </Button>
         <Btn className="mr-8" type={'primary'} onClick={handleConfirm}>
           <T name={'确定'} />
         </Btn>
       </div>
     </div>
   );
 };
 
 const template = () => [
   {
     name: 'name',
     label: i18n.get('配置名称'),
     placeholder: i18n.get('请输入配置名称'),
     maxLength: 10,
     type: 'text',
     optional: false,
     editable: true,
   },
   {
     name: 'description',
     label: i18n.get('配置描述'),
     placeholder: i18n.get('请配置描述'),
     maxLength: 50,
     type: 'text',
     optional: true,
     editable: true,
   },
   {
    name: 'billPlatform',
    label: i18n.get('供应商类型'),
    type: 'ref',
    placeholder: i18n.get('请选择供应商类型'),
    optional: false,
    editable: true,
  },
   {
     name: 'filterDataConfig',
     label: i18n.get('选择品类'),
     placeholder: i18n.get('请选择品类'),
     type: 'api-filter-condition',
     optional: false,
     editable: true,
   },
   {
    name: 'mappings',
    label: i18n.get('配置赋值规则'),
    type: 'ref',
    optional: false,
    editable: true,
  },
 ];
 
 export default ApiSynchroConfigModal;
 