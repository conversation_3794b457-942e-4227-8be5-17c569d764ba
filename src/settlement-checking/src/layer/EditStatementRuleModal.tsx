/**
 *  Created by pw on 2021/6/2 下午7:28.
 */
import React, { useEffect, useState } from 'react';
import './EditStatementRuleModal.less';
import { OutlinedTipsClose } from '@hose/eui-icons';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
import { SplitIF, SupplierAccountIF } from '@ekuaibao/ekuaibao_types';
import { saveStatementConfig, getCheckinList, getSupplierAccountList } from '../setttlement-checkin-action';
import { hideLoading, showLoading } from '@ekuaibao/lib/lib/lib-util';
import { showMessage } from '@ekuaibao/show-util';
import { Radio, Alert, Button, Form, Input, Select, Space, Modal } from '@hose/eui';

interface ISupplierAccount extends SupplierAccountIF {
  supplierAccountId?: any;
  splitConfigId: any;
}

interface Props extends ILayerProps {
  config?: ISupplierAccount;
}

const EditStatementRuleModal: React.FC<Props> = (props) => {
  const formatDate = (() => {
    const now = new Date();
    const year = now.getFullYear() % 100;
    const month = now.getMonth() + 1;
    return `${year} 年 ${month} 月`;
  })();

  const { config } = props;
  const [values, setValues] = useState<any>({});
  const [tags, setTags] = useState<Record<string, any>>();

  useEffect(() => {
    fnUpdateValue();
  }, []);

  const fnUpdateValue = async () => {
    await fetchOptions();
    const values = {
      supplierAccountId: config?.supplierAccountId?.id,
      splitConfigId: config?.splitConfigId?.id,
    };
    setValues(values);
  };

  const fetchOptions = async () => {
    const { items: splitRules } = await getCheckinList();
    const splits = splitRules.map((split: SplitIF) => ({ ...split, value: split.id, label: split.name }));
    const { items: accountList } = await getSupplierAccountList({});
    const accounts = accountList.map((account: SupplierAccountIF) => ({
      ...account,
      value: account.id,
      label: account.name,
    }));

    setTags({ splitConfigId: splits, supplierAccountId: accounts });
  };

  const [form] = Form.useForm();

  const handleClose = () => {
    props.layer.emitCancel();
  };

  const handleConfirm = async () => {
    form.validateFields().then(async values => {
      try {
        showLoading('');
        await saveStatementConfig({ ...values, id: config?.id })
          .then(() => {
            showMessage.success('保存成功');
            hideLoading();
            props.layer.emitOk(values);
          })
          .catch((err) => {
            hideLoading();
            showToast(err.message);
          });
      } catch (e) {
        hideLoading();
        showMessage.error(e.message);
      }
    })
  };

  const showToast = (message: string) => {
    Modal.error({
      title: '提示',
      content: message,
      centered: true,
      onOk() { },
    });
  };

  return (
    <div className="edit-statement-rule-wrapper">
      <div className="statement-rule-header">
        <div className="title">对账单设置</div>
        <OutlinedTipsClose className="cross-icon" onClick={handleClose} />
      </div>
      <Form layout="vertical" form={form}>
        <div className="statement-rule-option">
          <Form.Item
            className="statement-rule-option-select"
            label="供应商账户"
            rules={[{ required: true, message: '供应商账户不能为空' }]}
            name="supplierAccountId"
            initialValue={config?.supplierAccountId.id}
          >
            <Select options={tags?.supplierAccountId}></Select>
          </Form.Item>
          <Form.Item
            className="statement-rule-option-select"
            label="拆分规则"
            rules={[{ required: true, message: '拆分规则不能为空' }]}
            name="splitConfigId"
            initialValue={config?.splitConfigId.id}
          >
            <Select options={tags?.splitConfigId}></Select>
          </Form.Item>
          <Form.Item
            className="statement-rule-option-radio"
            name="titleGenBy"
            label="子对账单命名方式"
            initialValue={config ? (config?.titleGenBy ?? 'DEFAULT') : 'PERIOD'}
          >
            <Radio.Group>
              <Radio className="option-radio" value={'DEFAULT'}>
                按单据模板标题公式命名
              </Radio>
              <Radio className="option-radio" value={'PERIOD'}>
                按账期命名
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
          >
            {({ getFieldValue }) =>
              getFieldValue('titleGenBy') === 'PERIOD' ? (
                <>
                  <Form.Item
                    className="statement-rule-option-select"
                    label="子对账单标题"
                    rules={[{ required: true, message: '子对账标题不能为空' }]}
                    name="titleTpl"
                    initialValue={config?.titleTpl || '消费对账单'}
                  >
                    <div className="edit-son-statement-name">
                      <div>某年某月</div>
                      <Input showCount maxLength={8} defaultValue={config?.titleTpl || '消费对账单'} />
                    </div>
                  </Form.Item>
                  <div className="effect-preview">
                    效果预览：{formatDate}
                    {getFieldValue('titleTpl')}
                  </div>
                  <Alert
                    showIcon={false}
                    className="alert-statement-rule"
                    message="按账期命名子对账单时，单据模板「对账单(系统)」的标题写入方式需配置为「手动填写」，否则无法正常生成子对账单。"
                  />
                </>
              ) : (
                <Alert
                  showIcon={false}
                  className="alert-statement-rule"
                  message="请在单据模板「对账单(系统)」的标题字段配置规则"
                />
              )
            }
          </Form.Item>
        </div>
        <Form.Item className="statement-rule-footer">
          <Space>
            <Button className="bottom-btn" category="secondary" onClick={handleClose}>
              取消
            </Button>
            <Button className="bottom-btn" category="primary" onClick={handleConfirm} htmlType="submit">
              确定
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </div>
  );
};

export default EditStatementRuleModal;
