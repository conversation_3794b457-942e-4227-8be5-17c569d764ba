/*
 * @Description:
 * @Creator: chencan<PERSON>han
 * @Date: 2021-05-28 14:19:57
 */

import { app } from '@ekuaibao/whispered';
import React, { Component } from 'react';
import { Keel, registerComponentsCellar } from '@ekuaibao/keel';
const KeelSingleViewHeader = app.require<any>('@elements/puppet/KeelSingleViewHeader');
const KeelViewBody = app.require<any>('@elements/puppet/KeelViewBody');

@registerComponentsCellar([
  {
    key: 'SupplierDetailView',
    getComponent: () => import('./SupplierDetailView'),
  },
  {
    key: 'SupplierManagerView',
    getComponent: () => import('./SupplierManagerView'),
    title: i18n.get('供应商账户'),
  },
])
export default class ContactsView extends Component {
  render() {
    return (
      <Keel>
        <KeelSingleViewHeader viewKey="SupplierManagerView" showHeader={false} />
        <KeelViewBody classNameKey="content-main" />
      </Keel>
    );
  }
}
