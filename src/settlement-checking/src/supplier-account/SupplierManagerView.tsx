/*
 * @Description: 供应商账户管理列表
 * @Creator: chencan<PERSON>han
 * @Date: 2021-05-28 10:59:52
 */
import React from 'react';
import style from './SupplierManagerView.module.less';
import { app } from '@ekuaibao/whispered';
import { get, isBoolean } from 'lodash';
import {
  getSupplierAccountList,
  getSupplierArchiveList,
  syncAccount,
  disableSupplierAccount,
  restoreSupplierAccount,
  putSupplierAccount,
  postSupplierAccount,
} from '../setttlement-checkin-action';
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
import { showMessage, showModal } from '@ekuaibao/show-util';
import { Form } from 'antd';
import { Input, Select, Button, Tooltip } from '@hose/eui'
import { injectKeel, KeelVM } from '@ekuaibao/keel';
import { QuerySelect } from 'ekbc-query-builder';
import disable from '../images/disable.svg';
import enable from '../images/enable.svg';
import { name2icon, settlementType2Obj } from '../helpers/utils';
import newIcon from '../images/new.svg';
const FormItem = Form.Item;
const { Option } = Select;
const Money = app.require<any>('@elements/puppet/Money');
const EKBIcon = app.require('@elements/ekbIcon');

interface SupplierManagerViewProps {
  form: any;
}
interface SupplierManagerViewState {
  showBtn: boolean;
  archiveList: any[];
  accountList: any[];
  supplierArchiveId: string;
}

@EnhanceFormCreate()
export default class SupplierManagerView extends React.Component<SupplierManagerViewProps, SupplierManagerViewState> {
  @injectKeel()
  keel: KeelVM | undefined;

  constructor(props: SupplierManagerViewProps | Readonly<SupplierManagerViewProps>) {
    super(props);
    this.state = {
      showBtn: false,
      archiveList: [],
      accountList: [],
      supplierArchiveId: '',
      loading: false
    };
  }

  componentDidMount() {
    this.getList({});
    this.getArchiveList();
  }
  async getList(data: { active?: any; settlementType?: any; name?: any; supplierArchiveId?: any; }) {
    this.setState({ loading: true });
    const q = new QuerySelect().select('supplierArchiveId(id,name),...');
    if (data) {
      if (isBoolean(data?.active)) {
        q.filterBy(`active==${data?.active}`);
      }
      if (data?.settlementType) {
        q.filterBy(`settlementType=="${data?.settlementType}"`);
      }
      if (data?.name) {
        q.filterBy(`name.contains("${data?.name}")`);
      }
      if (data?.supplierArchiveId) {
        q.filterBy(`supplierArchiveId=="${data?.supplierArchiveId}"`);
      }
    }
    q.asc('createTime');
    getSupplierAccountList({ ...q.value() })
      .then((res) => {
        this.setState({ accountList: get(res, 'items', []), loading: false });
      })
      .catch((err) => {
        showMessage.error(err?.message || err?.errMessage);
        this.setState({ loading: false });
      });
  }
  async getArchiveList() {
    getSupplierArchiveList({ select: 'id,name,supplierArchiveType' })
      .then((res) => {
        // HOSE_TRIP
        const archiveList = get(res, 'items', []);
        this.setState({ archiveList });
        // for (let i = 0; i < archiveList.length; i++) {
        //   const it = archiveList[i];
        //   if (it?.supplierArchiveType === 'HOSE_TRIP') {
        //     const {
        //       form: { setFieldsValue },
        //     } = this.props;
        //     setFieldsValue && setFieldsValue({ supplierArchiveId: it?.id });
        //     this.setState({ showBtn: true, supplierArchiveId: it?.id });
        //     break;
        //   }
        // }
      })
      .catch((err) => {
        showMessage.error(err?.message || err?.errMessage);
      });
  }

  private handleSearch = () => {
    const {
      form: { validateFieldsAndScroll },
    } = this.props;
    validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }
      this.getList(values);
    });
  };
  private handleReset = () => {
    const {
      form: { resetFields },
    } = this.props;
    resetFields();
    this.getList({});
  };
  private handleRefresh = () => {
    const { supplierArchiveId } = this.state;
    syncAccount({ id: supplierArchiveId })
      .then((res) => {
        this.handleSearch();
      })
      .catch((err) => {
        showMessage.error(err?.message || err?.errMessage);
      });
  };
  private switchStatus = (it) => {
    const { id, active, supplierArchiveType } = it;
    if (supplierArchiveType !== 'HOSE_TRIP') {
      showModal.confirm({
        title: i18n.get('提示'),
        content: i18n.get(`确定${active ? '停用账户' : '启用账户'}？`),
        onOk: async () => {
          if (active) {
            disableSupplierAccount({ id })
              .then((res) => {
                showMessage.success(i18n.get('停用账户成功'));
                this.handleSearch();
              })
              .catch((err) => {
                showMessage.error(err?.message || err?.errMessage);
              });
          } else {
            restoreSupplierAccount({ id })
              .then((res) => {
                showMessage.success(i18n.get('启用账户成功'));
                this.handleSearch();
              })
              .catch((err) => {
                showMessage.error(err?.message || err?.errMessage);
              });
          }
        },
      });
    } else {
      showMessage.info('账户状态不可修改');
    }
  };
  private addNew = () => {
    app.open('@settlement-checkin:SupplierAccountDrawer', { title: i18n.get('新建供应商账户') }).then((res) => {
      postSupplierAccount(res)
        .then((re) => {
          showMessage.success('新建供应商账户成功');
          this.handleSearch();
        })
        .catch((err) => {
          showMessage.error(err?.message || err?.errMessage);
        });
    });
  };

  render() {
    const {
      form: { getFieldDecorator },
    } = this.props;
    const { archiveList, accountList, showBtn } = this.state;
    return (
      <div className={style['supplier_manager_view']}>
        <div className="header">
          <div className="title">{this.props?.title}</div>
          <div className="right_view">
            <Button type="primary" onClick={this.addNew}>
              <EKBIcon name="#EDico-plus-default" className="ico" />
              {i18n.get('新建')}
            </Button>
          </div>
        </div>
        <div className="query_view">
          <Form layout="inline">
            <FormItem className={'form_item'} label="账户名称">
              {getFieldDecorator('name', {
                rules: [],
              })(<Input placeholder={i18n.get('请输入账户名称')} style={{ width: 216 }} />)}
            </FormItem>
            <FormItem className={'form_item'} label="供应商">
              {getFieldDecorator('supplierArchiveId', {
                rules: [],
              })(
                <Select
                  notFoundContent={i18n.get('暂无数据')}
                  allowClear
                  placeholder={i18n.get('请选择供应商')}
                  style={{ width: 216 }}
                  onChange={(id) => {
                    this.setState({ showBtn: false, supplierArchiveId: undefined }, () => {
                      for (let i = 0; i < archiveList?.length; i++) {
                        const it = archiveList[i];
                        if (it?.id === id && it?.supplierArchiveType === 'HOSE_TRIP') {
                          this.setState({ showBtn: true, supplierArchiveId: it?.id });
                          break;
                        }
                      }
                    });
                  }}
                >
                  {archiveList?.map((it) => {
                    return (
                      <Option key={it.id} value={it.id}>
                        <Tooltip placement="bottom" title={it.name}>
                          {it.name}
                        </Tooltip>
                      </Option>
                    );
                  })}
                </Select>,
              )}
            </FormItem>
            <FormItem className={'form_item'} label="账户类型">
              {getFieldDecorator('settlementType', {
                initialValue: '',
                rules: [],
              })(
                <Select allowClear placeholder={i18n.get('请选择账户类型')} style={{ width: 80 }}>
                  <Option value="">{i18n.get('全部')}</Option>
                  <Option value="CREDIT">{i18n.get('授信')}</Option>
                  <Option value="PRIECHARGE">{i18n.get('预存')}</Option>
                </Select>,
              )}
            </FormItem>
            <FormItem className={'form_item'} label="账户状态">
              {getFieldDecorator('active', {
                initialValue: '',
                rules: [],
              })(
                <Select allowClear placeholder={i18n.get('账户状态')} style={{ width: 96 }}>
                  <Option value="">全部</Option>
                  <Option value={true}>启用中</Option>
                  <Option value={false}>已停用</Option>
                </Select>,
              )}
            </FormItem>
            <FormItem className={'form_item'}>
              <Button category="primary" onClick={this.handleSearch}>
                {i18n.get('查询')}
              </Button>
              <Button style={{ marginLeft: 8 }} category="secondary" onClick={this.handleReset}>
                {i18n.get('重置')}
              </Button>
              {showBtn && (
                <Button style={{ marginLeft: 8 }} onClick={this.handleRefresh} category="secondary" >
                  {i18n.get('刷新')}
                </Button>
              )}
            </FormItem>
          </Form>
        </div>

        <div className="lists_view">
          {accountList?.map((it: { id: React.Key | null | undefined; balanceAmount: any; creditAmount: any; supplierArchiveType: any; }) => {
              const isExceedLimit = (numStr: string): boolean => {
                const hasDot = numStr?.includes('.');
                const digitCount = numStr?.replace(/\D/g, '')?.length;
                return hasDot ? digitCount > 16 : digitCount > 14;
              };
              const isStandardTooLong = isExceedLimit(it?.balanceAmount?.standard || '');
              return (
                <div
                  key={it?.id}
                  className={`item_view ${get(name2icon(get(it, 'supplierArchiveId.name', '')), 'style', '')} ${
                    get(it, 'active') ? '' : 'disable_bgc'
                  }`}
              >
                {get(name2icon(get(it, 'supplierArchiveId.name', '')), 'icon', false) ? (
                  <div className="icon_view">
                    <img src={get(name2icon(get(it, 'supplierArchiveId.name', '')), 'icon')} />
                  </div>
                ) : null}
                <div className="top_view">
                  <div className="corporation">{get(it, 'name', '')}</div>
                  <div
                    className="status_view"
                    onClick={() => {
                      this.switchStatus(it);
                    }}
                  >
                    <img src={get(it, 'active') ? enable : disable} />
                    <div className="status">{get(it, 'active') ? i18n.get('启用中') : i18n.get('已停用')}</div>
                  </div>
                  <div className="settlement_type_view">
                    <img src={get(settlementType2Obj(get(it, 'settlementType')), 'icon')} />
                    {get(settlementType2Obj(get(it, 'settlementType')), 'label')}
                  </div>
                </div>

                <div
                  className="content_view"
                  onClick={() => {
                    this?.keel?.open('SupplierDetailView', {
                      data: { id: it?.id },
                      title: get(it, 'name', ''),
                    });
                  }}
                >
                  <div className="box">
                    <div className="left_b">
                      <div className="item_v">
                        <Money
                          className="money"
                          showSymbol={false}
                          isShowThousandsSeparator
                          value={it?.balanceAmount}
                          style={ isStandardTooLong ? { fontSize: 26 } : {}}
                        />
                      </div>
                      <div className="available">{i18n.get('剩余可用余额/元')}</div>
                    </div>
                    {get(it, 'settlementType', '') === 'CREDIT' ? (
                      <div className="right_b">
                        <Money
                          className="money"
                          showSymbol={false}
                          isShowThousandsSeparator
                          value={it?.creditAmount}
                        />
                        <div className="available">{i18n.get('授信额度/元')}</div>
                      </div>
                    ) : null}
                  </div>
                </div>
                <div className="bottom_view">
                  <div className="supplier_name">{get(it, 'supplierArchiveId.name', '')}</div>
                  <div
                    className="edit"
                    onClick={() => {
                      app
                        .open('@settlement-checkin:SupplierAccountDrawer', {
                          title: i18n.get('编辑供应商账户'),
                          data: it,
                        })
                        .then((res) => {
                          putSupplierAccount({ ...res, id: it?.id, supplierArchiveType: it?.supplierArchiveType })
                            .then((re) => {
                              showMessage.success('编辑供应商账户成功');
                              this.handleSearch();
                            })
                            .catch((err) => {
                              //showMessage.error(err?.message || err?.errMessage);
                            });
                        });
                    }}
                  >
                    {i18n.get(`编辑`)}
                  </div>
                </div>
              </div>
            );
          })}
          {!this.state.loading && <div className="item_new" onClick={this.addNew}>
            <div className="content">
              <img className="icon" src={newIcon} />
              <div className="new">{i18n.get('创建账户')}</div>
            </div>
          </div>}
        </div>
      </div>
    );
  }
}
