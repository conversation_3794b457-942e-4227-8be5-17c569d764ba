@import '~@ekuaibao/web-theme-variables/styles/default.less';
@import '~@ekuaibao/web-theme-variables/styles/colors.less';
@import '~@ekuaibao/eui-styles/less/token.less';

.supplier_detail_view {
  width: 100%;
  height: 100%;
  background-color: rgba(247, 247, 247, 1);

  :global {
    .header {
      background: white;
      height: 52px;
      width: 100%;
      display: flex;
      padding: 0 24px;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 20px;
        font-weight: 500;
        max-width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .container {
      width: 100%;
      display: flex;
      flex: 1;
      flex-direction: column;
      padding: @space-6;
      .account_info_view {
        display: flex;
        flex: 1;
        min-height: 101px;
        max-height: 101px;
        flex-shrink: 0;
        background: var(--brand-base);
        border-radius: 6px;
        align-items: center;
        color: #ffffff;
        position: relative;
        .icon {
          width: 103px;
          height: 119px;
          position: absolute;
          bottom: -30px;
          right: 0;
        }

        .money {
          font-size: 26px;
          font-family: DINAlternate, DINAlternate-Bold, sans-serif;
          font-weight: 700;
        }

        .more_view {
          display: flex;
          align-items: flex-end;

          .danwei {
            opacity: 0.92;
            font-size: 14px;
          }

          .more {
            margin-left: 14px;
            font-size: 12px;
            opacity: 0.92;
            text-decoration: underline;
            cursor: pointer;
          }
        }

        .ml24 {
          margin-left: 24px;
        }

        .ml73 {
          margin-left: 73px;
        }

        .company {
          position: absolute;
          bottom: 8px;
          right: 8px;
          opacity: 0.92;
          font-size: 12px;
          max-width: 70%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .disable_bgc {
        background: #b3b3b3;
      }

      .company_info_view {
        margin-top: @space-6;
        height: 138px;
        flex-shrink: 0;
        opacity: 1;
        background: #ffffff;
        border-radius: 8px;
        padding: @space-6;
        display: flex;
        align-items: center;

        .row {
          display: flex;
          margin-top: 12px;

          .label {
            opacity: 0.76;
            font-size: 14px;
            text-align: right;
            color: #142234;
          }

          .content {
            margin-left: 16px;
            font-size: 14px;
            color: #333333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            .refresh {
              margin-left: 8px;
              color: @color-brand;
              cursor: pointer;
            }
          }

          .money_content {
            font-weight: 600;
          }

          .disable {
            color: #f06e6e;
          }
        }

        .row:first-child {
          margin-top: 0;
        }
      }

      .beneficiary_view {
        margin-top: @space-6;
        background: #ffffff;
        border-radius: 8px;
        padding: 24px;
        padding-right: 0;

        .title {
          opacity: 0.76;
          font-size: 14px;
          color: #142234;
        }

        .items {
          display: flex;
          flex-wrap: wrap;
          .no_data_view {
            width: 264px;
            height: 99px;
            opacity: 1;
            background: #f5f5f5;
            border-radius: 5px;
            margin-top: 16px;
            margin-right: 32px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            color: #999999;

            .icon {
              width: 40px;
              height: 40px;
              margin-right: 16px;
            }
          }

          .item {
            width: 264px;
            height: 99px;
            opacity: 1;
            background: #f5f5f5;
            border-radius: 5px;
            margin-top: 16px;
            padding: 10px;
            margin-right: 32px;
            position: relative;

            display: flex;

            .default_view {
              position: absolute;
              opacity: 1;
              background: #f2fdff;
              border-radius: 0px 5px 0px 0px;
              right: 0;
              color: var(--brand-base);
              font-size: 12px;
              top: 0;
              padding: 2px 10px;
            }

            .icon {
              width: 40px;
              height: 40px;
            }

            .info_view {
              margin-left: 16px;

              font-size: 14px;
              color: #333333;

              .bank_number {
                margin-top: 2px;
                font-size: 16px;
                font-weight: 600;
                font-variant: normal;
              }

              .company {
                margin-top: 6px;
                font-size: 12px;
              }
            }
          }
        }
      }

      .supplier_bill_setting_title {
        opacity: 0.76;
        font-size: 16px;
        font-weight: 500;
        color: #142234;
        margin-top: 24px;
      }

      .supplier_bill_setting_view {
        margin-top: 16px;
        padding: 19px 24px 24px;
        background: #ffffff;
        border-radius: 8px;

        .title_view {
          display: flex;
          justify-content: space-between;

          .title {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            font: var(--eui-font-body-r1);
            color: var(--eui-text-caption);

            .eui-icon {
              margin-left: 4px;
              font-size: 16px;
              color: var(--eui-icon-n2);
            }
          }
        }

        .action_view {
          display: flex;
          font-size: 14px;

          .edit {
            color: var(--brand-base);
            cursor: pointer;
          }

          .delete {
            margin-left: 24px;
            color: #f06e6e;
            cursor: pointer;
          }
        }

        .table {
          .ant-table-thead > tr > th {
            font-size: 14px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
          }

          .ant-table-tbody > tr > td {
            color: rgba(0, 0, 0, 0.65);
            font-size: 14px;
          }
        }
      }
    }
  }
}
