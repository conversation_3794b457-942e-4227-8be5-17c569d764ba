/**
 *  Created by pw on 2021/7/21 下午9:11.
 */
import React, { useState, useEffect } from 'react';
import styles from './SuppplierBillTemplate.module.less';
import CategoryComponent from '../../components/CategoryComponent';
import { app } from '@ekuaibao/whispered';
import { T } from '@ekuaibao/i18n';
import { Icon, Tooltip } from 'antd';
import { Fetch } from '@ekuaibao/fetch';
import { DatalinkIF, ICategoryId, ISupplierAccountField } from '@ekuaibao/ekuaibao_types';
import { getSupplierAccount } from '../../setttlement-checkin-action';
import { showMessage } from '@ekuaibao/show-util';
const { entityFieldItemData } = app.require('@components/utils/fnEntityDataParse');
const EmptyBody = app.require<any>('@bills/elements/EmptyBody');
const EMPTY_ICON = app.require('@images/empty.svg');

interface Props {
  account?: any;
  accountId: string;
  accountName: string;
  categoryIds: ICategoryId[];
  onRefreshData: () => Promise<any>;
}

const DefaultDisplayFelidCount = 6;

const SupplierBillTemplate: React.FC<Props> = (props) => {
  const { account, accountId, accountName, categoryIds: defaultCategoryIds = [] } = props;
  const [categoryIds, setCategoryIds] = useState<ICategoryId[]>([]);
  const [entityInfo, setEntityInfo] = useState<ICategoryId>({} as ICategoryId);
  const [expandFields, setExpandFields] = useState<ISupplierAccountField[]>([]);
  const [expand, setExpand] = useState(false);
  const [allEntityList, setAllEntityList] = useState<DatalinkIF[]>([]);

  useEffect(() => {
    fetchAllEntityList();
  }, []);

  const fetchAllEntityList = async () => {
    const { items } = await app.invokeService('@third-party-manage:get:entity:all:list');
    setAllEntityList(items);
  };

  useEffect(() => {
    if (defaultCategoryIds?.length) {
      const [categoryId] = defaultCategoryIds;
      fnUpdateFields(categoryId?.fields || []);
      setEntityInfo(categoryId);
      setCategoryIds(defaultCategoryIds);
    }
  }, [defaultCategoryIds?.length]);

  const fnUpdateData = async () => {
    const { value = {} } = await getSupplierAccount(accountId);
    const { categoryIds = [] } = value;
    setCategoryIds(categoryIds);
    if (categoryIds?.length) {
      let currentCategory = categoryIds.find((category: ICategoryId) => category.id === entityInfo.id);
      if (!currentCategory) {
        currentCategory = categoryIds[0];
      }
      fnUpdateFields(currentCategory?.fields || []);
      setEntityInfo(currentCategory);
    } else {
      setEntityInfo({} as ICategoryId);
      setExpandFields([]);
    }
  };

  const fnUpdateFields = (updateFields?: ISupplierAccountField[], expand: boolean = false) => {
    const changeFields = updateFields?.length ? updateFields : entityInfo?.fields;
    if (expand) {
      setExpandFields(changeFields!.slice());
    } else {
      setExpandFields(changeFields!.slice(0, DefaultDisplayFelidCount));
    }
  };

  const handleExpandField = () => {
    setExpand(!expand);
    fnUpdateFields([], !expand);
  };

  const handleClickCategory = (category: ICategoryId) => {
    setExpand(false);
    setEntityInfo(Object.assign({}, category));
    fnUpdateFields(category.fields);
  };

  const handleExportExcel = () => {
    app
      .open('@bills:ImportDetailByExcel', {
        noticeMsg: i18n.get(
          '您可以在此页面上传商家给您开具的账单Excel，每一个sheet将会自动创建成一个商品品类，第一行内容将会被自动创建成字段,当商品品类重复时，将只会创建一个。',
        ),
        type: 'supplier_bill_import_excel',
        flag: { supplierAccountId: accountId, title: i18n.get('导入账单模板') },
        fn: () => {
          console.log('-----onCancel-----');
          fnUpdateData();
        },
      })
      .then((_) => {
        fnUpdateData();
      });
  };

  const handleAddCategory = async (entityInfo?: ICategoryId) => {
    let allEntities = allEntityList;
    if (!allEntityList?.length) {
      const { items } = await app.invokeService('@third-party-manage:get:entity:all:list', {});
      setAllEntityList(items);
      allEntities = items;
    }
    const res: any = await app.open('@settlement-checkin:EditSupplierAccountCategory', {
      title: entityInfo ? i18n.get('编辑账单模板字段') : i18n.get('新建账单模板字段'),
      entityInfo,
      allEntityList: allEntities,
      supplierAccountId: accountId,
      accountName,
      account,
    });
    if (account?.importMethod === 'api' && res?.action === 'save') {
      await app.open('@settlement-checkin:CategoryFieldsMap', {account, category: res?.value})
    }
    fnUpdateData();
  };

  const wrapperStyle = isWindows() && window.isWxWork ? { minHeight: 'calc(100%)', height: 'calc(100%)' } : {};

  return (
    <div style={wrapperStyle} className={styles['supplier-bill-template-wrapper']}>
      <div className={`header-wrapper ${isWindows() && window.isWxWork ? '' : 'flex-1'}`}>
        <div className="template-header">
          <div className="title">账单模板维护</div>
          <div className="right_box">
            <div className="import-excel" onClick={handleExportExcel}>
              {`${'导入Excel自动生成'} `}
              <Tooltip placement="topRight" title={i18n.get('通过上传账单的excel快速生成模板。')}>
                <Icon
                  className="enum-tooltip"
                  type="question-circle-o"
                  style={{ fontSize: 12, paddingTop: 4, color: '#1d2b3d' }}
                />
              </Tooltip>
            </div>
            <div
              className="export_excel"
              onClick={() => {
                if (categoryIds?.length > 0) {
                  const url = `/api/checking/v3/category/excel/export/$${accountId}?corpId=${Fetch.ekbCorpId}`;
                  app.emit('@vendor:download', url);
                } else {
                  showMessage.info(i18n.get('当前暂无账单模板'));
                }
              }}
            >
              导出账单模板
            </div>
          </div>
        </div>
        <CategoryComponent
          currentEntityInfo={entityInfo}
          categories={categoryIds}
          accountName={accountName}
          onClick={handleClickCategory}
          onNewCategory={handleAddCategory}
        />
      </div>
      <div className="field-content-wrapper">
        {categoryIds?.length === 0 && (
          <div className="category-empty-wrapper">
            <EmptyBody className={'category-empty'} icon={EMPTY_ICON} label={i18n.get('暂无数据')} />
          </div>
        )}
        <div className="content-wrapper">
          {expandFields.map((field) => {
            return <SupplierBillTemplateField key={field.name} field={field} allEntityList={allEntityList} />;
          })}
          <div className="field-action-wrapper">
            {entityInfo?.fields?.length > DefaultDisplayFelidCount && (
              <div className={'action'} onClick={() => handleExpandField()}>
                <T name={expand ? '收起' : '展开'} />
              </div>
            )}
            {entityInfo && entityInfo?.id && (
              <div className={'action ta-r'} onClick={() => handleAddCategory(entityInfo)}>
                <T name={'编辑'} />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupplierBillTemplate;

interface SupplierBillTemplateFieldProps {
  field: ISupplierAccountField;
  allEntityList: DatalinkIF[];
}

const SupplierBillTemplateField: React.FC<SupplierBillTemplateFieldProps> = (props) => {
  const { field, allEntityList } = props;
  let { title, subtitle, desc } = entityFieldItemData(field, allEntityList);
  if (field?.name?.endsWith('_name') || field?.name?.endsWith('_product_type')) {
    subtitle = i18n.get('产品类型')
  }
  return (
    <div className={styles['supplier-bill-template-field-wrapper']}>
      <div className="supplier-bill-template-field-title-wrapper">
        <div className="title">{title}</div>
        {subtitle && <div className="subtitle">{subtitle}</div>}
      </div>
      <div className="desc">{desc}</div>
    </div>
  );
};

const isWindows = () => {
  return /windows|win32/i.test(navigator.userAgent);
};
