/*
 * @Description: 供应商账户详情
 * @Creator: chencan<PERSON><PERSON>
 * @Date: 2021-05-28 10:59:52
 */
import React from 'react';
import style from './SupplierDetailView.module.less';
import { app, app as api } from '@ekuaibao/whispered';
import { Table, Button, Icon } from 'antd';
import { Popconfirm } from '@hose/eui'
import { injectKeel, KeelVM } from '@ekuaibao/keel';
import { get } from 'lodash';
import { getSupplierAccount, modifySetting, supplierAccountBillPersiodRefresh,supplierAccountConfig } from '../setttlement-checkin-action';
import noData from './../images/noData.svg';
import { array2object } from '@ekuaibao/helpers';
import { showMessage } from '@ekuaibao/show-util';
import ekb from '../images/icons/ekb.svg';
import { settlementType2Obj } from '../helpers/utils';
import {
  OutlinedTipsInfo,
} from '@hose/eui-icons'
import { Tooltip } from '@hose/eui'
const Money = app.require<any>('@elements/puppet/Money');
import SupplierBillTemplate from './view/SuppplierBillTemplate';

interface SupplierDetailViewProps {}
interface SupplierDetailViewState {
  accout: any;
  archive: any;
  isRefresh: boolean;
  importMethod: string;
  supplierArchiveType: string
  isOpen: boolean
}
export default class SupplierDetailView extends React.Component<SupplierDetailViewProps, SupplierDetailViewState> {
  @injectKeel()
  keel: KeelVM | undefined;
  state = {
    accout: {},
    archive: {},
    isRefresh: false,
    importMethod: '',
    supplierArchiveType: '',
    isOpen: false
  };

  get isHoseApi () {
    const { importMethod, supplierArchiveType,isOpen} = this.state
    return importMethod === 'api' && supplierArchiveType === 'HOSE_TRIP' && isOpen
  }

  private columns_title() {
    const columnsOrigin = [
      {
        title: '抬头名称',
        dataIndex: 'name',
        key: 'name',
        width: 300,
        render: (text) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '抬头税号',
        dataIndex: 'payerNo',
        key: 'payerNo',
        render: (text) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '法人实体字段',
        key: 'entityFieldLabel',
        dataIndex: 'entityFieldLabel',
        render: (text) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '操作',
        key: 'action',
        width: 160,
        render: (text, record, index) => (
          <div className="action_view">
            <div
              className="edit"
              onClick={() => {
                this.edit('title', record, index);
              }}
            >
              修改
            </div>
            <div
              className="delete"
              onClick={() => {
                this.delete('title', record, index);
              }}
            >
              删除
            </div>
          </div>
        ),
      },
    ];

    const columnsHose = [
      {
        title: '抬头名称',
        dataIndex: 'name',
        key: 'name',
        width: 300,
        render: (text) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '抬头税号',
        dataIndex: 'payerNo',
        key: 'payerNo',
        render: (text) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '法人实体字段',
        key: 'entityFieldLabel',
        dataIndex: 'entityFieldLabel',
        render: (text) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '法人实体',
        key: 'legalEntityName',
        dataIndex: 'legalEntityName',
        render: (text) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '邮寄地址',
        dataIndex: 'mailingAddress',
        key: 'mailingAddress',
        render: (text) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '接收人姓名',
        key: 'receiverName',
        dataIndex: 'receiverName',
        render: (text) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '接收人电话',
        dataIndex: 'tel',
        key: 'tel',
        render: (text) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '接收人邮箱',
        key: 'email',
        dataIndex: 'email',
        render: (text) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '操作',
        key: 'action',
        width: 160,
        render: (text, record, index) => (
          <div className="action_view">
            <div
              className="edit"
              onClick={() => {
                this.edit('title', record, index);
              }}
            >
              修改
            </div>
            <div
              className="delete"
              onClick={() => {
                this.delete('title', record, index);
              }}
            >
              删除
            </div>
          </div>
        ),
      },
    ];

    return this.isHoseApi ? columnsHose : columnsOrigin
  }
  private text2value(text) {
    if (text && text.length > 0) {
      const arr = text.map((it) => {
        switch (it) {
          case 'INVOICE_TAXI':
            return i18n.get('出租车发票');
          case 'INVOICE':
            return i18n.get('增值税发票');
          case 'VAT_INVOICE':
            return i18n.get('发票主体');
          case 'INVOICE_OTHER':
            return i18n.get('其他');
          case 'INVOICE_TRAIN':
            return i18n.get('铁路客票');
          case 'INVOICE_PASSENGER_CAR':
            return i18n.get('客运汽车发票');
          case 'INVOICE_AIRCRAFT':
            return i18n.get('航空运输电子客票行程单');
          case 'INVOICE_ROAD_TOLL':
            return i18n.get('过路费发票');
          case 'INVOICE_QUOTA':
            return i18n.get('定额发票');
          case 'INVOICE_MACHINE_PRINT':
            return i18n.get('机打发票');
          case 'DIGITAL_NORMAL':
            return i18n.get('增值税电子普通发票');
          case 'PAPER_SPECIAL':
            return i18n.get('增值税专用发票');
          case 'DIGITAL_SPECIAL':
            return i18n.get('增值税电子专用发票');
          default:
            return i18n.get('其他');
        }
      });
      return arr.join('、');
    } else {
      return i18n.get('-');
    }
  }

  private columns_type() {
    const columnsOrigin = [
      {
        title: '费用类型',
        dataIndex: 'name',
        key: 'name',
        render: (text, record) => {
          let feeTypeName = text
          if (!feeTypeName) {
            feeTypeName = i18n.get('-')
          } else if (record?.code) {
            feeTypeName = `${text}（${record?.code}）`
          }
          return <div>{feeTypeName}</div>
        },
      },
      {
        title: '发票类型',
        dataIndex: 'invoiceCategorys',
        key: 'invoiceCategorys',
        render: (text, record, index) => <div>{this.text2value(text)}</div>,
      },
      {
        title: '操作',
        key: 'action',
        width: 160,
        render: (text, record, index) => (
          <div className="action_view">
            <div
              className="edit"
              onClick={() => {
                this.edit('type', record, index);
              }}
            >
              {i18n.get("修改")}
            </div>
            <Popconfirm
              arrowPointAtCenter
              placement="topRight"
              title={i18n.get("删除该费用类型？")}
              content={<div style={{ width: '210px' }}>
                {i18n.get('删除该费用类型后，发票批次拆分时不会将符合该费用类型的明细打包在同一批次。是否删除？')}
              </div>}
              okText={i18n.get("删除")}
              okButtonProps={{ theme: "danger" }}
              onConfirm={() => this.delete('type', record, index)}
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}>
              <div className="delete">{i18n.get("删除")}</div>
            </Popconfirm>
          </div>
        ),
      },
    ];
    const columnsHose = [
      {
        title: '业务类型',
        dataIndex: 'businessName',
        key: 'businessName',
        width: '30%',
        render: (text) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '发票类型',
        dataIndex: 'invoiceCategorys',
        width: '30%',
        key: 'invoiceCategorys',
        render: (text, record, index) => <div>{text ? text : i18n.get('-')}</div>,
      },
      {
        title: '开票内容',
        dataIndex: 'invoicingContent',
        width: '30%',
        key: 'invoicingContent',
        render: (text, record, index) => <div>{text ? text : i18n.get('-')}</div>,
      },
    ]
    return this.isHoseApi ? columnsHose : columnsOrigin
  }

  private edit(type, record, index) {
    const { accout } = this.state;
    const modalName = this.isHoseApi ? 'HoseBillTitleDrawer' : 'BillTitleDrawer'
    if (type === 'title') {
      api
        .open(`@settlement-checkin:${modalName}`, {
          title: '编辑抬头',
          ...record
        })
        .then((data) => {
          const splitRules = get(accout, 'splitRules', []) || [];
          const invoiceTypeInfos = get(accout, 'invoiceTypeInfos', []) || [];
          const map = array2object(splitRules, 'corpPayerInfoId');
          if (record?.id === data?.corpPayerInfoId) {
            map[data?.corpPayerInfoId] = data;
          } else {
            delete map[record?.id];
            map[data?.corpPayerInfoId] = data;
          }
          modifySetting({
            id: get(accout, 'id'),
            splitRules: Object.values(map),
            invoiceTypeInfos: invoiceTypeInfos,
          })
            .then((res) => {
              this.getSupplierAccount();
            })
            .catch((err) => {
              showMessage.error(err?.message || err?.errMessage);
            });
        });
    } else {
      api
        .open('@settlement-checkin:BillTypeDrawer', {
          title: '编辑发票方式',
          feeTypeId: record?.id,
          invoiceCategorys: record?.invoiceCategorys,
        })
        .then((data) => {
          const splitRules = get(accout, 'splitRules', []) || [];
          const invoiceTypeInfos = get(accout, 'invoiceTypeInfos', []) || [];
          const map = array2object(invoiceTypeInfos, 'feeTypeId');
          if (record?.id === data?.feeTypeId) {
            map[data?.feeTypeId] = data;
          } else {
            delete map[record?.id];
            map[data?.feeTypeId] = data;
          }
          modifySetting({
            id: get(accout, 'id'),
            splitRules: splitRules,
            invoiceTypeInfos: Object.values(map),
          })
            .then((res) => {
              // 刷新数据
              this.getSupplierAccount();
            })
            .catch((err) => {
              showMessage.error(err?.message || err?.errMessage);
            });
        });
    }
  }
  private delete(type, record, index) {
    const { accout } = this.state;
    let splitRules = get(accout, 'splitRules', []) || [];
    let invoiceTypeInfos = get(accout, 'invoiceTypeInfos', []) || [];
    let map = {};
    if (type === 'title') {
      map = array2object(splitRules, 'corpPayerInfoId');
      delete map[record?.id];
      splitRules = Object.values(map);
    } else {
      map = array2object(invoiceTypeInfos, 'feeTypeId');
      delete map[record?.id];
      invoiceTypeInfos = Object.values(map);
    }
    modifySetting({
      id: get(accout, 'id'),
      splitRules: splitRules,
      invoiceTypeInfos: invoiceTypeInfos,
    })
      .then((res) => {
        this.getSupplierAccount();
      })
      .catch((err) => {
        showMessage.error(err?.message || err?.errMessage);
      });
  }
  componentDidMount() {
    this.getSupplierAccount();
    this.getSupplierAccountConfig()
  }
  private getSupplierAccountConfig() {
    supplierAccountConfig().then(res => {
      this.setState({isOpen: res?.value || false})
    })
  }
  private getSupplierAccount() {
    const id = this.props?.data?.id;
    return getSupplierAccount(id).then((res) => {
      const accout = res?.value || {};
      const archive = get(res, 'value.supplierArchiveObj', {}) || {};
      const importMethod = get(res, 'value.importMethod', '') || '';
      const supplierArchiveType = get(res, 'value.supplierArchiveType', '') || '';
      this.setState({ accout, archive,importMethod,supplierArchiveType });
      return res;
    });
  }
  private period2Name(period: string) {
    switch (period) {
      case 'WEEK':
        return i18n.get('按周');
      case 'BIWEEKLY':
        return i18n.get('按双周');
      case 'MONTH':
        return i18n.get('按月');
      case 'SEASON':
        return i18n.get('按季度');
      case 'HALFYEAR':
        return i18n.get('按半年');
      case 'YEAR':
        return i18n.get('按年');
      case 'IRREGULAR':
        return i18n.get('不定期');
      default:
        return i18n.get('-');
    }
  }
  private addTitle = () => {
    const { accout } = this.state;
    const modalName = this.isHoseApi ? 'HoseBillTitleDrawer' : 'BillTitleDrawer'
    api
      .open(`@settlement-checkin:${modalName}`, {
        title: '新增抬头',
        oldData: get(accout, 'corpPayerInfoObj', []) || [],
      })
      .then((data) => {
        const splitRules = get(accout, 'splitRules', []) || [];
        const invoiceTypeInfos = get(accout, 'invoiceTypeInfos', []) || [];
        const map = array2object(splitRules, 'corpPayerInfoId');
        map[data?.corpPayerInfoId] = data;
        modifySetting({
          id: get(accout, 'id'),
          splitRules: Object.values(map),
          invoiceTypeInfos: invoiceTypeInfos,
        })
          .then((res) => {
            this.getSupplierAccount();
          })
          .catch((err) => {
            showMessage.error(err?.message || err?.errMessage);
          });
      });
  };
  private addType = () => {
    const { accout } = this.state;
    api
      .open('@settlement-checkin:BillTypeDrawer', {
        title: '新增发票方式',
        oldData: get(accout, 'feeTypeObj', []) || [],
      })
      .then((data) => {
        const splitRules = get(accout, 'splitRules', []) || [];
        const invoiceTypeInfos = get(accout, 'invoiceTypeInfos', []) || [];
        const map = array2object(invoiceTypeInfos, 'feeTypeId');
        map[data?.feeTypeId] = data;
        modifySetting({
          id: get(accout, 'id'),
          splitRules: splitRules,
          invoiceTypeInfos: Object.values(map),
        })
          .then((res) => {
            // 刷新数据
            this.getSupplierAccount();
          })
          .catch((err) => {
            showMessage.error(err?.message || err?.errMessage);
          });
      });
  };

  billPeriodDisplay = (): string => {
    const { accout } = this.state;
    if (!accout) {
      return '-';
    }
    if (accout.billPeriod?.type === 'NATURAL_MONTH') {
      return `自然月/当月1日-当月最后一天`;
    }
    if (accout.billPeriod?.type === 'ACROSS_MONTH') {
      return `跨月/当月${accout.billPeriod?.startTime}日-次月${accout.billPeriod?.endTime}日`;
    }
    return '';
  };

  handleRefreshBillPeriod = () => {
    const { accout, isRefresh } = this.state;
    if (!isRefresh) {
      this.setState({ isRefresh: true });
      supplierAccountBillPersiodRefresh({ id: accout.id })
        .then((res) => {
          const billPeriod = res.value?.billPeriod;
          if (billPeriod) {
            this.setState({ accout: { ...accout, billPeriod } });
          }
          this.setState({ isRefresh: false });
        })
        .catch((err) => {
          this.setState({ isRefresh: false });
        });
    }
  };

  render() {
    const { accout, archive, isRefresh } = this.state;
    const paymentAccountObj = get(archive, 'paymentAccountObj', []) || [];
    return (
      <div className={style['supplier_detail_view']}>
        <div className="header">
          <div className="title">{this.props?.title}</div>
        </div>
        <div className="container">
          <div className={`account_info_view ${get(accout, 'active') ? '' : 'disable_bgc'}`}>
            <img className="icon" src={ekb} />
            <div className="ml24">
              <Money isShowThousandsSeparator className="money" value={accout?.recordedAmount} />
              <div className="more_view">
                <div className="danwei">已出账单/元</div>
              </div>
            </div>
            <div className="company">{get(accout, 'name')}</div>
          </div>
          <div className="company_info_view">
            <div className="">
              <div className="row">
                <div className="label" style={{ width: 90 }}>
                  供应商名称
                </div>
                <Tooltip placement="topLeft" title={get(archive, 'name')}>
                  <div className="content" style={{ width: 220 }}>
                    {get(archive, 'name')}
                  </div>
                </Tooltip>
                <div className="label" style={{ width: 70, marginLeft: 16 }}>
                  账户类型
                </div>
                <div className="content" style={{ width: 80 }}>
                  {get(settlementType2Obj(get(accout, 'settlementType')), 'name')}
                </div>
                <div className="label" style={{ width: 70, marginLeft: 16 }}>
                  账户状态
                </div>
                <div className={`content ${get(accout, 'active') ? '' : 'disable'}`} style={{ width: 70 }}>
                  {get(accout, 'active') ? i18n.get('已启用') : i18n.get('已停用')}
                </div>
                <div className="label" style={{ width: 70, marginLeft: 16 }}>
                  是否结算
                </div>
                <div className="content" style={{ width: 70 }}>
                  {get(accout, 'isSettlement') ? i18n.get('是') : i18n.get('否')}
                </div>
              </div>
              <div className="row">
                <div className="label" style={{ width: 90 }}>
                  {get(accout, 'settlementType') === 'CREDIT' ? i18n.get('剩余可用额度') : i18n.get('剩余可用余额')}
                </div>
                <div className="content" style={{ width: 220 }}>
                  <Money value={accout?.balanceAmount} />
                </div>
                <div className="label" style={{ width: 70, marginLeft: 16 }}>
                  账单日
                </div>
                <div className="content" style={{ width: 80 }}>
                  {get(accout, 'billingDay') ? `每月 ${get(accout, 'billingDay')} 日` : i18n.get('-')}
                </div>
                <div className="label" style={{ width: 70, marginLeft: 16 }}>
                  结算周期
                </div>
                <div className="content" style={{ width: 70 }}>
                  {this.period2Name(get(accout, 'period'))}
                </div>
                <div className="label" style={{ width: 70, marginLeft: 16 }}>
                  是否对账
                </div>
                <div className="content" style={{ width: 70 }}>
                  {get(accout, 'isReconciliation') ? i18n.get('是') : i18n.get('否')}
                </div>
              </div>
              <div className="row">
                {get(accout, 'settlementType') === 'CREDIT' ? (
                  <>
                    <div className="label" style={{ width: 90 }}>
                      授信额度
                    </div>
                    <div className="content" style={{ width: 220 }}>
                      <Money value={accout?.creditAmount} />
                    </div>
                  </>
                ) : (
                  <>
                    <div className="label" style={{ width: 90 }}>
                      还款日
                    </div>
                    <div className="content" style={{ width: 220 }}>
                      {get(accout, 'repaymentDay') ? `每月 ${get(accout, 'repaymentDay')} 日` : i18n.get('-')}
                    </div>
                  </>
                )}
                {get(accout, 'settlementType') === 'CREDIT' ? (
                  <>
                    <div className="label" style={{ width: 70, marginLeft: 16 }}>
                      还款日
                    </div>
                    <div className="content" style={{ width: 80 }}>
                      {get(accout, 'repaymentDay') ? `每月 ${get(accout, 'repaymentDay')} 日` : i18n.get('-')}
                    </div>
                  </>
                ) : null}
                <>
                  <div className="label" style={{ width: 70, marginLeft: 16 }}>
                    企业对账
                  </div>
                  <div className="content">
                    {this.billPeriodDisplay()}
                    {accout?.supplierArchiveType === 'HOSE_TRIP' && (
                      <span className="refresh" onClick={this.handleRefreshBillPeriod}>
                        {i18n.get('刷新')}
                        {isRefresh && <Icon style={{ marginLeft: 4 }} type="loading" />}
                      </span>
                    )}
                  </div>
                </>
              </div>
            </div>
          </div>
          <div className="beneficiary_view">
            <div className="title">收款信息</div>
            <div className="items">
              {paymentAccountObj.length > 0 ? (
                paymentAccountObj.map((it, i) => {
                  return (
                    <div key={it?.id} className="item">
                      {it.id === get(archive, 'defaultPaymentAccountId') ? (
                        <div className="default_view">默认</div>
                      ) : null}
                      <div className="icon">
                        <img className="icon" src={it?.icon} />
                      </div>
                      <div className="info_view">
                        <div className="bank">{get(it, 'bank', '')}</div>
                        <div className="bank_number">{get(it, 'accountNo', '')}</div>
                        <div className="company">{get(it, 'accountName', '')}</div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="no_data_view">
                  <img className="icon" src={noData} />
                  <div>暂无收款信息</div>
                </div>
              )}
            </div>
          </div>
          <div className="supplier_bill_setting_title">供应商开票设置</div>
          <div className="supplier_bill_setting_view">
            <div className="title_view" style={{ marginBottom: 11 }}>
              <div className="title">{this.isHoseApi ? '发票抬头以及接收信息维护' : '发票抬头维护'}</div>
              <Button type="primary" size="large" onClick={this.addTitle}>
                新增抬头
              </Button>
            </div>
            <Table
              className="table"
              columns={this.columns_title()}
              pagination={false}
              dataSource={get(accout, 'corpPayerInfoObj', []) || []}
            />
            <div className="title_view" style={{ marginBottom: 11, marginTop: 48 }}>
              <div className="title">
                费用类型维护
                <Tooltip title={i18n.get('用于统一开票批次拆分')}>
                  <OutlinedTipsInfo/>
                </Tooltip>
              </div>
              { !this.isHoseApi &&
              <Button type="primary" size="large" onClick={this.addType}>
                新增发票方式
              </Button> }

            </div>
            <Table
              className="table"
              columns={this.columns_type()}
              pagination={false}
              dataSource={get(accout, 'feeTypeObj', []) || []}
            />
          </div>
          <div className="supplier_bill_setting_title">账单模板设置</div>
          <SupplierBillTemplate
            account={accout}
            accountId={(accout as any)?.id}
            categoryIds={(accout as any)?.categoryIds}
            accountName={(accout as any)?.name}
            onRefreshData={() => {
              return this.getSupplierAccount();
            }}
          />
        </div>
      </div>
    );
  }
}
