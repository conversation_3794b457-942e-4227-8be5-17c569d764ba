@import '~@ekuaibao/web-theme-variables/styles/default.less';
@import '~@ekuaibao/web-theme-variables/styles/colors.less';
@import '~@ekuaibao/eui-styles/less/token.less';

.supplier_manager_view {
  width: 100%;
  background-color: rgba(247, 247, 247, 1);
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 1330px;

  :global {
    .header {
      background: white;
      height: 52px;
      flex-shrink: 0;
      width: 100%;
      display: flex;
      padding: 0 24px;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 20px;
        font-weight: 500;
        max-width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .right_view {
        .ico {
          margin-right: 8px;
        }
      }
    }
    .query_view {
      background-color: white;
      padding: @space-6 @space-7 0;
      min-width: 1330px;

      .form_item {
        height: 32px;
        margin-bottom: @space-6;
        margin-right: @space-8;
      }
    }

    .lists_view {
      padding-right: 24px;
      flex-wrap: wrap;
      display: flex;
      padding-bottom: 24px;

      .disable_bgc {
        background: #b3b3b3;
      }
      .item_view {
        width: 410px;
        height: 200px;
        background: linear-gradient(146deg, #5c8dff 13%, #7b6eff 84%, #4143f3 100%);
        border-radius: 6px;
        margin-left: 24px;
        margin-top: 24px;
        color: white;
        display: flex;
        flex-direction: column;
        position: relative;
        transition: all 0.3s ease 0s;

        .icon_view {
          position: absolute;
          right: 0;
          bottom: 0;
        }

        .top_view {
          margin-left: 16px;
          margin-right: 8px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 48px;
          position: relative;

          .corporation {
            font-size: 16px;
            max-width: 290px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .status_view {
            cursor: pointer;
            display: flex;
            align-items: center;
            padding: 6px 12px;
            background: transparent;
            border-radius: 4px;
            &:hover {
              background: rgba(0, 0, 0, 0.1);
            }

            img {
              margin-top: 1px;
            }

            .status {
              font-size: 14px;
              margin-left: 7px;
            }
          }
          .settlement_type_view {
            display: flex;
            justify-content: center;
            color: #ffffff;
            opacity: 1;
            position: absolute;
            bottom: -8px;

            img {
              margin-right: 4px;
            }
          }
        }

        .content_view {
          cursor: pointer;
          display: flex;
          flex-direction: column;
          flex: 1;
          justify-content: center;
          margin-left: 24px;

          .box {
            display: flex;
            align-items: flex-end;

            .available {
              font-size: 14px;
            }

            .left_b {
              .item_v {
                display: flex;
                align-items: center;

                .money {
                  font-size: 40px;
                  font-family: DINAlternate, DINAlternate-Bold, sans-serif;
                  font-weight: 700;
                  color: #ffffff;
                  line-height: 47px;
                }

                .btn {
                  width: 68px;
                  height: 30px;
                  background: #ffffff;
                  border-radius: 3px;
                  font-size: 14px;
                  color: #31b7cf;
                  text-align: center;
                  line-height: 30px;
                  margin-left: 12px;
                  cursor: pointer;
                  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
                }
              }
            }

            .right_b {
              margin-left: 58px;

              .money {
                font-size: 26px;
                font-family: DINAlternate, DINAlternate-Bold, sans-serif;
                font-weight: 700;
                line-height: 30px;
              }
            }
          }
        }

        .bottom_view {
          font-size: 12px;
          margin: 0 8px 8px 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .supplier_name {
            opacity: 0.6;
            color: white;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .edit {
            cursor: pointer;
            opacity: 0.92;
            color: #ffffff;
            padding: 7px 13px;
            background: transparent;
            border-radius: 4px;
            &:hover {
              background: rgba(0, 0, 0, 0.1);
            }
          }
        }
      }
      .item_view:hover {
        box-shadow: 0px 3px 14px 0px rgba(0, 0, 0, 0.03), 0px 4px 8px 0px rgba(0, 0, 0, 0.1),
          0px 2px 8px -3px rgba(0, 0, 0, 0.08);
      }

      .ekb {
        background: linear-gradient(146deg, #31b7cf 13%, #31b7cf 44%, #33bcd4 84%, #2da5bb 100%);
      }

      .alsl {
        background: linear-gradient(146deg, #02a3ff 13%, #009df7 44%, #1aacff 84%, #126eff 100%);
      }

      .didi {
        background: linear-gradient(146deg, #ff905c 13%, #fd864e 44%, #ffa073 84%, #ff7e41 100%);
      }

      .can {
        background: linear-gradient(146deg, #bf4141 13%, #c12d2d 44%, #e95555 84%, #b03131 100%);
      }

      .jtb {
        background: linear-gradient(146deg, #6e5cff 13%, #5e4bfb 44%, #7e6eff 84%, #5441f3 100%);
      }

      .yql {
        background: linear-gradient(146deg, #d02d2d 13%, #e34040 44%, #f26b6b 84%, #c42626 100%);
      }

      .jd {
        background: linear-gradient(146deg, #de2e28 13%, #ff554f 89%, #c92620 100%);
      }

      .elm {
        background: linear-gradient(146deg, #04b6fd 13%, #04b6fd 44%, #55cfff 84%, #04b6fd 100%);
      }

      .item_new {
        width: 410px;
        height: 200px;
        opacity: 1;
        background: #eaeaea;
        border-radius: 6px;
        margin-left: 24px;
        margin-top: 24px;
        color: #999999;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .icon {
          width: 50px;
          height: 50px;
        }

        .new {
          margin-top: 11px;
          font-size: 14px;
        }
      }
    }
  }
}
